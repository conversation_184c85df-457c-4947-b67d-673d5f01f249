"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@peculiar";
exports.ids = ["vendor-chunks/@peculiar"];
exports.modules = {

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/convert.js":
/*!********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/convert.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnConvert: () => (/* binding */ AsnConvert)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/./node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parser */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/parser.js\");\n/* harmony import */ var _serializer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./serializer */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\");\n\n\n\n\nclass AsnConvert {\n    static serialize(obj) {\n        return _serializer__WEBPACK_IMPORTED_MODULE_3__.AsnSerializer.serialize(obj);\n    }\n    static parse(data, target) {\n        return _parser__WEBPACK_IMPORTED_MODULE_2__.AsnParser.parse(data, target);\n    }\n    static toString(data) {\n        const buf = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(data)\n            ? pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(data)\n            : AsnConvert.serialize(data);\n        const asn = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(buf);\n        if (asn.offset === -1) {\n            throw new Error(`Cannot decode ASN.1 data. ${asn.result.error}`);\n        }\n        return asn.result.toString();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9jb252ZXJ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWlDO0FBQ2lCO0FBQ2I7QUFDUTtBQUN0QztBQUNQO0FBQ0EsZUFBZSxzREFBYTtBQUM1QjtBQUNBO0FBQ0EsZUFBZSw4Q0FBUztBQUN4QjtBQUNBO0FBQ0Esb0JBQW9CLDREQUFxQjtBQUN6QyxjQUFjLDREQUFxQjtBQUNuQztBQUNBLG9CQUFvQiwyQ0FBYztBQUNsQztBQUNBLHlEQUF5RCxpQkFBaUI7QUFDMUU7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcGVjdWxpYXIvYXNuMS1zY2hlbWEvYnVpbGQvZXMyMDE1L2NvbnZlcnQuanM/MTcyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBhc24xanMgZnJvbSBcImFzbjFqc1wiO1xuaW1wb3J0IHsgQnVmZmVyU291cmNlQ29udmVydGVyIH0gZnJvbSBcInB2dHN1dGlsc1wiO1xuaW1wb3J0IHsgQXNuUGFyc2VyIH0gZnJvbSBcIi4vcGFyc2VyXCI7XG5pbXBvcnQgeyBBc25TZXJpYWxpemVyIH0gZnJvbSBcIi4vc2VyaWFsaXplclwiO1xuZXhwb3J0IGNsYXNzIEFzbkNvbnZlcnQge1xuICAgIHN0YXRpYyBzZXJpYWxpemUob2JqKSB7XG4gICAgICAgIHJldHVybiBBc25TZXJpYWxpemVyLnNlcmlhbGl6ZShvYmopO1xuICAgIH1cbiAgICBzdGF0aWMgcGFyc2UoZGF0YSwgdGFyZ2V0KSB7XG4gICAgICAgIHJldHVybiBBc25QYXJzZXIucGFyc2UoZGF0YSwgdGFyZ2V0KTtcbiAgICB9XG4gICAgc3RhdGljIHRvU3RyaW5nKGRhdGEpIHtcbiAgICAgICAgY29uc3QgYnVmID0gQnVmZmVyU291cmNlQ29udmVydGVyLmlzQnVmZmVyU291cmNlKGRhdGEpXG4gICAgICAgICAgICA/IEJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b0FycmF5QnVmZmVyKGRhdGEpXG4gICAgICAgICAgICA6IEFzbkNvbnZlcnQuc2VyaWFsaXplKGRhdGEpO1xuICAgICAgICBjb25zdCBhc24gPSBhc24xanMuZnJvbUJFUihidWYpO1xuICAgICAgICBpZiAoYXNuLm9mZnNldCA9PT0gLTEpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgQ2Fubm90IGRlY29kZSBBU04uMSBkYXRhLiAke2Fzbi5yZXN1bHQuZXJyb3J9YCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGFzbi5yZXN1bHQudG9TdHJpbmcoKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/convert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/converters.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnAnyConverter: () => (/* binding */ AsnAnyConverter),\n/* harmony export */   AsnBitStringConverter: () => (/* binding */ AsnBitStringConverter),\n/* harmony export */   AsnBmpStringConverter: () => (/* binding */ AsnBmpStringConverter),\n/* harmony export */   AsnBooleanConverter: () => (/* binding */ AsnBooleanConverter),\n/* harmony export */   AsnCharacterStringConverter: () => (/* binding */ AsnCharacterStringConverter),\n/* harmony export */   AsnConstructedOctetStringConverter: () => (/* binding */ AsnConstructedOctetStringConverter),\n/* harmony export */   AsnEnumeratedConverter: () => (/* binding */ AsnEnumeratedConverter),\n/* harmony export */   AsnGeneralStringConverter: () => (/* binding */ AsnGeneralStringConverter),\n/* harmony export */   AsnGeneralizedTimeConverter: () => (/* binding */ AsnGeneralizedTimeConverter),\n/* harmony export */   AsnGraphicStringConverter: () => (/* binding */ AsnGraphicStringConverter),\n/* harmony export */   AsnIA5StringConverter: () => (/* binding */ AsnIA5StringConverter),\n/* harmony export */   AsnIntegerArrayBufferConverter: () => (/* binding */ AsnIntegerArrayBufferConverter),\n/* harmony export */   AsnIntegerBigIntConverter: () => (/* binding */ AsnIntegerBigIntConverter),\n/* harmony export */   AsnIntegerConverter: () => (/* binding */ AsnIntegerConverter),\n/* harmony export */   AsnNullConverter: () => (/* binding */ AsnNullConverter),\n/* harmony export */   AsnNumericStringConverter: () => (/* binding */ AsnNumericStringConverter),\n/* harmony export */   AsnObjectIdentifierConverter: () => (/* binding */ AsnObjectIdentifierConverter),\n/* harmony export */   AsnOctetStringConverter: () => (/* binding */ AsnOctetStringConverter),\n/* harmony export */   AsnPrintableStringConverter: () => (/* binding */ AsnPrintableStringConverter),\n/* harmony export */   AsnTeletexStringConverter: () => (/* binding */ AsnTeletexStringConverter),\n/* harmony export */   AsnUTCTimeConverter: () => (/* binding */ AsnUTCTimeConverter),\n/* harmony export */   AsnUniversalStringConverter: () => (/* binding */ AsnUniversalStringConverter),\n/* harmony export */   AsnUtf8StringConverter: () => (/* binding */ AsnUtf8StringConverter),\n/* harmony export */   AsnVideotexStringConverter: () => (/* binding */ AsnVideotexStringConverter),\n/* harmony export */   AsnVisibleStringConverter: () => (/* binding */ AsnVisibleStringConverter),\n/* harmony export */   defaultConverter: () => (/* binding */ defaultConverter)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _types_index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types/index */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\");\n\n\n\nconst AsnAnyConverter = {\n    fromASN: (value) => value instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.Null ? null : value.valueBeforeDecodeView,\n    toASN: (value) => {\n        if (value === null) {\n            return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Null();\n        }\n        const schema = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(value);\n        if (schema.result.error) {\n            throw new Error(schema.result.error);\n        }\n        return schema.result;\n    },\n};\nconst AsnIntegerConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView.byteLength >= 4\n        ? value.valueBlock.toString()\n        : value.valueBlock.valueDec,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer({ value: +value }),\n};\nconst AsnEnumeratedConverter = {\n    fromASN: (value) => value.valueBlock.valueDec,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Enumerated({ value }),\n};\nconst AsnIntegerArrayBufferConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer({ valueHex: value }),\n};\nconst AsnIntegerBigIntConverter = {\n    fromASN: (value) => value.toBigInt(),\n    toASN: (value) => asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer.fromBigInt(value),\n};\nconst AsnBitStringConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ valueHex: value }),\n};\nconst AsnObjectIdentifierConverter = {\n    fromASN: (value) => value.valueBlock.toString(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.ObjectIdentifier({ value }),\n};\nconst AsnBooleanConverter = {\n    fromASN: (value) => value.valueBlock.value,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Boolean({ value }),\n};\nconst AsnOctetStringConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ valueHex: value }),\n};\nconst AsnConstructedOctetStringConverter = {\n    fromASN: (value) => new _types_index__WEBPACK_IMPORTED_MODULE_2__.OctetString(value.getValue()),\n    toASN: (value) => value.toASN(),\n};\nfunction createStringConverter(Asn1Type) {\n    return {\n        fromASN: (value) => value.valueBlock.value,\n        toASN: (value) => new Asn1Type({ value }),\n    };\n}\nconst AsnUtf8StringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.Utf8String);\nconst AsnBmpStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.BmpString);\nconst AsnUniversalStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.UniversalString);\nconst AsnNumericStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.NumericString);\nconst AsnPrintableStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.PrintableString);\nconst AsnTeletexStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.TeletexString);\nconst AsnVideotexStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.VideotexString);\nconst AsnIA5StringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.IA5String);\nconst AsnGraphicStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.GraphicString);\nconst AsnVisibleStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.VisibleString);\nconst AsnGeneralStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.GeneralString);\nconst AsnCharacterStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.CharacterString);\nconst AsnUTCTimeConverter = {\n    fromASN: (value) => value.toDate(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.UTCTime({ valueDate: value }),\n};\nconst AsnGeneralizedTimeConverter = {\n    fromASN: (value) => value.toDate(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.GeneralizedTime({ valueDate: value }),\n};\nconst AsnNullConverter = {\n    fromASN: () => null,\n    toASN: () => {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Null();\n    },\n};\nfunction defaultConverter(type) {\n    switch (type) {\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any:\n            return AsnAnyConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString:\n            return AsnBitStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BmpString:\n            return AsnBmpStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Boolean:\n            return AsnBooleanConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.CharacterString:\n            return AsnCharacterStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Enumerated:\n            return AsnEnumeratedConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GeneralString:\n            return AsnGeneralStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GeneralizedTime:\n            return AsnGeneralizedTimeConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GraphicString:\n            return AsnGraphicStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.IA5String:\n            return AsnIA5StringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer:\n            return AsnIntegerConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Null:\n            return AsnNullConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.NumericString:\n            return AsnNumericStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier:\n            return AsnObjectIdentifierConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString:\n            return AsnOctetStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.PrintableString:\n            return AsnPrintableStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.TeletexString:\n            return AsnTeletexStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.UTCTime:\n            return AsnUTCTimeConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.UniversalString:\n            return AsnUniversalStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Utf8String:\n            return AsnUtf8StringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.VideotexString:\n            return AsnVideotexStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.VisibleString:\n            return AsnVisibleStringConverter;\n        default:\n            return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/decorators.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/decorators.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnChoiceType: () => (/* binding */ AsnChoiceType),\n/* harmony export */   AsnProp: () => (/* binding */ AsnProp),\n/* harmony export */   AsnSequenceType: () => (/* binding */ AsnSequenceType),\n/* harmony export */   AsnSetType: () => (/* binding */ AsnSetType),\n/* harmony export */   AsnType: () => (/* binding */ AsnType)\n/* harmony export */ });\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./converters */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./storage */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\nconst AsnType = (options) => (target) => {\n    let schema;\n    if (!_storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.has(target)) {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.createDefault(target);\n        _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.set(target, schema);\n    }\n    else {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.get(target);\n    }\n    Object.assign(schema, options);\n};\nconst AsnChoiceType = () => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice });\nconst AsnSetType = (options) => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Set, ...options });\nconst AsnSequenceType = (options) => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence, ...options });\nconst AsnProp = (options) => (target, propertyKey) => {\n    let schema;\n    if (!_storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.has(target.constructor)) {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.createDefault(target.constructor);\n        _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.set(target.constructor, schema);\n    }\n    else {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.get(target.constructor);\n    }\n    const copyOptions = Object.assign({}, options);\n    if (typeof copyOptions.type === \"number\" && !copyOptions.converter) {\n        const defaultConverter = _converters__WEBPACK_IMPORTED_MODULE_0__.defaultConverter(options.type);\n        if (!defaultConverter) {\n            throw new Error(`Cannot get default converter for property '${propertyKey}' of ${target.constructor.name}`);\n        }\n        copyOptions.converter = defaultConverter;\n    }\n    schema.items[propertyKey] = copyOptions;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/decorators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js":
/*!******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/enums.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnPropTypes: () => (/* binding */ AsnPropTypes),\n/* harmony export */   AsnTypeTypes: () => (/* binding */ AsnTypeTypes)\n/* harmony export */ });\nvar AsnTypeTypes;\n(function (AsnTypeTypes) {\n    AsnTypeTypes[AsnTypeTypes[\"Sequence\"] = 0] = \"Sequence\";\n    AsnTypeTypes[AsnTypeTypes[\"Set\"] = 1] = \"Set\";\n    AsnTypeTypes[AsnTypeTypes[\"Choice\"] = 2] = \"Choice\";\n})(AsnTypeTypes || (AsnTypeTypes = {}));\nvar AsnPropTypes;\n(function (AsnPropTypes) {\n    AsnPropTypes[AsnPropTypes[\"Any\"] = 1] = \"Any\";\n    AsnPropTypes[AsnPropTypes[\"Boolean\"] = 2] = \"Boolean\";\n    AsnPropTypes[AsnPropTypes[\"OctetString\"] = 3] = \"OctetString\";\n    AsnPropTypes[AsnPropTypes[\"BitString\"] = 4] = \"BitString\";\n    AsnPropTypes[AsnPropTypes[\"Integer\"] = 5] = \"Integer\";\n    AsnPropTypes[AsnPropTypes[\"Enumerated\"] = 6] = \"Enumerated\";\n    AsnPropTypes[AsnPropTypes[\"ObjectIdentifier\"] = 7] = \"ObjectIdentifier\";\n    AsnPropTypes[AsnPropTypes[\"Utf8String\"] = 8] = \"Utf8String\";\n    AsnPropTypes[AsnPropTypes[\"BmpString\"] = 9] = \"BmpString\";\n    AsnPropTypes[AsnPropTypes[\"UniversalString\"] = 10] = \"UniversalString\";\n    AsnPropTypes[AsnPropTypes[\"NumericString\"] = 11] = \"NumericString\";\n    AsnPropTypes[AsnPropTypes[\"PrintableString\"] = 12] = \"PrintableString\";\n    AsnPropTypes[AsnPropTypes[\"TeletexString\"] = 13] = \"TeletexString\";\n    AsnPropTypes[AsnPropTypes[\"VideotexString\"] = 14] = \"VideotexString\";\n    AsnPropTypes[AsnPropTypes[\"IA5String\"] = 15] = \"IA5String\";\n    AsnPropTypes[AsnPropTypes[\"GraphicString\"] = 16] = \"GraphicString\";\n    AsnPropTypes[AsnPropTypes[\"VisibleString\"] = 17] = \"VisibleString\";\n    AsnPropTypes[AsnPropTypes[\"GeneralString\"] = 18] = \"GeneralString\";\n    AsnPropTypes[AsnPropTypes[\"CharacterString\"] = 19] = \"CharacterString\";\n    AsnPropTypes[AsnPropTypes[\"UTCTime\"] = 20] = \"UTCTime\";\n    AsnPropTypes[AsnPropTypes[\"GeneralizedTime\"] = 21] = \"GeneralizedTime\";\n    AsnPropTypes[AsnPropTypes[\"DATE\"] = 22] = \"DATE\";\n    AsnPropTypes[AsnPropTypes[\"TimeOfDay\"] = 23] = \"TimeOfDay\";\n    AsnPropTypes[AsnPropTypes[\"DateTime\"] = 24] = \"DateTime\";\n    AsnPropTypes[AsnPropTypes[\"Duration\"] = 25] = \"Duration\";\n    AsnPropTypes[AsnPropTypes[\"TIME\"] = 26] = \"TIME\";\n    AsnPropTypes[AsnPropTypes[\"Null\"] = 27] = \"Null\";\n})(AsnPropTypes || (AsnPropTypes = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaValidationError: () => (/* reexport safe */ _schema_validation__WEBPACK_IMPORTED_MODULE_0__.AsnSchemaValidationError)\n/* harmony export */ });\n/* harmony import */ var _schema_validation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema_validation */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9lcnJvcnMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcGVjdWxpYXIvYXNuMS1zY2hlbWEvYnVpbGQvZXMyMDE1L2Vycm9ycy9pbmRleC5qcz9kZjU2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL3NjaGVtYV92YWxpZGF0aW9uXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaValidationError: () => (/* binding */ AsnSchemaValidationError)\n/* harmony export */ });\nclass AsnSchemaValidationError extends Error {\n    constructor() {\n        super(...arguments);\n        this.schemas = [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9lcnJvcnMvc2NoZW1hX3ZhbGlkYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvZXJyb3JzL3NjaGVtYV92YWxpZGF0aW9uLmpzP2Y3NTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIEFzblNjaGVtYVZhbGlkYXRpb25FcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5zY2hlbWFzID0gW107XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/helper.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArrayEqual: () => (/* binding */ isArrayEqual),\n/* harmony export */   isConvertible: () => (/* binding */ isConvertible),\n/* harmony export */   isTypeOfArray: () => (/* binding */ isTypeOfArray)\n/* harmony export */ });\nfunction isConvertible(target) {\n    if (typeof target === \"function\" && target.prototype) {\n        if (target.prototype.toASN && target.prototype.fromASN) {\n            return true;\n        }\n        else {\n            return isConvertible(target.prototype);\n        }\n    }\n    else {\n        return !!(target && typeof target === \"object\" && \"toASN\" in target && \"fromASN\" in target);\n    }\n}\nfunction isTypeOfArray(target) {\n    var _a;\n    if (target) {\n        const proto = Object.getPrototypeOf(target);\n        if (((_a = proto === null || proto === void 0 ? void 0 : proto.prototype) === null || _a === void 0 ? void 0 : _a.constructor) === Array) {\n            return true;\n        }\n        return isTypeOfArray(proto);\n    }\n    return false;\n}\nfunction isArrayEqual(bytes1, bytes2) {\n    if (!(bytes1 && bytes2)) {\n        return false;\n    }\n    if (bytes1.byteLength !== bytes2.byteLength) {\n        return false;\n    }\n    const b1 = new Uint8Array(bytes1);\n    const b2 = new Uint8Array(bytes2);\n    for (let i = 0; i < bytes1.byteLength; i++) {\n        if (b1[i] !== b2[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9oZWxwZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHVCQUF1QjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcGVjdWxpYXIvYXNuMS1zY2hlbWEvYnVpbGQvZXMyMDE1L2hlbHBlci5qcz9iZThiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBpc0NvbnZlcnRpYmxlKHRhcmdldCkge1xuICAgIGlmICh0eXBlb2YgdGFyZ2V0ID09PSBcImZ1bmN0aW9uXCIgJiYgdGFyZ2V0LnByb3RvdHlwZSkge1xuICAgICAgICBpZiAodGFyZ2V0LnByb3RvdHlwZS50b0FTTiAmJiB0YXJnZXQucHJvdG90eXBlLmZyb21BU04pIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIGlzQ29udmVydGlibGUodGFyZ2V0LnByb3RvdHlwZSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiAhISh0YXJnZXQgJiYgdHlwZW9mIHRhcmdldCA9PT0gXCJvYmplY3RcIiAmJiBcInRvQVNOXCIgaW4gdGFyZ2V0ICYmIFwiZnJvbUFTTlwiIGluIHRhcmdldCk7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIGlzVHlwZU9mQXJyYXkodGFyZ2V0KSB7XG4gICAgdmFyIF9hO1xuICAgIGlmICh0YXJnZXQpIHtcbiAgICAgICAgY29uc3QgcHJvdG8gPSBPYmplY3QuZ2V0UHJvdG90eXBlT2YodGFyZ2V0KTtcbiAgICAgICAgaWYgKCgoX2EgPSBwcm90byA9PT0gbnVsbCB8fCBwcm90byA9PT0gdm9pZCAwID8gdm9pZCAwIDogcHJvdG8ucHJvdG90eXBlKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY29uc3RydWN0b3IpID09PSBBcnJheSkge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGlzVHlwZU9mQXJyYXkocHJvdG8pO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNBcnJheUVxdWFsKGJ5dGVzMSwgYnl0ZXMyKSB7XG4gICAgaWYgKCEoYnl0ZXMxICYmIGJ5dGVzMikpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoYnl0ZXMxLmJ5dGVMZW5ndGggIT09IGJ5dGVzMi5ieXRlTGVuZ3RoKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3QgYjEgPSBuZXcgVWludDhBcnJheShieXRlczEpO1xuICAgIGNvbnN0IGIyID0gbmV3IFVpbnQ4QXJyYXkoYnl0ZXMyKTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGJ5dGVzMS5ieXRlTGVuZ3RoOyBpKyspIHtcbiAgICAgICAgaWYgKGIxW2ldICE9PSBiMltpXSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnAnyConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnAnyConverter),\n/* harmony export */   AsnArray: () => (/* reexport safe */ _objects__WEBPACK_IMPORTED_MODULE_7__.AsnArray),\n/* harmony export */   AsnBitStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBitStringConverter),\n/* harmony export */   AsnBmpStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBmpStringConverter),\n/* harmony export */   AsnBooleanConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBooleanConverter),\n/* harmony export */   AsnCharacterStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnCharacterStringConverter),\n/* harmony export */   AsnChoiceType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnChoiceType),\n/* harmony export */   AsnConstructedOctetStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnConstructedOctetStringConverter),\n/* harmony export */   AsnConvert: () => (/* reexport safe */ _convert__WEBPACK_IMPORTED_MODULE_8__.AsnConvert),\n/* harmony export */   AsnEnumeratedConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnEnumeratedConverter),\n/* harmony export */   AsnGeneralStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGeneralStringConverter),\n/* harmony export */   AsnGeneralizedTimeConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGeneralizedTimeConverter),\n/* harmony export */   AsnGraphicStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGraphicStringConverter),\n/* harmony export */   AsnIA5StringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIA5StringConverter),\n/* harmony export */   AsnIntegerArrayBufferConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerArrayBufferConverter),\n/* harmony export */   AsnIntegerBigIntConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerBigIntConverter),\n/* harmony export */   AsnIntegerConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerConverter),\n/* harmony export */   AsnNullConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnNullConverter),\n/* harmony export */   AsnNumericStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnNumericStringConverter),\n/* harmony export */   AsnObjectIdentifierConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnObjectIdentifierConverter),\n/* harmony export */   AsnOctetStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnOctetStringConverter),\n/* harmony export */   AsnParser: () => (/* reexport safe */ _parser__WEBPACK_IMPORTED_MODULE_4__.AsnParser),\n/* harmony export */   AsnPrintableStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnPrintableStringConverter),\n/* harmony export */   AsnProp: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnProp),\n/* harmony export */   AsnPropTypes: () => (/* reexport safe */ _enums__WEBPACK_IMPORTED_MODULE_3__.AsnPropTypes),\n/* harmony export */   AsnSchemaValidationError: () => (/* reexport safe */ _errors__WEBPACK_IMPORTED_MODULE_6__.AsnSchemaValidationError),\n/* harmony export */   AsnSequenceType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnSequenceType),\n/* harmony export */   AsnSerializer: () => (/* reexport safe */ _serializer__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer),\n/* harmony export */   AsnSetType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnSetType),\n/* harmony export */   AsnTeletexStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnTeletexStringConverter),\n/* harmony export */   AsnType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnType),\n/* harmony export */   AsnTypeTypes: () => (/* reexport safe */ _enums__WEBPACK_IMPORTED_MODULE_3__.AsnTypeTypes),\n/* harmony export */   AsnUTCTimeConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUTCTimeConverter),\n/* harmony export */   AsnUniversalStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUniversalStringConverter),\n/* harmony export */   AsnUtf8StringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUtf8StringConverter),\n/* harmony export */   AsnVideotexStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnVideotexStringConverter),\n/* harmony export */   AsnVisibleStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnVisibleStringConverter),\n/* harmony export */   BitString: () => (/* reexport safe */ _types_index__WEBPACK_IMPORTED_MODULE_1__.BitString),\n/* harmony export */   OctetString: () => (/* reexport safe */ _types_index__WEBPACK_IMPORTED_MODULE_1__.OctetString),\n/* harmony export */   defaultConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.defaultConverter)\n/* harmony export */ });\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./converters */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _types_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\");\n/* harmony import */ var _decorators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./decorators */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/decorators.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./enums */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./parser */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/parser.js\");\n/* harmony import */ var _serializer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./serializer */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\");\n/* harmony import */ var _objects__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./objects */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/objects.js\");\n/* harmony import */ var _convert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./convert */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/convert.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTZCO0FBQ0M7QUFDOEQ7QUFDdkM7QUFDaEI7QUFDUTtBQUNwQjtBQUNDO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcGVjdWxpYXIvYXNuMS1zY2hlbWEvYnVpbGQvZXMyMDE1L2luZGV4LmpzPzMzZTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vY29udmVydGVyc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXMvaW5kZXhcIjtcbmV4cG9ydCB7IEFzblByb3AsIEFzblR5cGUsIEFzbkNob2ljZVR5cGUsIEFzblNlcXVlbmNlVHlwZSwgQXNuU2V0VHlwZSB9IGZyb20gXCIuL2RlY29yYXRvcnNcIjtcbmV4cG9ydCB7IEFzblR5cGVUeXBlcywgQXNuUHJvcFR5cGVzIH0gZnJvbSBcIi4vZW51bXNcIjtcbmV4cG9ydCB7IEFzblBhcnNlciB9IGZyb20gXCIuL3BhcnNlclwiO1xuZXhwb3J0IHsgQXNuU2VyaWFsaXplciB9IGZyb20gXCIuL3NlcmlhbGl6ZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2Vycm9yc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vb2JqZWN0c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY29udmVydFwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/objects.js":
/*!********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/objects.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnArray: () => (/* binding */ AsnArray)\n/* harmony export */ });\nclass AsnArray extends Array {\n    constructor(items = []) {\n        if (typeof items === \"number\") {\n            super(items);\n        }\n        else {\n            super();\n            for (const item of items) {\n                this.push(item);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9vYmplY3RzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvb2JqZWN0cy5qcz9iYzE3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBBc25BcnJheSBleHRlbmRzIEFycmF5IHtcbiAgICBjb25zdHJ1Y3RvcihpdGVtcyA9IFtdKSB7XG4gICAgICAgIGlmICh0eXBlb2YgaXRlbXMgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgIHN1cGVyKGl0ZW1zKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHN1cGVyKCk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgaXRlbXMpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnB1c2goaXRlbSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/objects.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/parser.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/parser.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnParser: () => (/* binding */ AsnParser)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./converters */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helper */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./storage */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\n\n\n\nclass AsnParser {\n    static parse(data, target) {\n        const asn1Parsed = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(data);\n        if (asn1Parsed.result.error) {\n            throw new Error(asn1Parsed.result.error);\n        }\n        const res = this.fromASN(asn1Parsed.result, target);\n        return res;\n    }\n    static fromASN(asn1Schema, target) {\n        var _a;\n        try {\n            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(target)) {\n                const value = new target();\n                return value.fromASN(asn1Schema);\n            }\n            const schema = _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(target);\n            _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.cache(target);\n            let targetSchema = schema.schema;\n            if (asn1Schema.constructor === asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed && schema.type !== _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice) {\n                targetSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                    idBlock: {\n                        tagClass: 3,\n                        tagNumber: asn1Schema.idBlock.tagNumber,\n                    },\n                    value: schema.schema.valueBlock.value,\n                });\n                for (const key in schema.items) {\n                    delete asn1Schema[key];\n                }\n            }\n            const asn1ComparedSchema = asn1js__WEBPACK_IMPORTED_MODULE_0__.compareSchema({}, asn1Schema, targetSchema);\n            if (!asn1ComparedSchema.verified) {\n                throw new _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError(`Data does not match to ${target.name} ASN1 schema. ${asn1ComparedSchema.result.error}`);\n            }\n            const res = new target();\n            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isTypeOfArray)(target)) {\n                if (!(\"value\" in asn1Schema.valueBlock && Array.isArray(asn1Schema.valueBlock.value))) {\n                    throw new Error(`Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.`);\n                }\n                const itemType = schema.itemType;\n                if (typeof itemType === \"number\") {\n                    const converter = _converters__WEBPACK_IMPORTED_MODULE_2__.defaultConverter(itemType);\n                    if (!converter) {\n                        throw new Error(`Cannot get default converter for array item of ${target.name} ASN1 schema`);\n                    }\n                    return target.from(asn1Schema.valueBlock.value, (element) => converter.fromASN(element));\n                }\n                else {\n                    return target.from(asn1Schema.valueBlock.value, (element) => this.fromASN(element, itemType));\n                }\n            }\n            for (const key in schema.items) {\n                const asn1SchemaValue = asn1ComparedSchema.result[key];\n                if (!asn1SchemaValue) {\n                    continue;\n                }\n                const schemaItem = schema.items[key];\n                const schemaItemType = schemaItem.type;\n                if (typeof schemaItemType === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)) {\n                    const converter = (_a = schemaItem.converter) !== null && _a !== void 0 ? _a : ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)\n                        ? new schemaItemType()\n                        : null);\n                    if (!converter) {\n                        throw new Error(\"Converter is empty\");\n                    }\n                    if (schemaItem.repeated) {\n                        if (schemaItem.implicit) {\n                            const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n                            const newItem = new Container();\n                            newItem.valueBlock = asn1SchemaValue.valueBlock;\n                            const newItemAsn = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(newItem.toBER(false));\n                            if (newItemAsn.offset === -1) {\n                                throw new Error(`Cannot parse the child item. ${newItemAsn.result.error}`);\n                            }\n                            if (!(\"value\" in newItemAsn.result.valueBlock &&\n                                Array.isArray(newItemAsn.result.valueBlock.value))) {\n                                throw new Error(\"Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.\");\n                            }\n                            const value = newItemAsn.result.valueBlock.value;\n                            res[key] = Array.from(value, (element) => converter.fromASN(element));\n                        }\n                        else {\n                            res[key] = Array.from(asn1SchemaValue, (element) => converter.fromASN(element));\n                        }\n                    }\n                    else {\n                        let value = asn1SchemaValue;\n                        if (schemaItem.implicit) {\n                            let newItem;\n                            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)) {\n                                newItem = new schemaItemType().toSchema(\"\");\n                            }\n                            else {\n                                const Asn1TypeName = _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes[schemaItemType];\n                                const Asn1Type = asn1js__WEBPACK_IMPORTED_MODULE_0__[Asn1TypeName];\n                                if (!Asn1Type) {\n                                    throw new Error(`Cannot get '${Asn1TypeName}' class from asn1js module`);\n                                }\n                                newItem = new Asn1Type();\n                            }\n                            newItem.valueBlock = value.valueBlock;\n                            value = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(newItem.toBER(false)).result;\n                        }\n                        res[key] = converter.fromASN(value);\n                    }\n                }\n                else {\n                    if (schemaItem.repeated) {\n                        if (!Array.isArray(asn1SchemaValue)) {\n                            throw new Error(\"Cannot get list of items from the ASN.1 parsed value. ASN.1 value should be iterable.\");\n                        }\n                        res[key] = Array.from(asn1SchemaValue, (element) => this.fromASN(element, schemaItemType));\n                    }\n                    else {\n                        res[key] = this.fromASN(asn1SchemaValue, schemaItemType);\n                    }\n                }\n            }\n            return res;\n        }\n        catch (error) {\n            if (error instanceof _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError) {\n                error.schemas.push(target.name);\n            }\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9wYXJzZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFpQztBQUNvQjtBQUNWO0FBQ1M7QUFDSTtBQUNkO0FBQ25DO0FBQ1A7QUFDQSwyQkFBMkIsMkNBQWM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLHNEQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixtREFBYTtBQUN4QyxZQUFZLG1EQUFhO0FBQ3pCO0FBQ0EsMkNBQTJDLCtDQUFrQixvQkFBb0IsZ0RBQVk7QUFDN0YsbUNBQW1DLCtDQUFrQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsaURBQW9CLEdBQUc7QUFDOUQ7QUFDQSwwQkFBMEIsNkRBQXdCLDJCQUEyQixhQUFhLGVBQWUsZ0NBQWdDO0FBQ3pJO0FBQ0E7QUFDQSxnQkFBZ0Isc0RBQWE7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyx5REFBMkI7QUFDakU7QUFDQSwwRkFBMEYsYUFBYTtBQUN2RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBELHNEQUFhO0FBQ3ZFLG9HQUFvRyxzREFBYTtBQUNqSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1GQUFtRiw0Q0FBZSxHQUFHLHVDQUFVO0FBQy9HO0FBQ0E7QUFDQSwrQ0FBK0MsMkNBQWM7QUFDN0Q7QUFDQSxnRkFBZ0Ysd0JBQXdCO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHNEQUFhO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxnREFBWTtBQUNqRSxpREFBaUQsbUNBQU07QUFDdkQ7QUFDQSxtRUFBbUUsYUFBYTtBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQywyQ0FBYztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyw2REFBd0I7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9wYXJzZXIuanM/ZjRjYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBhc24xanMgZnJvbSBcImFzbjFqc1wiO1xuaW1wb3J0IHsgQXNuUHJvcFR5cGVzLCBBc25UeXBlVHlwZXMgfSBmcm9tIFwiLi9lbnVtc1wiO1xuaW1wb3J0ICogYXMgY29udmVydGVycyBmcm9tIFwiLi9jb252ZXJ0ZXJzXCI7XG5pbXBvcnQgeyBBc25TY2hlbWFWYWxpZGF0aW9uRXJyb3IgfSBmcm9tIFwiLi9lcnJvcnNcIjtcbmltcG9ydCB7IGlzQ29udmVydGlibGUsIGlzVHlwZU9mQXJyYXkgfSBmcm9tIFwiLi9oZWxwZXJcIjtcbmltcG9ydCB7IHNjaGVtYVN0b3JhZ2UgfSBmcm9tIFwiLi9zdG9yYWdlXCI7XG5leHBvcnQgY2xhc3MgQXNuUGFyc2VyIHtcbiAgICBzdGF0aWMgcGFyc2UoZGF0YSwgdGFyZ2V0KSB7XG4gICAgICAgIGNvbnN0IGFzbjFQYXJzZWQgPSBhc24xanMuZnJvbUJFUihkYXRhKTtcbiAgICAgICAgaWYgKGFzbjFQYXJzZWQucmVzdWx0LmVycm9yKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYXNuMVBhcnNlZC5yZXN1bHQuZXJyb3IpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJlcyA9IHRoaXMuZnJvbUFTTihhc24xUGFyc2VkLnJlc3VsdCwgdGFyZ2V0KTtcbiAgICAgICAgcmV0dXJuIHJlcztcbiAgICB9XG4gICAgc3RhdGljIGZyb21BU04oYXNuMVNjaGVtYSwgdGFyZ2V0KSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGlmIChpc0NvbnZlcnRpYmxlKHRhcmdldCkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IG5ldyB0YXJnZXQoKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUuZnJvbUFTTihhc24xU2NoZW1hKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHNjaGVtYSA9IHNjaGVtYVN0b3JhZ2UuZ2V0KHRhcmdldCk7XG4gICAgICAgICAgICBzY2hlbWFTdG9yYWdlLmNhY2hlKHRhcmdldCk7XG4gICAgICAgICAgICBsZXQgdGFyZ2V0U2NoZW1hID0gc2NoZW1hLnNjaGVtYTtcbiAgICAgICAgICAgIGlmIChhc24xU2NoZW1hLmNvbnN0cnVjdG9yID09PSBhc24xanMuQ29uc3RydWN0ZWQgJiYgc2NoZW1hLnR5cGUgIT09IEFzblR5cGVUeXBlcy5DaG9pY2UpIHtcbiAgICAgICAgICAgICAgICB0YXJnZXRTY2hlbWEgPSBuZXcgYXNuMWpzLkNvbnN0cnVjdGVkKHtcbiAgICAgICAgICAgICAgICAgICAgaWRCbG9jazoge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGFnQ2xhc3M6IDMsXG4gICAgICAgICAgICAgICAgICAgICAgICB0YWdOdW1iZXI6IGFzbjFTY2hlbWEuaWRCbG9jay50YWdOdW1iZXIsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBzY2hlbWEuc2NoZW1hLnZhbHVlQmxvY2sudmFsdWUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gc2NoZW1hLml0ZW1zKSB7XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBhc24xU2NoZW1hW2tleV07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgYXNuMUNvbXBhcmVkU2NoZW1hID0gYXNuMWpzLmNvbXBhcmVTY2hlbWEoe30sIGFzbjFTY2hlbWEsIHRhcmdldFNjaGVtYSk7XG4gICAgICAgICAgICBpZiAoIWFzbjFDb21wYXJlZFNjaGVtYS52ZXJpZmllZCkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBBc25TY2hlbWFWYWxpZGF0aW9uRXJyb3IoYERhdGEgZG9lcyBub3QgbWF0Y2ggdG8gJHt0YXJnZXQubmFtZX0gQVNOMSBzY2hlbWEuICR7YXNuMUNvbXBhcmVkU2NoZW1hLnJlc3VsdC5lcnJvcn1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHJlcyA9IG5ldyB0YXJnZXQoKTtcbiAgICAgICAgICAgIGlmIChpc1R5cGVPZkFycmF5KHRhcmdldCkpIHtcbiAgICAgICAgICAgICAgICBpZiAoIShcInZhbHVlXCIgaW4gYXNuMVNjaGVtYS52YWx1ZUJsb2NrICYmIEFycmF5LmlzQXJyYXkoYXNuMVNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlKSkpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDYW5ub3QgZ2V0IGl0ZW1zIGZyb20gdGhlIEFTTi4xIHBhcnNlZCB2YWx1ZS4gQVNOLjEgb2JqZWN0IGlzIG5vdCBjb25zdHJ1Y3RlZC5gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgaXRlbVR5cGUgPSBzY2hlbWEuaXRlbVR5cGU7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBpdGVtVHlwZSA9PT0gXCJudW1iZXJcIikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjb252ZXJ0ZXIgPSBjb252ZXJ0ZXJzLmRlZmF1bHRDb252ZXJ0ZXIoaXRlbVR5cGUpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWNvbnZlcnRlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDYW5ub3QgZ2V0IGRlZmF1bHQgY29udmVydGVyIGZvciBhcnJheSBpdGVtIG9mICR7dGFyZ2V0Lm5hbWV9IEFTTjEgc2NoZW1hYCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRhcmdldC5mcm9tKGFzbjFTY2hlbWEudmFsdWVCbG9jay52YWx1ZSwgKGVsZW1lbnQpID0+IGNvbnZlcnRlci5mcm9tQVNOKGVsZW1lbnQpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB0YXJnZXQuZnJvbShhc24xU2NoZW1hLnZhbHVlQmxvY2sudmFsdWUsIChlbGVtZW50KSA9PiB0aGlzLmZyb21BU04oZWxlbWVudCwgaXRlbVR5cGUpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBzY2hlbWEuaXRlbXMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBhc24xU2NoZW1hVmFsdWUgPSBhc24xQ29tcGFyZWRTY2hlbWEucmVzdWx0W2tleV07XG4gICAgICAgICAgICAgICAgaWYgKCFhc24xU2NoZW1hVmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHNjaGVtYUl0ZW0gPSBzY2hlbWEuaXRlbXNba2V5XTtcbiAgICAgICAgICAgICAgICBjb25zdCBzY2hlbWFJdGVtVHlwZSA9IHNjaGVtYUl0ZW0udHlwZTtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHNjaGVtYUl0ZW1UeXBlID09PSBcIm51bWJlclwiIHx8IGlzQ29udmVydGlibGUoc2NoZW1hSXRlbVR5cGUpKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbnZlcnRlciA9IChfYSA9IHNjaGVtYUl0ZW0uY29udmVydGVyKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAoaXNDb252ZXJ0aWJsZShzY2hlbWFJdGVtVHlwZSlcbiAgICAgICAgICAgICAgICAgICAgICAgID8gbmV3IHNjaGVtYUl0ZW1UeXBlKClcbiAgICAgICAgICAgICAgICAgICAgICAgIDogbnVsbCk7XG4gICAgICAgICAgICAgICAgICAgIGlmICghY29udmVydGVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb252ZXJ0ZXIgaXMgZW1wdHlcIik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKHNjaGVtYUl0ZW0ucmVwZWF0ZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzY2hlbWFJdGVtLmltcGxpY2l0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgQ29udGFpbmVyID0gc2NoZW1hSXRlbS5yZXBlYXRlZCA9PT0gXCJzZXF1ZW5jZVwiID8gYXNuMWpzLlNlcXVlbmNlIDogYXNuMWpzLlNldDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdJdGVtID0gbmV3IENvbnRhaW5lcigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0l0ZW0udmFsdWVCbG9jayA9IGFzbjFTY2hlbWFWYWx1ZS52YWx1ZUJsb2NrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0l0ZW1Bc24gPSBhc24xanMuZnJvbUJFUihuZXdJdGVtLnRvQkVSKGZhbHNlKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG5ld0l0ZW1Bc24ub2Zmc2V0ID09PSAtMSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYENhbm5vdCBwYXJzZSB0aGUgY2hpbGQgaXRlbS4gJHtuZXdJdGVtQXNuLnJlc3VsdC5lcnJvcn1gKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCEoXCJ2YWx1ZVwiIGluIG5ld0l0ZW1Bc24ucmVzdWx0LnZhbHVlQmxvY2sgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShuZXdJdGVtQXNuLnJlc3VsdC52YWx1ZUJsb2NrLnZhbHVlKSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQ2Fubm90IGdldCBpdGVtcyBmcm9tIHRoZSBBU04uMSBwYXJzZWQgdmFsdWUuIEFTTi4xIG9iamVjdCBpcyBub3QgY29uc3RydWN0ZWQuXCIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IG5ld0l0ZW1Bc24ucmVzdWx0LnZhbHVlQmxvY2sudmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzW2tleV0gPSBBcnJheS5mcm9tKHZhbHVlLCAoZWxlbWVudCkgPT4gY29udmVydGVyLmZyb21BU04oZWxlbWVudCkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzW2tleV0gPSBBcnJheS5mcm9tKGFzbjFTY2hlbWFWYWx1ZSwgKGVsZW1lbnQpID0+IGNvbnZlcnRlci5mcm9tQVNOKGVsZW1lbnQpKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCB2YWx1ZSA9IGFzbjFTY2hlbWFWYWx1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzY2hlbWFJdGVtLmltcGxpY2l0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0IG5ld0l0ZW07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlzQ29udmVydGlibGUoc2NoZW1hSXRlbVR5cGUpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0l0ZW0gPSBuZXcgc2NoZW1hSXRlbVR5cGUoKS50b1NjaGVtYShcIlwiKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IEFzbjFUeXBlTmFtZSA9IEFzblByb3BUeXBlc1tzY2hlbWFJdGVtVHlwZV07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IEFzbjFUeXBlID0gYXNuMWpzW0FzbjFUeXBlTmFtZV07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghQXNuMVR5cGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgQ2Fubm90IGdldCAnJHtBc24xVHlwZU5hbWV9JyBjbGFzcyBmcm9tIGFzbjFqcyBtb2R1bGVgKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdJdGVtID0gbmV3IEFzbjFUeXBlKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0l0ZW0udmFsdWVCbG9jayA9IHZhbHVlLnZhbHVlQmxvY2s7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSBhc24xanMuZnJvbUJFUihuZXdJdGVtLnRvQkVSKGZhbHNlKSkucmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzW2tleV0gPSBjb252ZXJ0ZXIuZnJvbUFTTih2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChzY2hlbWFJdGVtLnJlcGVhdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIUFycmF5LmlzQXJyYXkoYXNuMVNjaGVtYVZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkNhbm5vdCBnZXQgbGlzdCBvZiBpdGVtcyBmcm9tIHRoZSBBU04uMSBwYXJzZWQgdmFsdWUuIEFTTi4xIHZhbHVlIHNob3VsZCBiZSBpdGVyYWJsZS5cIik7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICByZXNba2V5XSA9IEFycmF5LmZyb20oYXNuMVNjaGVtYVZhbHVlLCAoZWxlbWVudCkgPT4gdGhpcy5mcm9tQVNOKGVsZW1lbnQsIHNjaGVtYUl0ZW1UeXBlKSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXNba2V5XSA9IHRoaXMuZnJvbUFTTihhc24xU2NoZW1hVmFsdWUsIHNjaGVtYUl0ZW1UeXBlKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiByZXM7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBBc25TY2hlbWFWYWxpZGF0aW9uRXJyb3IpIHtcbiAgICAgICAgICAgICAgICBlcnJvci5zY2hlbWFzLnB1c2godGFyZ2V0Lm5hbWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/schema.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/schema.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaStorage: () => (/* binding */ AsnSchemaStorage)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helper */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n\n\n\nclass AsnSchemaStorage {\n    constructor() {\n        this.items = new WeakMap();\n    }\n    has(target) {\n        return this.items.has(target);\n    }\n    get(target, checkSchema = false) {\n        const schema = this.items.get(target);\n        if (!schema) {\n            throw new Error(`Cannot get schema for '${target.prototype.constructor.name}' target`);\n        }\n        if (checkSchema && !schema.schema) {\n            throw new Error(`Schema '${target.prototype.constructor.name}' doesn't contain ASN.1 schema. Call 'AsnSchemaStorage.cache'.`);\n        }\n        return schema;\n    }\n    cache(target) {\n        const schema = this.get(target);\n        if (!schema.schema) {\n            schema.schema = this.create(target, true);\n        }\n    }\n    createDefault(target) {\n        const schema = {\n            type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence,\n            items: {},\n        };\n        const parentSchema = this.findParentSchema(target);\n        if (parentSchema) {\n            Object.assign(schema, parentSchema);\n            schema.items = Object.assign({}, schema.items, parentSchema.items);\n        }\n        return schema;\n    }\n    create(target, useNames) {\n        const schema = this.items.get(target) || this.createDefault(target);\n        const asn1Value = [];\n        for (const key in schema.items) {\n            const item = schema.items[key];\n            const name = useNames ? key : \"\";\n            let asn1Item;\n            if (typeof item.type === \"number\") {\n                const Asn1TypeName = _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes[item.type];\n                const Asn1Type = asn1js__WEBPACK_IMPORTED_MODULE_0__[Asn1TypeName];\n                if (!Asn1Type) {\n                    throw new Error(`Cannot get ASN1 class by name '${Asn1TypeName}'`);\n                }\n                asn1Item = new Asn1Type({ name });\n            }\n            else if ((0,_helper__WEBPACK_IMPORTED_MODULE_2__.isConvertible)(item.type)) {\n                const instance = new item.type();\n                asn1Item = instance.toSchema(name);\n            }\n            else if (item.optional) {\n                const itemSchema = this.get(item.type);\n                if (itemSchema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice) {\n                    asn1Item = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Any({ name });\n                }\n                else {\n                    asn1Item = this.create(item.type, false);\n                    asn1Item.name = name;\n                }\n            }\n            else {\n                asn1Item = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Any({ name });\n            }\n            const optional = !!item.optional || item.defaultValue !== undefined;\n            if (item.repeated) {\n                asn1Item.name = \"\";\n                const Container = item.repeated === \"set\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Set : asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence;\n                asn1Item = new Container({\n                    name: \"\",\n                    value: [\n                        new asn1js__WEBPACK_IMPORTED_MODULE_0__.Repeated({\n                            name,\n                            value: asn1Item,\n                        }),\n                    ],\n                });\n            }\n            if (item.context !== null && item.context !== undefined) {\n                if (item.implicit) {\n                    if (typeof item.type === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_2__.isConvertible)(item.type)) {\n                        const Container = item.repeated ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed : asn1js__WEBPACK_IMPORTED_MODULE_0__.Primitive;\n                        asn1Value.push(new Container({\n                            name,\n                            optional,\n                            idBlock: {\n                                tagClass: 3,\n                                tagNumber: item.context,\n                            },\n                        }));\n                    }\n                    else {\n                        this.cache(item.type);\n                        const isRepeated = !!item.repeated;\n                        let value = !isRepeated ? this.get(item.type, true).schema : asn1Item;\n                        value =\n                            \"valueBlock\" in value\n                                ? value.valueBlock.value\n                                : value.value;\n                        asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                            name: !isRepeated ? name : \"\",\n                            optional,\n                            idBlock: {\n                                tagClass: 3,\n                                tagNumber: item.context,\n                            },\n                            value: value,\n                        }));\n                    }\n                }\n                else {\n                    asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                        optional,\n                        idBlock: {\n                            tagClass: 3,\n                            tagNumber: item.context,\n                        },\n                        value: [asn1Item],\n                    }));\n                }\n            }\n            else {\n                asn1Item.optional = optional;\n                asn1Value.push(asn1Item);\n            }\n        }\n        switch (schema.type) {\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence({ value: asn1Value, name: \"\" });\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Set:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Set({ value: asn1Value, name: \"\" });\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Choice({ value: asn1Value, name: \"\" });\n            default:\n                throw new Error(`Unsupported ASN1 type in use`);\n        }\n    }\n    set(target, schema) {\n        this.items.set(target, schema);\n        return this;\n    }\n    findParentSchema(target) {\n        const parent = Object.getPrototypeOf(target);\n        if (parent) {\n            const schema = this.items.get(parent);\n            return schema || this.findParentSchema(parent);\n        }\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9zY2hlbWEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNvQjtBQUNaO0FBQ2xDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELGtDQUFrQztBQUN4RjtBQUNBO0FBQ0EsdUNBQXVDLGtDQUFrQztBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGdEQUFZO0FBQzlCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQztBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGdEQUFZO0FBQ2pELGlDQUFpQyxtQ0FBTTtBQUN2QztBQUNBLHNFQUFzRSxhQUFhO0FBQ25GO0FBQ0EsMENBQTBDLE1BQU07QUFDaEQ7QUFDQSxxQkFBcUIsc0RBQWE7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxnREFBWTtBQUNwRCxtQ0FBbUMsdUNBQVUsR0FBRyxNQUFNO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLHVDQUFVLEdBQUcsTUFBTTtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCx1Q0FBVSxHQUFHLDRDQUFlO0FBQ3hGO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw0Q0FBZTtBQUMzQztBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCxzREFBYTtBQUN0RSwwREFBMEQsK0NBQWtCLEdBQUcsNkNBQWdCO0FBQy9GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3Qix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLCtDQUFrQjtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QywrQ0FBa0I7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixnREFBWTtBQUM3QiwyQkFBMkIsNENBQWUsR0FBRyw0QkFBNEI7QUFDekUsaUJBQWlCLGdEQUFZO0FBQzdCLDJCQUEyQix1Q0FBVSxHQUFHLDRCQUE0QjtBQUNwRSxpQkFBaUIsZ0RBQVk7QUFDN0IsMkJBQTJCLDBDQUFhLEdBQUcsNEJBQTRCO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcGVjdWxpYXIvYXNuMS1zY2hlbWEvYnVpbGQvZXMyMDE1L3NjaGVtYS5qcz8xMTI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGFzbjFqcyBmcm9tIFwiYXNuMWpzXCI7XG5pbXBvcnQgeyBBc25Qcm9wVHlwZXMsIEFzblR5cGVUeXBlcyB9IGZyb20gXCIuL2VudW1zXCI7XG5pbXBvcnQgeyBpc0NvbnZlcnRpYmxlIH0gZnJvbSBcIi4vaGVscGVyXCI7XG5leHBvcnQgY2xhc3MgQXNuU2NoZW1hU3RvcmFnZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuaXRlbXMgPSBuZXcgV2Vha01hcCgpO1xuICAgIH1cbiAgICBoYXModGFyZ2V0KSB7XG4gICAgICAgIHJldHVybiB0aGlzLml0ZW1zLmhhcyh0YXJnZXQpO1xuICAgIH1cbiAgICBnZXQodGFyZ2V0LCBjaGVja1NjaGVtYSA9IGZhbHNlKSB7XG4gICAgICAgIGNvbnN0IHNjaGVtYSA9IHRoaXMuaXRlbXMuZ2V0KHRhcmdldCk7XG4gICAgICAgIGlmICghc2NoZW1hKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYENhbm5vdCBnZXQgc2NoZW1hIGZvciAnJHt0YXJnZXQucHJvdG90eXBlLmNvbnN0cnVjdG9yLm5hbWV9JyB0YXJnZXRgKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY2hlY2tTY2hlbWEgJiYgIXNjaGVtYS5zY2hlbWEpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgU2NoZW1hICcke3RhcmdldC5wcm90b3R5cGUuY29uc3RydWN0b3IubmFtZX0nIGRvZXNuJ3QgY29udGFpbiBBU04uMSBzY2hlbWEuIENhbGwgJ0FzblNjaGVtYVN0b3JhZ2UuY2FjaGUnLmApO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBzY2hlbWE7XG4gICAgfVxuICAgIGNhY2hlKHRhcmdldCkge1xuICAgICAgICBjb25zdCBzY2hlbWEgPSB0aGlzLmdldCh0YXJnZXQpO1xuICAgICAgICBpZiAoIXNjaGVtYS5zY2hlbWEpIHtcbiAgICAgICAgICAgIHNjaGVtYS5zY2hlbWEgPSB0aGlzLmNyZWF0ZSh0YXJnZXQsIHRydWUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNyZWF0ZURlZmF1bHQodGFyZ2V0KSB7XG4gICAgICAgIGNvbnN0IHNjaGVtYSA9IHtcbiAgICAgICAgICAgIHR5cGU6IEFzblR5cGVUeXBlcy5TZXF1ZW5jZSxcbiAgICAgICAgICAgIGl0ZW1zOiB7fSxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgcGFyZW50U2NoZW1hID0gdGhpcy5maW5kUGFyZW50U2NoZW1hKHRhcmdldCk7XG4gICAgICAgIGlmIChwYXJlbnRTY2hlbWEpIHtcbiAgICAgICAgICAgIE9iamVjdC5hc3NpZ24oc2NoZW1hLCBwYXJlbnRTY2hlbWEpO1xuICAgICAgICAgICAgc2NoZW1hLml0ZW1zID0gT2JqZWN0LmFzc2lnbih7fSwgc2NoZW1hLml0ZW1zLCBwYXJlbnRTY2hlbWEuaXRlbXMpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBzY2hlbWE7XG4gICAgfVxuICAgIGNyZWF0ZSh0YXJnZXQsIHVzZU5hbWVzKSB7XG4gICAgICAgIGNvbnN0IHNjaGVtYSA9IHRoaXMuaXRlbXMuZ2V0KHRhcmdldCkgfHwgdGhpcy5jcmVhdGVEZWZhdWx0KHRhcmdldCk7XG4gICAgICAgIGNvbnN0IGFzbjFWYWx1ZSA9IFtdO1xuICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBzY2hlbWEuaXRlbXMpIHtcbiAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSBzY2hlbWEuaXRlbXNba2V5XTtcbiAgICAgICAgICAgIGNvbnN0IG5hbWUgPSB1c2VOYW1lcyA/IGtleSA6IFwiXCI7XG4gICAgICAgICAgICBsZXQgYXNuMUl0ZW07XG4gICAgICAgICAgICBpZiAodHlwZW9mIGl0ZW0udHlwZSA9PT0gXCJudW1iZXJcIikge1xuICAgICAgICAgICAgICAgIGNvbnN0IEFzbjFUeXBlTmFtZSA9IEFzblByb3BUeXBlc1tpdGVtLnR5cGVdO1xuICAgICAgICAgICAgICAgIGNvbnN0IEFzbjFUeXBlID0gYXNuMWpzW0FzbjFUeXBlTmFtZV07XG4gICAgICAgICAgICAgICAgaWYgKCFBc24xVHlwZSkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYENhbm5vdCBnZXQgQVNOMSBjbGFzcyBieSBuYW1lICcke0FzbjFUeXBlTmFtZX0nYCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGFzbjFJdGVtID0gbmV3IEFzbjFUeXBlKHsgbmFtZSB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGlzQ29udmVydGlibGUoaXRlbS50eXBlKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGluc3RhbmNlID0gbmV3IGl0ZW0udHlwZSgpO1xuICAgICAgICAgICAgICAgIGFzbjFJdGVtID0gaW5zdGFuY2UudG9TY2hlbWEobmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChpdGVtLm9wdGlvbmFsKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgaXRlbVNjaGVtYSA9IHRoaXMuZ2V0KGl0ZW0udHlwZSk7XG4gICAgICAgICAgICAgICAgaWYgKGl0ZW1TY2hlbWEudHlwZSA9PT0gQXNuVHlwZVR5cGVzLkNob2ljZSkge1xuICAgICAgICAgICAgICAgICAgICBhc24xSXRlbSA9IG5ldyBhc24xanMuQW55KHsgbmFtZSB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGFzbjFJdGVtID0gdGhpcy5jcmVhdGUoaXRlbS50eXBlLCBmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAgIGFzbjFJdGVtLm5hbWUgPSBuYW1lO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGFzbjFJdGVtID0gbmV3IGFzbjFqcy5BbnkoeyBuYW1lIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3Qgb3B0aW9uYWwgPSAhIWl0ZW0ub3B0aW9uYWwgfHwgaXRlbS5kZWZhdWx0VmFsdWUgIT09IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIGlmIChpdGVtLnJlcGVhdGVkKSB7XG4gICAgICAgICAgICAgICAgYXNuMUl0ZW0ubmFtZSA9IFwiXCI7XG4gICAgICAgICAgICAgICAgY29uc3QgQ29udGFpbmVyID0gaXRlbS5yZXBlYXRlZCA9PT0gXCJzZXRcIiA/IGFzbjFqcy5TZXQgOiBhc24xanMuU2VxdWVuY2U7XG4gICAgICAgICAgICAgICAgYXNuMUl0ZW0gPSBuZXcgQ29udGFpbmVyKHtcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogXCJcIixcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5ldyBhc24xanMuUmVwZWF0ZWQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGFzbjFJdGVtLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXRlbS5jb250ZXh0ICE9PSBudWxsICYmIGl0ZW0uY29udGV4dCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgaWYgKGl0ZW0uaW1wbGljaXQpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBpdGVtLnR5cGUgPT09IFwibnVtYmVyXCIgfHwgaXNDb252ZXJ0aWJsZShpdGVtLnR5cGUpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBDb250YWluZXIgPSBpdGVtLnJlcGVhdGVkID8gYXNuMWpzLkNvbnN0cnVjdGVkIDogYXNuMWpzLlByaW1pdGl2ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFzbjFWYWx1ZS5wdXNoKG5ldyBDb250YWluZXIoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9uYWwsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWRCbG9jazoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YWdDbGFzczogMyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFnTnVtYmVyOiBpdGVtLmNvbnRleHQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2FjaGUoaXRlbS50eXBlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzUmVwZWF0ZWQgPSAhIWl0ZW0ucmVwZWF0ZWQ7XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgdmFsdWUgPSAhaXNSZXBlYXRlZCA/IHRoaXMuZ2V0KGl0ZW0udHlwZSwgdHJ1ZSkuc2NoZW1hIDogYXNuMUl0ZW07XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJ2YWx1ZUJsb2NrXCIgaW4gdmFsdWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB2YWx1ZS52YWx1ZUJsb2NrLnZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdmFsdWUudmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgICBhc24xVmFsdWUucHVzaChuZXcgYXNuMWpzLkNvbnN0cnVjdGVkKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAhaXNSZXBlYXRlZCA/IG5hbWUgOiBcIlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbmFsLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkQmxvY2s6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFnQ2xhc3M6IDMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ051bWJlcjogaXRlbS5jb250ZXh0LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHZhbHVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBhc24xVmFsdWUucHVzaChuZXcgYXNuMWpzLkNvbnN0cnVjdGVkKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbmFsLFxuICAgICAgICAgICAgICAgICAgICAgICAgaWRCbG9jazoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ0NsYXNzOiAzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ051bWJlcjogaXRlbS5jb250ZXh0LFxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBbYXNuMUl0ZW1dLFxuICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgYXNuMUl0ZW0ub3B0aW9uYWwgPSBvcHRpb25hbDtcbiAgICAgICAgICAgICAgICBhc24xVmFsdWUucHVzaChhc24xSXRlbSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgc3dpdGNoIChzY2hlbWEudHlwZSkge1xuICAgICAgICAgICAgY2FzZSBBc25UeXBlVHlwZXMuU2VxdWVuY2U6XG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBhc24xanMuU2VxdWVuY2UoeyB2YWx1ZTogYXNuMVZhbHVlLCBuYW1lOiBcIlwiIH0pO1xuICAgICAgICAgICAgY2FzZSBBc25UeXBlVHlwZXMuU2V0OlxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgYXNuMWpzLlNldCh7IHZhbHVlOiBhc24xVmFsdWUsIG5hbWU6IFwiXCIgfSk7XG4gICAgICAgICAgICBjYXNlIEFzblR5cGVUeXBlcy5DaG9pY2U6XG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBhc24xanMuQ2hvaWNlKHsgdmFsdWU6IGFzbjFWYWx1ZSwgbmFtZTogXCJcIiB9KTtcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbnN1cHBvcnRlZCBBU04xIHR5cGUgaW4gdXNlYCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc2V0KHRhcmdldCwgc2NoZW1hKSB7XG4gICAgICAgIHRoaXMuaXRlbXMuc2V0KHRhcmdldCwgc2NoZW1hKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIGZpbmRQYXJlbnRTY2hlbWEodGFyZ2V0KSB7XG4gICAgICAgIGNvbnN0IHBhcmVudCA9IE9iamVjdC5nZXRQcm90b3R5cGVPZih0YXJnZXQpO1xuICAgICAgICBpZiAocGFyZW50KSB7XG4gICAgICAgICAgICBjb25zdCBzY2hlbWEgPSB0aGlzLml0ZW1zLmdldChwYXJlbnQpO1xuICAgICAgICAgICAgcmV0dXJuIHNjaGVtYSB8fCB0aGlzLmZpbmRQYXJlbnRTY2hlbWEocGFyZW50KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/schema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSerializer: () => (/* binding */ AsnSerializer)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./converters */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./enums */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helper */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./storage */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\n\n\nclass AsnSerializer {\n    static serialize(obj) {\n        if (obj instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.BaseBlock) {\n            return obj.toBER(false);\n        }\n        return this.toASN(obj).toBER(false);\n    }\n    static toASN(obj) {\n        if (obj && typeof obj === \"object\" && (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isConvertible)(obj)) {\n            return obj.toASN();\n        }\n        if (!(obj && typeof obj === \"object\")) {\n            throw new TypeError(\"Parameter 1 should be type of Object.\");\n        }\n        const target = obj.constructor;\n        const schema = _storage__WEBPACK_IMPORTED_MODULE_4__.schemaStorage.get(target);\n        _storage__WEBPACK_IMPORTED_MODULE_4__.schemaStorage.cache(target);\n        let asn1Value = [];\n        if (schema.itemType) {\n            if (!Array.isArray(obj)) {\n                throw new TypeError(\"Parameter 1 should be type of Array.\");\n            }\n            if (typeof schema.itemType === \"number\") {\n                const converter = _converters__WEBPACK_IMPORTED_MODULE_1__.defaultConverter(schema.itemType);\n                if (!converter) {\n                    throw new Error(`Cannot get default converter for array item of ${target.name} ASN1 schema`);\n                }\n                asn1Value = obj.map((o) => converter.toASN(o));\n            }\n            else {\n                asn1Value = obj.map((o) => this.toAsnItem({ type: schema.itemType }, \"[]\", target, o));\n            }\n        }\n        else {\n            for (const key in schema.items) {\n                const schemaItem = schema.items[key];\n                const objProp = obj[key];\n                if (objProp === undefined ||\n                    schemaItem.defaultValue === objProp ||\n                    (typeof schemaItem.defaultValue === \"object\" &&\n                        typeof objProp === \"object\" &&\n                        (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isArrayEqual)(this.serialize(schemaItem.defaultValue), this.serialize(objProp)))) {\n                    continue;\n                }\n                const asn1Item = AsnSerializer.toAsnItem(schemaItem, key, target, objProp);\n                if (typeof schemaItem.context === \"number\") {\n                    if (schemaItem.implicit) {\n                        if (!schemaItem.repeated &&\n                            (typeof schemaItem.type === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isConvertible)(schemaItem.type))) {\n                            const value = {};\n                            value.valueHex =\n                                asn1Item instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.Null\n                                    ? asn1Item.valueBeforeDecodeView\n                                    : asn1Item.valueBlock.toBER();\n                            asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Primitive({\n                                optional: schemaItem.optional,\n                                idBlock: {\n                                    tagClass: 3,\n                                    tagNumber: schemaItem.context,\n                                },\n                                ...value,\n                            }));\n                        }\n                        else {\n                            asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                                optional: schemaItem.optional,\n                                idBlock: {\n                                    tagClass: 3,\n                                    tagNumber: schemaItem.context,\n                                },\n                                value: asn1Item.valueBlock.value,\n                            }));\n                        }\n                    }\n                    else {\n                        asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                            optional: schemaItem.optional,\n                            idBlock: {\n                                tagClass: 3,\n                                tagNumber: schemaItem.context,\n                            },\n                            value: [asn1Item],\n                        }));\n                    }\n                }\n                else if (schemaItem.repeated) {\n                    asn1Value = asn1Value.concat(asn1Item);\n                }\n                else {\n                    asn1Value.push(asn1Item);\n                }\n            }\n        }\n        let asnSchema;\n        switch (schema.type) {\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Sequence:\n                asnSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence({ value: asn1Value });\n                break;\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Set:\n                asnSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Set({ value: asn1Value });\n                break;\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Choice:\n                if (!asn1Value[0]) {\n                    throw new Error(`Schema '${target.name}' has wrong data. Choice cannot be empty.`);\n                }\n                asnSchema = asn1Value[0];\n                break;\n        }\n        return asnSchema;\n    }\n    static toAsnItem(schemaItem, key, target, objProp) {\n        let asn1Item;\n        if (typeof schemaItem.type === \"number\") {\n            const converter = schemaItem.converter;\n            if (!converter) {\n                throw new Error(`Property '${key}' doesn't have converter for type ${_enums__WEBPACK_IMPORTED_MODULE_2__.AsnPropTypes[schemaItem.type]} in schema '${target.name}'`);\n            }\n            if (schemaItem.repeated) {\n                if (!Array.isArray(objProp)) {\n                    throw new TypeError(\"Parameter 'objProp' should be type of Array.\");\n                }\n                const items = Array.from(objProp, (element) => converter.toASN(element));\n                const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n                asn1Item = new Container({\n                    value: items,\n                });\n            }\n            else {\n                asn1Item = converter.toASN(objProp);\n            }\n        }\n        else {\n            if (schemaItem.repeated) {\n                if (!Array.isArray(objProp)) {\n                    throw new TypeError(\"Parameter 'objProp' should be type of Array.\");\n                }\n                const items = Array.from(objProp, (element) => this.toASN(element));\n                const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n                asn1Item = new Container({\n                    value: items,\n                });\n            }\n            else {\n                asn1Item = this.toASN(objProp);\n            }\n        }\n        return asn1Item;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js":
/*!********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/storage.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   schemaStorage: () => (/* binding */ schemaStorage)\n/* harmony export */ });\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/schema.js\");\n\nconst schemaStorage = new _schema__WEBPACK_IMPORTED_MODULE_0__.AsnSchemaStorage();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9zdG9yYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQ3JDLDBCQUEwQixxREFBZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcGVjdWxpYXIvYXNuMS1zY2hlbWEvYnVpbGQvZXMyMDE1L3N0b3JhZ2UuanM/ZGM2MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBc25TY2hlbWFTdG9yYWdlIH0gZnJvbSBcIi4vc2NoZW1hXCI7XG5leHBvcnQgY29uc3Qgc2NoZW1hU3RvcmFnZSA9IG5ldyBBc25TY2hlbWFTdG9yYWdlKCk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BitString: () => (/* binding */ BitString)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/./node_modules/pvtsutils/build/index.es.js\");\n\n\nclass BitString {\n    constructor(params, unusedBits = 0) {\n        this.unusedBits = 0;\n        this.value = new ArrayBuffer(0);\n        if (params) {\n            if (typeof params === \"number\") {\n                this.fromNumber(params);\n            }\n            else if (pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(params)) {\n                this.unusedBits = unusedBits;\n                this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(params);\n            }\n            else {\n                throw TypeError(\"Unsupported type of 'params' argument for BitString\");\n            }\n        }\n    }\n    fromASN(asn) {\n        if (!(asn instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString)) {\n            throw new TypeError(\"Argument 'asn' is not instance of ASN.1 BitString\");\n        }\n        this.unusedBits = asn.valueBlock.unusedBits;\n        this.value = asn.valueBlock.valueHex;\n        return this;\n    }\n    toASN() {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ unusedBits: this.unusedBits, valueHex: this.value });\n    }\n    toSchema(name) {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ name });\n    }\n    toNumber() {\n        let res = \"\";\n        const uintArray = new Uint8Array(this.value);\n        for (const octet of uintArray) {\n            res += octet.toString(2).padStart(8, \"0\");\n        }\n        res = res.split(\"\").reverse().join(\"\");\n        if (this.unusedBits) {\n            res = res.slice(this.unusedBits).padStart(this.unusedBits, \"0\");\n        }\n        return parseInt(res, 2);\n    }\n    fromNumber(value) {\n        let bits = value.toString(2);\n        const octetSize = (bits.length + 7) >> 3;\n        this.unusedBits = (octetSize << 3) - bits.length;\n        const octets = new Uint8Array(octetSize);\n        bits = bits\n            .padStart(octetSize << 3, \"0\")\n            .split(\"\")\n            .reverse()\n            .join(\"\");\n        let index = 0;\n        while (index < octetSize) {\n            octets[index] = parseInt(bits.slice(index << 3, (index << 3) + 8), 2);\n            index++;\n        }\n        this.value = octets.buffer;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BitString: () => (/* reexport safe */ _bit_string__WEBPACK_IMPORTED_MODULE_0__.BitString),\n/* harmony export */   OctetString: () => (/* reexport safe */ _octet_string__WEBPACK_IMPORTED_MODULE_1__.OctetString)\n/* harmony export */ });\n/* harmony import */ var _bit_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bit_string */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js\");\n/* harmony import */ var _octet_string__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./octet_string */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS90eXBlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZCO0FBQ0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcGVjdWxpYXIvYXNuMS1zY2hlbWEvYnVpbGQvZXMyMDE1L3R5cGVzL2luZGV4LmpzPzA0NGEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vYml0X3N0cmluZ1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vb2N0ZXRfc3RyaW5nXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OctetString: () => (/* binding */ OctetString)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/./node_modules/pvtsutils/build/index.es.js\");\n\n\nclass OctetString {\n    get byteLength() {\n        return this.buffer.byteLength;\n    }\n    get byteOffset() {\n        return 0;\n    }\n    constructor(param) {\n        if (typeof param === \"number\") {\n            this.buffer = new ArrayBuffer(param);\n        }\n        else {\n            if (pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(param)) {\n                this.buffer = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(param);\n            }\n            else if (Array.isArray(param)) {\n                this.buffer = new Uint8Array(param);\n            }\n            else {\n                this.buffer = new ArrayBuffer(0);\n            }\n        }\n    }\n    fromASN(asn) {\n        if (!(asn instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString)) {\n            throw new TypeError(\"Argument 'asn' is not instance of ASN.1 OctetString\");\n        }\n        this.buffer = asn.valueBlock.valueHex;\n        return this;\n    }\n    toASN() {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ valueHex: this.buffer });\n    }\n    toSchema(name) {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ name });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/json-schema/build/index.es.js":
/*!**************************************************************!*\
  !*** ./node_modules/@peculiar/json-schema/build/index.es.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonError: () => (/* binding */ JsonError),\n/* harmony export */   JsonParser: () => (/* binding */ JsonParser),\n/* harmony export */   JsonProp: () => (/* binding */ JsonProp),\n/* harmony export */   JsonPropTypes: () => (/* binding */ JsonPropTypes),\n/* harmony export */   JsonSerializer: () => (/* binding */ JsonSerializer),\n/* harmony export */   KeyError: () => (/* binding */ KeyError),\n/* harmony export */   ParserError: () => (/* binding */ ParserError),\n/* harmony export */   SerializerError: () => (/* binding */ SerializerError),\n/* harmony export */   TransformError: () => (/* binding */ TransformError),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError)\n/* harmony export */ });\n/**\n * Copyright (c) 2020, Peculiar Ventures, All rights reserved.\n */\n\nclass JsonError extends Error {\r\n    constructor(message, innerError) {\r\n        super(innerError\r\n            ? `${message}. See the inner exception for more details.`\r\n            : message);\r\n        this.message = message;\r\n        this.innerError = innerError;\r\n    }\r\n}\n\nclass TransformError extends JsonError {\r\n    constructor(schema, message, innerError) {\r\n        super(message, innerError);\r\n        this.schema = schema;\r\n    }\r\n}\n\nclass ParserError extends TransformError {\r\n    constructor(schema, message, innerError) {\r\n        super(schema, `JSON doesn't match to '${schema.target.name}' schema. ${message}`, innerError);\r\n    }\r\n}\n\nclass ValidationError extends JsonError {\r\n}\n\nclass SerializerError extends JsonError {\r\n    constructor(schemaName, message, innerError) {\r\n        super(`Cannot serialize by '${schemaName}' schema. ${message}`, innerError);\r\n        this.schemaName = schemaName;\r\n    }\r\n}\n\nclass KeyError extends ParserError {\r\n    constructor(schema, keys, errors = {}) {\r\n        super(schema, \"Some keys doesn't match to schema\");\r\n        this.keys = keys;\r\n        this.errors = errors;\r\n    }\r\n}\n\nvar JsonPropTypes;\r\n(function (JsonPropTypes) {\r\n    JsonPropTypes[JsonPropTypes[\"Any\"] = 0] = \"Any\";\r\n    JsonPropTypes[JsonPropTypes[\"Boolean\"] = 1] = \"Boolean\";\r\n    JsonPropTypes[JsonPropTypes[\"Number\"] = 2] = \"Number\";\r\n    JsonPropTypes[JsonPropTypes[\"String\"] = 3] = \"String\";\r\n})(JsonPropTypes || (JsonPropTypes = {}));\n\nfunction checkType(value, type) {\r\n    switch (type) {\r\n        case JsonPropTypes.Boolean:\r\n            return typeof value === \"boolean\";\r\n        case JsonPropTypes.Number:\r\n            return typeof value === \"number\";\r\n        case JsonPropTypes.String:\r\n            return typeof value === \"string\";\r\n    }\r\n    return true;\r\n}\r\nfunction throwIfTypeIsWrong(value, type) {\r\n    if (!checkType(value, type)) {\r\n        throw new TypeError(`Value must be ${JsonPropTypes[type]}`);\r\n    }\r\n}\r\nfunction isConvertible(target) {\r\n    if (target && target.prototype) {\r\n        if (target.prototype.toJSON && target.prototype.fromJSON) {\r\n            return true;\r\n        }\r\n        else {\r\n            return isConvertible(target.prototype);\r\n        }\r\n    }\r\n    else {\r\n        return !!(target && target.toJSON && target.fromJSON);\r\n    }\r\n}\n\nclass JsonSchemaStorage {\r\n    constructor() {\r\n        this.items = new Map();\r\n    }\r\n    has(target) {\r\n        return this.items.has(target) || !!this.findParentSchema(target);\r\n    }\r\n    get(target) {\r\n        const schema = this.items.get(target) || this.findParentSchema(target);\r\n        if (!schema) {\r\n            throw new Error(\"Cannot get schema for current target\");\r\n        }\r\n        return schema;\r\n    }\r\n    create(target) {\r\n        const schema = { names: {} };\r\n        const parentSchema = this.findParentSchema(target);\r\n        if (parentSchema) {\r\n            Object.assign(schema, parentSchema);\r\n            schema.names = {};\r\n            for (const name in parentSchema.names) {\r\n                schema.names[name] = Object.assign({}, parentSchema.names[name]);\r\n            }\r\n        }\r\n        schema.target = target;\r\n        return schema;\r\n    }\r\n    set(target, schema) {\r\n        this.items.set(target, schema);\r\n        return this;\r\n    }\r\n    findParentSchema(target) {\r\n        const parent = target.__proto__;\r\n        if (parent) {\r\n            const schema = this.items.get(parent);\r\n            return schema || this.findParentSchema(parent);\r\n        }\r\n        return null;\r\n    }\r\n}\n\nconst DEFAULT_SCHEMA = \"default\";\r\nconst schemaStorage = new JsonSchemaStorage();\n\nclass PatternValidation {\r\n    constructor(pattern) {\r\n        this.pattern = new RegExp(pattern);\r\n    }\r\n    validate(value) {\r\n        const pattern = new RegExp(this.pattern.source, this.pattern.flags);\r\n        if (typeof value !== \"string\") {\r\n            throw new ValidationError(\"Incoming value must be string\");\r\n        }\r\n        if (!pattern.exec(value)) {\r\n            throw new ValidationError(`Value doesn't match to pattern '${pattern.toString()}'`);\r\n        }\r\n    }\r\n}\n\nclass InclusiveValidation {\r\n    constructor(min = Number.MIN_VALUE, max = Number.MAX_VALUE) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.Number);\r\n        if (!(this.min <= value && value <= this.max)) {\r\n            const min = this.min === Number.MIN_VALUE ? \"MIN\" : this.min;\r\n            const max = this.max === Number.MAX_VALUE ? \"MAX\" : this.max;\r\n            throw new ValidationError(`Value doesn't match to diapason [${min},${max}]`);\r\n        }\r\n    }\r\n}\n\nclass ExclusiveValidation {\r\n    constructor(min = Number.MIN_VALUE, max = Number.MAX_VALUE) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.Number);\r\n        if (!(this.min < value && value < this.max)) {\r\n            const min = this.min === Number.MIN_VALUE ? \"MIN\" : this.min;\r\n            const max = this.max === Number.MAX_VALUE ? \"MAX\" : this.max;\r\n            throw new ValidationError(`Value doesn't match to diapason (${min},${max})`);\r\n        }\r\n    }\r\n}\n\nclass LengthValidation {\r\n    constructor(length, minLength, maxLength) {\r\n        this.length = length;\r\n        this.minLength = minLength;\r\n        this.maxLength = maxLength;\r\n    }\r\n    validate(value) {\r\n        if (this.length !== undefined) {\r\n            if (value.length !== this.length) {\r\n                throw new ValidationError(`Value length must be exactly ${this.length}.`);\r\n            }\r\n            return;\r\n        }\r\n        if (this.minLength !== undefined) {\r\n            if (value.length < this.minLength) {\r\n                throw new ValidationError(`Value length must be more than ${this.minLength}.`);\r\n            }\r\n        }\r\n        if (this.maxLength !== undefined) {\r\n            if (value.length > this.maxLength) {\r\n                throw new ValidationError(`Value length must be less than ${this.maxLength}.`);\r\n            }\r\n        }\r\n    }\r\n}\n\nclass EnumerationValidation {\r\n    constructor(enumeration) {\r\n        this.enumeration = enumeration;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.String);\r\n        if (!this.enumeration.includes(value)) {\r\n            throw new ValidationError(`Value must be one of ${this.enumeration.map((v) => `'${v}'`).join(\", \")}`);\r\n        }\r\n    }\r\n}\n\nclass JsonTransform {\r\n    static checkValues(data, schemaItem) {\r\n        const values = Array.isArray(data) ? data : [data];\r\n        for (const value of values) {\r\n            for (const validation of schemaItem.validations) {\r\n                if (validation instanceof LengthValidation && schemaItem.repeated) {\r\n                    validation.validate(data);\r\n                }\r\n                else {\r\n                    validation.validate(value);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    static checkTypes(value, schemaItem) {\r\n        if (schemaItem.repeated && !Array.isArray(value)) {\r\n            throw new TypeError(\"Value must be Array\");\r\n        }\r\n        if (typeof schemaItem.type === \"number\") {\r\n            const values = Array.isArray(value) ? value : [value];\r\n            for (const v of values) {\r\n                throwIfTypeIsWrong(v, schemaItem.type);\r\n            }\r\n        }\r\n    }\r\n    static getSchemaByName(schema, name = DEFAULT_SCHEMA) {\r\n        return { ...schema.names[DEFAULT_SCHEMA], ...schema.names[name] };\r\n    }\r\n}\n\nclass JsonSerializer extends JsonTransform {\r\n    static serialize(obj, options, replacer, space) {\r\n        const json = this.toJSON(obj, options);\r\n        return JSON.stringify(json, replacer, space);\r\n    }\r\n    static toJSON(obj, options = {}) {\r\n        let res;\r\n        let targetSchema = options.targetSchema;\r\n        const schemaName = options.schemaName || DEFAULT_SCHEMA;\r\n        if (isConvertible(obj)) {\r\n            return obj.toJSON();\r\n        }\r\n        if (Array.isArray(obj)) {\r\n            res = [];\r\n            for (const item of obj) {\r\n                res.push(this.toJSON(item, options));\r\n            }\r\n        }\r\n        else if (typeof obj === \"object\") {\r\n            if (targetSchema && !schemaStorage.has(targetSchema)) {\r\n                throw new JsonError(\"Cannot get schema for `targetSchema` param\");\r\n            }\r\n            targetSchema = (targetSchema || obj.constructor);\r\n            if (schemaStorage.has(targetSchema)) {\r\n                const schema = schemaStorage.get(targetSchema);\r\n                res = {};\r\n                const namedSchema = this.getSchemaByName(schema, schemaName);\r\n                for (const key in namedSchema) {\r\n                    try {\r\n                        const item = namedSchema[key];\r\n                        const objItem = obj[key];\r\n                        let value;\r\n                        if ((item.optional && objItem === undefined)\r\n                            || (item.defaultValue !== undefined && objItem === item.defaultValue)) {\r\n                            continue;\r\n                        }\r\n                        if (!item.optional && objItem === undefined) {\r\n                            throw new SerializerError(targetSchema.name, `Property '${key}' is required.`);\r\n                        }\r\n                        if (typeof item.type === \"number\") {\r\n                            if (item.converter) {\r\n                                if (item.repeated) {\r\n                                    value = objItem.map((el) => item.converter.toJSON(el, obj));\r\n                                }\r\n                                else {\r\n                                    value = item.converter.toJSON(objItem, obj);\r\n                                }\r\n                            }\r\n                            else {\r\n                                value = objItem;\r\n                            }\r\n                        }\r\n                        else {\r\n                            if (item.repeated) {\r\n                                value = objItem.map((el) => this.toJSON(el, { schemaName }));\r\n                            }\r\n                            else {\r\n                                value = this.toJSON(objItem, { schemaName });\r\n                            }\r\n                        }\r\n                        this.checkTypes(value, item);\r\n                        this.checkValues(value, item);\r\n                        res[item.name || key] = value;\r\n                    }\r\n                    catch (e) {\r\n                        if (e instanceof SerializerError) {\r\n                            throw e;\r\n                        }\r\n                        else {\r\n                            throw new SerializerError(schema.target.name, `Property '${key}' is wrong. ${e.message}`, e);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                res = {};\r\n                for (const key in obj) {\r\n                    res[key] = this.toJSON(obj[key], { schemaName });\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            res = obj;\r\n        }\r\n        return res;\r\n    }\r\n}\n\nclass JsonParser extends JsonTransform {\r\n    static parse(data, options) {\r\n        const obj = JSON.parse(data);\r\n        return this.fromJSON(obj, options);\r\n    }\r\n    static fromJSON(target, options) {\r\n        const targetSchema = options.targetSchema;\r\n        const schemaName = options.schemaName || DEFAULT_SCHEMA;\r\n        const obj = new targetSchema();\r\n        if (isConvertible(obj)) {\r\n            return obj.fromJSON(target);\r\n        }\r\n        const schema = schemaStorage.get(targetSchema);\r\n        const namedSchema = this.getSchemaByName(schema, schemaName);\r\n        const keyErrors = {};\r\n        if (options.strictProperty && !Array.isArray(target)) {\r\n            JsonParser.checkStrictProperty(target, namedSchema, schema);\r\n        }\r\n        for (const key in namedSchema) {\r\n            try {\r\n                const item = namedSchema[key];\r\n                const name = item.name || key;\r\n                const value = target[name];\r\n                if (value === undefined && (item.optional || item.defaultValue !== undefined)) {\r\n                    continue;\r\n                }\r\n                if (!item.optional && value === undefined) {\r\n                    throw new ParserError(schema, `Property '${name}' is required.`);\r\n                }\r\n                this.checkTypes(value, item);\r\n                this.checkValues(value, item);\r\n                if (typeof (item.type) === \"number\") {\r\n                    if (item.converter) {\r\n                        if (item.repeated) {\r\n                            obj[key] = value.map((el) => item.converter.fromJSON(el, obj));\r\n                        }\r\n                        else {\r\n                            obj[key] = item.converter.fromJSON(value, obj);\r\n                        }\r\n                    }\r\n                    else {\r\n                        obj[key] = value;\r\n                    }\r\n                }\r\n                else {\r\n                    const newOptions = {\r\n                        ...options,\r\n                        targetSchema: item.type,\r\n                        schemaName,\r\n                    };\r\n                    if (item.repeated) {\r\n                        obj[key] = value.map((el) => this.fromJSON(el, newOptions));\r\n                    }\r\n                    else {\r\n                        obj[key] = this.fromJSON(value, newOptions);\r\n                    }\r\n                }\r\n            }\r\n            catch (e) {\r\n                if (!(e instanceof ParserError)) {\r\n                    e = new ParserError(schema, `Property '${key}' is wrong. ${e.message}`, e);\r\n                }\r\n                if (options.strictAllKeys) {\r\n                    keyErrors[key] = e;\r\n                }\r\n                else {\r\n                    throw e;\r\n                }\r\n            }\r\n        }\r\n        const keys = Object.keys(keyErrors);\r\n        if (keys.length) {\r\n            throw new KeyError(schema, keys, keyErrors);\r\n        }\r\n        return obj;\r\n    }\r\n    static checkStrictProperty(target, namedSchema, schema) {\r\n        const jsonProps = Object.keys(target);\r\n        const schemaProps = Object.keys(namedSchema);\r\n        const keys = [];\r\n        for (const key of jsonProps) {\r\n            if (schemaProps.indexOf(key) === -1) {\r\n                keys.push(key);\r\n            }\r\n        }\r\n        if (keys.length) {\r\n            throw new KeyError(schema, keys);\r\n        }\r\n    }\r\n}\n\nfunction getValidations(item) {\r\n    const validations = [];\r\n    if (item.pattern) {\r\n        validations.push(new PatternValidation(item.pattern));\r\n    }\r\n    if (item.type === JsonPropTypes.Number || item.type === JsonPropTypes.Any) {\r\n        if (item.minInclusive !== undefined || item.maxInclusive !== undefined) {\r\n            validations.push(new InclusiveValidation(item.minInclusive, item.maxInclusive));\r\n        }\r\n        if (item.minExclusive !== undefined || item.maxExclusive !== undefined) {\r\n            validations.push(new ExclusiveValidation(item.minExclusive, item.maxExclusive));\r\n        }\r\n        if (item.enumeration !== undefined) {\r\n            validations.push(new EnumerationValidation(item.enumeration));\r\n        }\r\n    }\r\n    if (item.type === JsonPropTypes.String || item.repeated || item.type === JsonPropTypes.Any) {\r\n        if (item.length !== undefined || item.minLength !== undefined || item.maxLength !== undefined) {\r\n            validations.push(new LengthValidation(item.length, item.minLength, item.maxLength));\r\n        }\r\n    }\r\n    return validations;\r\n}\r\nconst JsonProp = (options = {}) => (target, propertyKey) => {\r\n    const errorMessage = `Cannot set type for ${propertyKey} property of ${target.constructor.name} schema`;\r\n    let schema;\r\n    if (!schemaStorage.has(target.constructor)) {\r\n        schema = schemaStorage.create(target.constructor);\r\n        schemaStorage.set(target.constructor, schema);\r\n    }\r\n    else {\r\n        schema = schemaStorage.get(target.constructor);\r\n        if (schema.target !== target.constructor) {\r\n            schema = schemaStorage.create(target.constructor);\r\n            schemaStorage.set(target.constructor, schema);\r\n        }\r\n    }\r\n    const defaultSchema = {\r\n        type: JsonPropTypes.Any,\r\n        validations: [],\r\n    };\r\n    const copyOptions = Object.assign(defaultSchema, options);\r\n    copyOptions.validations = getValidations(copyOptions);\r\n    if (typeof copyOptions.type !== \"number\") {\r\n        if (!schemaStorage.has(copyOptions.type) && !isConvertible(copyOptions.type)) {\r\n            throw new Error(`${errorMessage}. Assigning type doesn't have schema.`);\r\n        }\r\n    }\r\n    let schemaNames;\r\n    if (Array.isArray(options.schema)) {\r\n        schemaNames = options.schema;\r\n    }\r\n    else {\r\n        schemaNames = [options.schema || DEFAULT_SCHEMA];\r\n    }\r\n    for (const schemaName of schemaNames) {\r\n        if (!schema.names[schemaName]) {\r\n            schema.names[schemaName] = {};\r\n        }\r\n        const namedSchema = schema.names[schemaName];\r\n        namedSchema[propertyKey] = copyOptions;\r\n    }\r\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/json-schema/build/index.es.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@peculiar/webcrypto/build/webcrypto.es.js":
/*!****************************************************************!*\
  !*** ./node_modules/@peculiar/webcrypto/build/webcrypto.es.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Crypto: () => (/* binding */ Crypto),\n/* harmony export */   CryptoKey: () => (/* reexport safe */ webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey)\n/* harmony export */ });\n/* harmony import */ var webcrypto_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! webcrypto-core */ \"(rsc)/./node_modules/webcrypto-core/build/webcrypto-core.es.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var process__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! process */ \"process\");\n/* harmony import */ var process__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(process__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @peculiar/json-schema */ \"(rsc)/./node_modules/@peculiar/json-schema/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/./node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @peculiar/asn1-schema */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/index.js\");\n/*!\n Copyright (c) Peculiar Ventures, LLC\n*/\n\n\n\n\n\n\n\n\n\n\n\n\nconst JsonBase64UrlConverter = {\r\n    fromJSON: (value) => Buffer.from(pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.FromBase64Url(value)),\r\n    toJSON: (value) => pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64Url(value),\r\n};\n\nclass CryptoKey extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.data = Buffer.alloc(0);\r\n        this.algorithm = { name: \"\" };\r\n        this.extractable = false;\r\n        this.type = \"secret\";\r\n        this.usages = [];\r\n        this.kty = \"oct\";\r\n        this.alg = \"\";\r\n    }\r\n}\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"ext\", type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonPropTypes.Boolean, optional: true })\r\n], CryptoKey.prototype, \"extractable\", void 0);\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"key_ops\", type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonPropTypes.String, repeated: true, optional: true })\r\n], CryptoKey.prototype, \"usages\", void 0);\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonPropTypes.String })\r\n], CryptoKey.prototype, \"kty\", void 0);\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonPropTypes.String, optional: true })\r\n], CryptoKey.prototype, \"alg\", void 0);\n\nclass SymmetricKey extends CryptoKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.kty = \"oct\";\r\n        this.type = \"secret\";\r\n    }\r\n}\n\nclass AsymmetricKey extends CryptoKey {\r\n}\n\nclass AesCryptoKey extends SymmetricKey {\r\n    get alg() {\r\n        switch (this.algorithm.name.toUpperCase()) {\r\n            case \"AES-CBC\":\r\n                return `A${this.algorithm.length}CBC`;\r\n            case \"AES-CTR\":\r\n                return `A${this.algorithm.length}CTR`;\r\n            case \"AES-GCM\":\r\n                return `A${this.algorithm.length}GCM`;\r\n            case \"AES-KW\":\r\n                return `A${this.algorithm.length}KW`;\r\n            case \"AES-CMAC\":\r\n                return `A${this.algorithm.length}CMAC`;\r\n            case \"AES-ECB\":\r\n                return `A${this.algorithm.length}ECB`;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AlgorithmError(\"Unsupported algorithm name\");\r\n        }\r\n    }\r\n    set alg(value) {\r\n    }\r\n}\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\r\n], AesCryptoKey.prototype, \"data\", void 0);\n\nconst keyStorage = new WeakMap();\r\nfunction getCryptoKey(key) {\r\n    const res = keyStorage.get(key);\r\n    if (!res) {\r\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"Cannot get CryptoKey from secure storage\");\r\n    }\r\n    return res;\r\n}\r\nfunction setCryptoKey(value) {\r\n    const key = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey.create(value.algorithm, value.type, value.extractable, value.usages);\r\n    Object.freeze(key);\r\n    keyStorage.set(key, value);\r\n    return key;\r\n}\n\nclass AesCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const key = new AesCryptoKey();\r\n        key.algorithm = algorithm;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(algorithm.length >> 3);\r\n        return key;\r\n    }\r\n    static async exportKey(format, key) {\r\n        if (!(key instanceof AesCryptoKey)) {\r\n            throw new Error(\"key: Is not AesCryptoKey\");\r\n        }\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"raw\":\r\n                return new Uint8Array(key.data).buffer;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        let key;\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: AesCryptoKey });\r\n                break;\r\n            case \"raw\":\r\n                key = new AesCryptoKey();\r\n                key.data = Buffer.from(keyData);\r\n                break;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n        key.algorithm = algorithm;\r\n        key.algorithm.length = key.data.length << 3;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        switch (key.algorithm.length) {\r\n            case 128:\r\n            case 192:\r\n            case 256:\r\n                break;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Is wrong key length\");\r\n        }\r\n        return key;\r\n    }\r\n    static async encrypt(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"AES-CBC\":\r\n                return this.encryptAesCBC(algorithm, key, Buffer.from(data));\r\n            case \"AES-CTR\":\r\n                return this.encryptAesCTR(algorithm, key, Buffer.from(data));\r\n            case \"AES-GCM\":\r\n                return this.encryptAesGCM(algorithm, key, Buffer.from(data));\r\n            case \"AES-KW\":\r\n                return this.encryptAesKW(algorithm, key, Buffer.from(data));\r\n            case \"AES-ECB\":\r\n                return this.encryptAesECB(algorithm, key, Buffer.from(data));\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async decrypt(algorithm, key, data) {\r\n        if (!(key instanceof AesCryptoKey)) {\r\n            throw new Error(\"key: Is not AesCryptoKey\");\r\n        }\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"AES-CBC\":\r\n                return this.decryptAesCBC(algorithm, key, Buffer.from(data));\r\n            case \"AES-CTR\":\r\n                return this.decryptAesCTR(algorithm, key, Buffer.from(data));\r\n            case \"AES-GCM\":\r\n                return this.decryptAesGCM(algorithm, key, Buffer.from(data));\r\n            case \"AES-KW\":\r\n                return this.decryptAesKW(algorithm, key, Buffer.from(data));\r\n            case \"AES-ECB\":\r\n                return this.decryptAesECB(algorithm, key, Buffer.from(data));\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async encryptAesCBC(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`aes-${key.algorithm.length}-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptAesCBC(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`aes-${key.algorithm.length}-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptAesCTR(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`aes-${key.algorithm.length}-ctr`, key.data, Buffer.from(algorithm.counter));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptAesCTR(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`aes-${key.algorithm.length}-ctr`, key.data, new Uint8Array(algorithm.counter));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptAesGCM(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`aes-${key.algorithm.length}-gcm`, key.data, Buffer.from(algorithm.iv), {\r\n            authTagLength: (algorithm.tagLength || 128) >> 3,\r\n        });\r\n        if (algorithm.additionalData) {\r\n            cipher.setAAD(Buffer.from(algorithm.additionalData));\r\n        }\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final(), cipher.getAuthTag()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptAesGCM(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`aes-${key.algorithm.length}-gcm`, key.data, new Uint8Array(algorithm.iv));\r\n        const tagLength = (algorithm.tagLength || 128) >> 3;\r\n        const enc = data.slice(0, data.length - tagLength);\r\n        const tag = data.slice(data.length - tagLength);\r\n        if (algorithm.additionalData) {\r\n            decipher.setAAD(Buffer.from(algorithm.additionalData));\r\n        }\r\n        decipher.setAuthTag(tag);\r\n        let dec = decipher.update(enc);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptAesKW(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`id-aes${key.algorithm.length}-wrap`, key.data, this.AES_KW_IV);\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        return new Uint8Array(enc).buffer;\r\n    }\r\n    static async decryptAesKW(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`id-aes${key.algorithm.length}-wrap`, key.data, this.AES_KW_IV);\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptAesECB(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`aes-${key.algorithm.length}-ecb`, key.data, new Uint8Array(0));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptAesECB(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`aes-${key.algorithm.length}-ecb`, key.data, new Uint8Array(0));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n}\r\nAesCrypto.AES_KW_IV = Buffer.from(\"A6A6A6A6A6A6A6A6\", \"hex\");\n\nclass AesCbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCbcProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nconst zero = Buffer.from([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);\r\nconst rb = Buffer.from([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 135]);\r\nconst blockSize = 16;\r\nfunction bitShiftLeft(buffer) {\r\n    const shifted = Buffer.alloc(buffer.length);\r\n    const last = buffer.length - 1;\r\n    for (let index = 0; index < last; index++) {\r\n        shifted[index] = buffer[index] << 1;\r\n        if (buffer[index + 1] & 0x80) {\r\n            shifted[index] += 0x01;\r\n        }\r\n    }\r\n    shifted[last] = buffer[last] << 1;\r\n    return shifted;\r\n}\r\nfunction xor(a, b) {\r\n    const length = Math.min(a.length, b.length);\r\n    const output = Buffer.alloc(length);\r\n    for (let index = 0; index < length; index++) {\r\n        output[index] = a[index] ^ b[index];\r\n    }\r\n    return output;\r\n}\r\nfunction aes(key, message) {\r\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_1__.createCipheriv(`aes${key.length << 3}`, key, zero);\r\n    const result = cipher.update(message);\r\n    cipher.final();\r\n    return result;\r\n}\r\nfunction getMessageBlock(message, blockIndex) {\r\n    const block = Buffer.alloc(blockSize);\r\n    const start = blockIndex * blockSize;\r\n    const end = start + blockSize;\r\n    message.copy(block, 0, start, end);\r\n    return block;\r\n}\r\nfunction getPaddedMessageBlock(message, blockIndex) {\r\n    const block = Buffer.alloc(blockSize);\r\n    const start = blockIndex * blockSize;\r\n    const end = message.length;\r\n    block.fill(0);\r\n    message.copy(block, 0, start, end);\r\n    block[end - start] = 0x80;\r\n    return block;\r\n}\r\nfunction generateSubkeys(key) {\r\n    const l = aes(key, zero);\r\n    let subkey1 = bitShiftLeft(l);\r\n    if (l[0] & 0x80) {\r\n        subkey1 = xor(subkey1, rb);\r\n    }\r\n    let subkey2 = bitShiftLeft(subkey1);\r\n    if (subkey1[0] & 0x80) {\r\n        subkey2 = xor(subkey2, rb);\r\n    }\r\n    return { subkey1, subkey2 };\r\n}\r\nfunction aesCmac(key, message) {\r\n    const subkeys = generateSubkeys(key);\r\n    let blockCount = Math.ceil(message.length / blockSize);\r\n    let lastBlockCompleteFlag;\r\n    let lastBlock;\r\n    if (blockCount === 0) {\r\n        blockCount = 1;\r\n        lastBlockCompleteFlag = false;\r\n    }\r\n    else {\r\n        lastBlockCompleteFlag = (message.length % blockSize === 0);\r\n    }\r\n    const lastBlockIndex = blockCount - 1;\r\n    if (lastBlockCompleteFlag) {\r\n        lastBlock = xor(getMessageBlock(message, lastBlockIndex), subkeys.subkey1);\r\n    }\r\n    else {\r\n        lastBlock = xor(getPaddedMessageBlock(message, lastBlockIndex), subkeys.subkey2);\r\n    }\r\n    let x = zero;\r\n    let y;\r\n    for (let index = 0; index < lastBlockIndex; index++) {\r\n        y = xor(x, getMessageBlock(message, index));\r\n        x = aes(key, y);\r\n    }\r\n    y = xor(lastBlock, x);\r\n    return aes(key, y);\r\n}\r\nclass AesCmacProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCmacProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        const result = aesCmac(getCryptoKey(key).data, Buffer.from(data));\r\n        return new Uint8Array(result).buffer;\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        const signature2 = await this.sign(algorithm, key, data);\r\n        return Buffer.from(signature).compare(Buffer.from(signature2)) === 0;\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass AesCtrProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCtrProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass AesGcmProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesGcmProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass AesKwProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesKwProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass AesEcbProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesEcbProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass DesCryptoKey extends SymmetricKey {\r\n    get alg() {\r\n        switch (this.algorithm.name.toUpperCase()) {\r\n            case \"DES-CBC\":\r\n                return `DES-CBC`;\r\n            case \"DES-EDE3-CBC\":\r\n                return `3DES-CBC`;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AlgorithmError(\"Unsupported algorithm name\");\r\n        }\r\n    }\r\n    set alg(value) {\r\n    }\r\n}\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\r\n], DesCryptoKey.prototype, \"data\", void 0);\n\nclass DesCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const key = new DesCryptoKey();\r\n        key.algorithm = algorithm;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(algorithm.length >> 3);\r\n        return key;\r\n    }\r\n    static async exportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"raw\":\r\n                return new Uint8Array(key.data).buffer;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        let key;\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: DesCryptoKey });\r\n                break;\r\n            case \"raw\":\r\n                key = new DesCryptoKey();\r\n                key.data = Buffer.from(keyData);\r\n                break;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n        key.algorithm = algorithm;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static async encrypt(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"DES-CBC\":\r\n                return this.encryptDesCBC(algorithm, key, Buffer.from(data));\r\n            case \"DES-EDE3-CBC\":\r\n                return this.encryptDesEDE3CBC(algorithm, key, Buffer.from(data));\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async decrypt(algorithm, key, data) {\r\n        if (!(key instanceof DesCryptoKey)) {\r\n            throw new Error(\"key: Is not DesCryptoKey\");\r\n        }\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"DES-CBC\":\r\n                return this.decryptDesCBC(algorithm, key, Buffer.from(data));\r\n            case \"DES-EDE3-CBC\":\r\n                return this.decryptDesEDE3CBC(algorithm, key, Buffer.from(data));\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async encryptDesCBC(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`des-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptDesCBC(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`des-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptDesEDE3CBC(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`des-ede3-cbc`, key.data, Buffer.from(algorithm.iv));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptDesEDE3CBC(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`des-ede3-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n}\n\nclass DesCbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.DesProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.keySizeBits = 64;\r\n        this.ivSize = 8;\r\n        this.name = \"DES-CBC\";\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await DesCrypto.generateKey({\r\n            name: this.name,\r\n            length: this.keySizeBits,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return DesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return DesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return DesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await DesCrypto.importKey(format, keyData, { name: this.name, length: this.keySizeBits }, extractable, keyUsages);\r\n        if (key.data.length !== (this.keySizeBits >> 3)) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Wrong key size\");\r\n        }\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof DesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a DesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass DesEde3CbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.DesProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.keySizeBits = 192;\r\n        this.ivSize = 8;\r\n        this.name = \"DES-EDE3-CBC\";\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await DesCrypto.generateKey({\r\n            name: this.name,\r\n            length: this.keySizeBits,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return DesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return DesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return DesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await DesCrypto.importKey(format, keyData, { name: this.name, length: this.keySizeBits }, extractable, keyUsages);\r\n        if (key.data.length !== (this.keySizeBits >> 3)) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Wrong key size\");\r\n        }\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof DesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a DesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nfunction getJwkAlgorithm(algorithm) {\r\n    switch (algorithm.name.toUpperCase()) {\r\n        case \"RSA-OAEP\": {\r\n            const mdSize = /(\\d+)$/.exec(algorithm.hash.name)[1];\r\n            return `RSA-OAEP${mdSize !== \"1\" ? `-${mdSize}` : \"\"}`;\r\n        }\r\n        case \"RSASSA-PKCS1-V1_5\":\r\n            return `RS${/(\\d+)$/.exec(algorithm.hash.name)[1]}`;\r\n        case \"RSA-PSS\":\r\n            return `PS${/(\\d+)$/.exec(algorithm.hash.name)[1]}`;\r\n        case \"RSA-PKCS1\":\r\n            return `RS1`;\r\n        default:\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n    }\r\n}\n\nclass RsaPrivateKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"private\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"RSA\",\r\n            alg: getJwkAlgorithm(this.algorithm),\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey });\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\r\n        keyInfo.privateKeyAlgorithm.parameters = null;\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(key);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n    }\r\n}\n\nclass RsaPublicKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"public\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.publicKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"RSA\",\r\n            alg: getJwkAlgorithm(this.algorithm),\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey });\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\r\n        keyInfo.publicKeyAlgorithm.parameters = null;\r\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(key);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n    }\r\n}\n\nclass RsaCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const privateKey = new RsaPrivateKey();\r\n        privateKey.algorithm = algorithm;\r\n        privateKey.extractable = extractable;\r\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\r\n        const publicKey = new RsaPublicKey();\r\n        publicKey.algorithm = algorithm;\r\n        publicKey.extractable = true;\r\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\r\n        const publicExponent = Buffer.concat([\r\n            Buffer.alloc(4 - algorithm.publicExponent.byteLength, 0),\r\n            Buffer.from(algorithm.publicExponent),\r\n        ]).readInt32BE(0);\r\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_1___default().generateKeyPairSync(\"rsa\", {\r\n            modulusLength: algorithm.modulusLength,\r\n            publicExponent,\r\n            publicKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"spki\",\r\n            },\r\n            privateKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"pkcs8\",\r\n            },\r\n        });\r\n        privateKey.data = keys.privateKey;\r\n        publicKey.data = keys.publicKey;\r\n        const res = {\r\n            privateKey,\r\n            publicKey,\r\n        };\r\n        return res;\r\n    }\r\n    static async exportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"pkcs8\":\r\n            case \"spki\":\r\n                return new Uint8Array(key.data).buffer;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\": {\r\n                const jwk = keyData;\r\n                if (jwk.d) {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey });\r\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n                else {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey });\r\n                    return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n            }\r\n            case \"spki\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.publicKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey);\r\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"pkcs8\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey);\r\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static async sign(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"RSA-PSS\":\r\n            case \"RSASSA-PKCS1-V1_5\":\r\n                return this.signRsa(algorithm, key, data);\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async verify(algorithm, key, signature, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"RSA-PSS\":\r\n            case \"RSASSA-PKCS1-V1_5\":\r\n                return this.verifySSA(algorithm, key, data, signature);\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async encrypt(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"RSA-OAEP\":\r\n                return this.encryptOAEP(algorithm, key, data);\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async decrypt(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"RSA-OAEP\":\r\n                return this.decryptOAEP(algorithm, key, data);\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\r\n        keyInfo.privateKeyAlgorithm.parameters = null;\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(asnKey);\r\n        const key = new RsaPrivateKey();\r\n        key.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.algorithm.publicExponent = new Uint8Array(asnKey.publicExponent);\r\n        key.algorithm.modulusLength = asnKey.modulus.byteLength << 3;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static importPublicKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\r\n        keyInfo.publicKeyAlgorithm.parameters = null;\r\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(asnKey);\r\n        const key = new RsaPublicKey();\r\n        key.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.algorithm.publicExponent = new Uint8Array(asnKey.publicExponent);\r\n        key.algorithm.modulusLength = asnKey.modulus.byteLength << 3;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static getCryptoAlgorithm(alg) {\r\n        switch (alg.hash.name.toUpperCase()) {\r\n            case \"SHA-1\":\r\n                return \"RSA-SHA1\";\r\n            case \"SHA-256\":\r\n                return \"RSA-SHA256\";\r\n            case \"SHA-384\":\r\n                return \"RSA-SHA384\";\r\n            case \"SHA-512\":\r\n                return \"RSA-SHA512\";\r\n            case \"SHA3-256\":\r\n                return \"RSA-SHA3-256\";\r\n            case \"SHA3-384\":\r\n                return \"RSA-SHA3-384\";\r\n            case \"SHA3-512\":\r\n                return \"RSA-SHA3-512\";\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm.hash: Is not recognized\");\r\n        }\r\n    }\r\n    static signRsa(algorithm, key, data) {\r\n        const cryptoAlg = this.getCryptoAlgorithm(key.algorithm);\r\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_1___default().createSign(cryptoAlg);\r\n        signer.update(Buffer.from(data));\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        if (algorithm.name.toUpperCase() === \"RSA-PSS\") {\r\n            options.padding = (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_PKCS1_PSS_PADDING;\r\n            options.saltLength = algorithm.saltLength;\r\n        }\r\n        const signature = signer.sign(options);\r\n        return new Uint8Array(signature).buffer;\r\n    }\r\n    static verifySSA(algorithm, key, data, signature) {\r\n        const cryptoAlg = this.getCryptoAlgorithm(key.algorithm);\r\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_1___default().createVerify(cryptoAlg);\r\n        signer.update(Buffer.from(data));\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        if (algorithm.name.toUpperCase() === \"RSA-PSS\") {\r\n            options.padding = (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_PKCS1_PSS_PADDING;\r\n            options.saltLength = algorithm.saltLength;\r\n        }\r\n        const ok = signer.verify(options, signature);\r\n        return ok;\r\n    }\r\n    static encryptOAEP(algorithm, key, data) {\r\n        const options = {\r\n            key: `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`,\r\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_PKCS1_OAEP_PADDING,\r\n        };\r\n        if (algorithm.label) ;\r\n        return new Uint8Array(crypto__WEBPACK_IMPORTED_MODULE_1___default().publicEncrypt(options, data)).buffer;\r\n    }\r\n    static decryptOAEP(algorithm, key, data) {\r\n        const options = {\r\n            key: `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`,\r\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_PKCS1_OAEP_PADDING,\r\n        };\r\n        if (algorithm.label) ;\r\n        return new Uint8Array(crypto__WEBPACK_IMPORTED_MODULE_1___default().privateDecrypt(options, data)).buffer;\r\n    }\r\n}\r\nRsaCrypto.publicKeyUsages = [\"verify\", \"encrypt\", \"wrapKey\"];\r\nRsaCrypto.privateKeyUsages = [\"sign\", \"decrypt\", \"unwrapKey\"];\n\nclass RsaSsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaSsaProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.hashAlgorithms = [\r\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\r\n            \"shake128\", \"shake256\",\r\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\r\n        ];\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await RsaCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        return RsaCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        return RsaCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\r\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass RsaPssProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaPssProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.hashAlgorithms = [\r\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\r\n            \"shake128\", \"shake256\",\r\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\r\n        ];\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await RsaCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        return RsaCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        return RsaCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\r\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass ShaCrypto {\r\n    static size(algorithm) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"SHA-1\":\r\n                return 160;\r\n            case \"SHA-256\":\r\n            case \"SHA3-256\":\r\n                return 256;\r\n            case \"SHA-384\":\r\n            case \"SHA3-384\":\r\n                return 384;\r\n            case \"SHA-512\":\r\n            case \"SHA3-512\":\r\n                return 512;\r\n            default:\r\n                throw new Error(\"Unrecognized name\");\r\n        }\r\n    }\r\n    static getAlgorithmName(algorithm) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"SHA-1\":\r\n                return \"sha1\";\r\n            case \"SHA-256\":\r\n                return \"sha256\";\r\n            case \"SHA-384\":\r\n                return \"sha384\";\r\n            case \"SHA-512\":\r\n                return \"sha512\";\r\n            case \"SHA3-256\":\r\n                return \"sha3-256\";\r\n            case \"SHA3-384\":\r\n                return \"sha3-384\";\r\n            case \"SHA3-512\":\r\n                return \"sha3-512\";\r\n            default:\r\n                throw new Error(\"Unrecognized name\");\r\n        }\r\n    }\r\n    static digest(algorithm, data) {\r\n        const hashAlg = this.getAlgorithmName(algorithm);\r\n        const hash = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(hashAlg)\r\n            .update(Buffer.from(data)).digest();\r\n        return new Uint8Array(hash).buffer;\r\n    }\r\n}\n\nclass RsaOaepProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaOaepProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await RsaCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        const internalKey = getCryptoKey(key);\r\n        const dataView = new Uint8Array(data);\r\n        const keySize = Math.ceil(internalKey.algorithm.modulusLength >> 3);\r\n        const hashSize = ShaCrypto.size(internalKey.algorithm.hash) >> 3;\r\n        const dataLength = dataView.byteLength;\r\n        const psLength = keySize - dataLength - 2 * hashSize - 2;\r\n        if (dataLength > keySize - 2 * hashSize - 2) {\r\n            throw new Error(\"Data too large\");\r\n        }\r\n        const message = new Uint8Array(keySize);\r\n        const seed = message.subarray(1, hashSize + 1);\r\n        const dataBlock = message.subarray(hashSize + 1);\r\n        dataBlock.set(dataView, hashSize + psLength + 1);\r\n        const labelHash = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(internalKey.algorithm.hash.name.replace(\"-\", \"\"))\r\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(algorithm.label || new Uint8Array(0)))\r\n            .digest();\r\n        dataBlock.set(labelHash, 0);\r\n        dataBlock[hashSize + psLength] = 1;\r\n        crypto__WEBPACK_IMPORTED_MODULE_1___default().randomFillSync(seed);\r\n        const dataBlockMask = this.mgf1(internalKey.algorithm.hash, seed, dataBlock.length);\r\n        for (let i = 0; i < dataBlock.length; i++) {\r\n            dataBlock[i] ^= dataBlockMask[i];\r\n        }\r\n        const seedMask = this.mgf1(internalKey.algorithm.hash, dataBlock, seed.length);\r\n        for (let i = 0; i < seed.length; i++) {\r\n            seed[i] ^= seedMask[i];\r\n        }\r\n        if (!internalKey.pem) {\r\n            internalKey.pem = `-----BEGIN PUBLIC KEY-----\\n${internalKey.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\r\n        }\r\n        const pkcs0 = crypto__WEBPACK_IMPORTED_MODULE_1___default().publicEncrypt({\r\n            key: internalKey.pem,\r\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_NO_PADDING,\r\n        }, Buffer.from(message));\r\n        return new Uint8Array(pkcs0).buffer;\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        const internalKey = getCryptoKey(key);\r\n        const keySize = Math.ceil(internalKey.algorithm.modulusLength >> 3);\r\n        const hashSize = ShaCrypto.size(internalKey.algorithm.hash) >> 3;\r\n        const dataLength = data.byteLength;\r\n        if (dataLength !== keySize) {\r\n            throw new Error(\"Bad data\");\r\n        }\r\n        if (!internalKey.pem) {\r\n            internalKey.pem = `-----BEGIN PRIVATE KEY-----\\n${internalKey.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\r\n        }\r\n        let pkcs0 = crypto__WEBPACK_IMPORTED_MODULE_1___default().privateDecrypt({\r\n            key: internalKey.pem,\r\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_NO_PADDING,\r\n        }, Buffer.from(data));\r\n        const z = pkcs0[0];\r\n        const seed = pkcs0.subarray(1, hashSize + 1);\r\n        const dataBlock = pkcs0.subarray(hashSize + 1);\r\n        if (z !== 0) {\r\n            throw new Error(\"Decryption failed\");\r\n        }\r\n        const seedMask = this.mgf1(internalKey.algorithm.hash, dataBlock, seed.length);\r\n        for (let i = 0; i < seed.length; i++) {\r\n            seed[i] ^= seedMask[i];\r\n        }\r\n        const dataBlockMask = this.mgf1(internalKey.algorithm.hash, seed, dataBlock.length);\r\n        for (let i = 0; i < dataBlock.length; i++) {\r\n            dataBlock[i] ^= dataBlockMask[i];\r\n        }\r\n        const labelHash = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(internalKey.algorithm.hash.name.replace(\"-\", \"\"))\r\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(algorithm.label || new Uint8Array(0)))\r\n            .digest();\r\n        for (let i = 0; i < hashSize; i++) {\r\n            if (labelHash[i] !== dataBlock[i]) {\r\n                throw new Error(\"Decryption failed\");\r\n            }\r\n        }\r\n        let psEnd = hashSize;\r\n        for (; psEnd < dataBlock.length; psEnd++) {\r\n            const psz = dataBlock[psEnd];\r\n            if (psz === 1) {\r\n                break;\r\n            }\r\n            if (psz !== 0) {\r\n                throw new Error(\"Decryption failed\");\r\n            }\r\n        }\r\n        if (psEnd === dataBlock.length) {\r\n            throw new Error(\"Decryption failed\");\r\n        }\r\n        pkcs0 = dataBlock.subarray(psEnd + 1);\r\n        return new Uint8Array(pkcs0).buffer;\r\n    }\r\n    async onExportKey(format, key) {\r\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\r\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\r\n        }\r\n    }\r\n    mgf1(algorithm, seed, length = 0) {\r\n        const hashSize = ShaCrypto.size(algorithm) >> 3;\r\n        const mask = new Uint8Array(length);\r\n        const counter = new Uint8Array(4);\r\n        const chunks = Math.ceil(length / hashSize);\r\n        for (let i = 0; i < chunks; i++) {\r\n            counter[0] = i >>> 24;\r\n            counter[1] = (i >>> 16) & 255;\r\n            counter[2] = (i >>> 8) & 255;\r\n            counter[3] = i & 255;\r\n            const submask = mask.subarray(i * hashSize);\r\n            let chunk = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(algorithm.name.replace(\"-\", \"\"))\r\n                .update(seed)\r\n                .update(counter)\r\n                .digest();\r\n            if (chunk.length > submask.length) {\r\n                chunk = chunk.subarray(0, submask.length);\r\n            }\r\n            submask.set(chunk);\r\n        }\r\n        return mask;\r\n    }\r\n}\n\nclass RsaEsProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"RSAES-PKCS1-v1_5\";\r\n        this.usages = {\r\n            publicKey: [\"encrypt\", \"wrapKey\"],\r\n            privateKey: [\"decrypt\", \"unwrapKey\"],\r\n        };\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await RsaCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    checkGenerateKeyParams(algorithm) {\r\n        this.checkRequiredProperty(algorithm, \"publicExponent\");\r\n        if (!(algorithm.publicExponent && algorithm.publicExponent instanceof Uint8Array)) {\r\n            throw new TypeError(\"publicExponent: Missing or not a Uint8Array\");\r\n        }\r\n        const publicExponent = pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64(algorithm.publicExponent);\r\n        if (!(publicExponent === \"Aw==\" || publicExponent === \"AQAB\")) {\r\n            throw new TypeError(\"publicExponent: Must be [3] or [1,0,1]\");\r\n        }\r\n        this.checkRequiredProperty(algorithm, \"modulusLength\");\r\n        switch (algorithm.modulusLength) {\r\n            case 1024:\r\n            case 2048:\r\n            case 4096:\r\n                break;\r\n            default:\r\n                throw new TypeError(\"modulusLength: Must be 1024, 2048, or 4096\");\r\n        }\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        const options = this.toCryptoOptions(key);\r\n        const enc = crypto__WEBPACK_IMPORTED_MODULE_1__.publicEncrypt(options, new Uint8Array(data));\r\n        return new Uint8Array(enc).buffer;\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        const options = this.toCryptoOptions(key);\r\n        const dec = crypto__WEBPACK_IMPORTED_MODULE_1__.privateDecrypt(options, new Uint8Array(data));\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    async onExportKey(format, key) {\r\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\r\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\r\n        }\r\n    }\r\n    toCryptoOptions(key) {\r\n        const type = key.type.toUpperCase();\r\n        return {\r\n            key: `-----BEGIN ${type} KEY-----\\n${getCryptoKey(key).data.toString(\"base64\")}\\n-----END ${type} KEY-----`,\r\n            padding: crypto__WEBPACK_IMPORTED_MODULE_1__.constants.RSA_PKCS1_PADDING,\r\n        };\r\n    }\r\n}\n\nconst namedOIDs = {\r\n    \"1.2.840.10045.3.1.7\": \"P-256\",\r\n    \"P-256\": \"1.2.840.10045.3.1.7\",\r\n    \"1.3.132.0.34\": \"P-384\",\r\n    \"P-384\": \"1.3.132.0.34\",\r\n    \"1.3.132.0.35\": \"P-521\",\r\n    \"P-521\": \"1.3.132.0.35\",\r\n    \"1.3.132.0.10\": \"K-256\",\r\n    \"K-256\": \"1.3.132.0.10\",\r\n    \"brainpoolP160r1\": \"1.3.36.3.3.2.8.1.1.1\",\r\n    \"1.3.36.3.3.2.8.1.1.1\": \"brainpoolP160r1\",\r\n    \"brainpoolP160t1\": \"1.3.36.3.3.2.8.1.1.2\",\r\n    \"1.3.36.3.3.2.8.1.1.2\": \"brainpoolP160t1\",\r\n    \"brainpoolP192r1\": \"1.3.36.3.3.2.8.1.1.3\",\r\n    \"1.3.36.3.3.2.8.1.1.3\": \"brainpoolP192r1\",\r\n    \"brainpoolP192t1\": \"1.3.36.3.3.2.8.1.1.4\",\r\n    \"1.3.36.3.3.2.8.1.1.4\": \"brainpoolP192t1\",\r\n    \"brainpoolP224r1\": \"1.3.36.3.3.2.8.1.1.5\",\r\n    \"1.3.36.3.3.2.8.1.1.5\": \"brainpoolP224r1\",\r\n    \"brainpoolP224t1\": \"1.3.36.3.3.2.8.1.1.6\",\r\n    \"1.3.36.3.3.2.8.1.1.6\": \"brainpoolP224t1\",\r\n    \"brainpoolP256r1\": \"1.3.36.3.3.2.8.1.1.7\",\r\n    \"1.3.36.3.3.2.8.1.1.7\": \"brainpoolP256r1\",\r\n    \"brainpoolP256t1\": \"1.3.36.3.3.2.8.1.1.8\",\r\n    \"1.3.36.3.3.2.8.1.1.8\": \"brainpoolP256t1\",\r\n    \"brainpoolP320r1\": \"1.3.36.3.3.2.8.1.1.9\",\r\n    \"1.3.36.3.3.2.8.1.1.9\": \"brainpoolP320r1\",\r\n    \"brainpoolP320t1\": \"1.3.36.3.3.2.8.1.1.10\",\r\n    \"1.3.36.3.3.2.8.1.1.10\": \"brainpoolP320t1\",\r\n    \"brainpoolP384r1\": \"1.3.36.3.3.2.8.1.1.11\",\r\n    \"1.3.36.3.3.2.8.1.1.11\": \"brainpoolP384r1\",\r\n    \"brainpoolP384t1\": \"1.3.36.3.3.2.8.1.1.12\",\r\n    \"1.3.36.3.3.2.8.1.1.12\": \"brainpoolP384t1\",\r\n    \"brainpoolP512r1\": \"1.3.36.3.3.2.8.1.1.13\",\r\n    \"1.3.36.3.3.2.8.1.1.13\": \"brainpoolP512r1\",\r\n    \"brainpoolP512t1\": \"1.3.36.3.3.2.8.1.1.14\",\r\n    \"1.3.36.3.3.2.8.1.1.14\": \"brainpoolP512t1\",\r\n};\r\nfunction getOidByNamedCurve$1(namedCurve) {\r\n    const oid = namedOIDs[namedCurve];\r\n    if (!oid) {\r\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot convert WebCrypto named curve '${namedCurve}' to OID`);\r\n    }\r\n    return oid;\r\n}\n\nclass EcPrivateKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"private\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"EC\",\r\n            crv: this.algorithm.namedCurve,\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        if (!json.crv) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\r\n        }\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\r\n        keyInfo.privateKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(json.crv)));\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey });\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(key);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        return this;\r\n    }\r\n}\n\nclass EcPublicKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"public\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n        return new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyInfo.publicKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"EC\",\r\n            crv: this.algorithm.namedCurve,\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        if (!json.crv) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\r\n        }\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey });\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\r\n        keyInfo.publicKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(json.crv)));\r\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.toASN(key).valueHex;\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        return this;\r\n    }\r\n}\n\nclass Sha1Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA-1\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA-256\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha384Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA-384\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha512Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA-512\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha3256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA3-256\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha3384Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA3-384\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha3512Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA3-512\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass EcCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const privateKey = new EcPrivateKey();\r\n        privateKey.algorithm = algorithm;\r\n        privateKey.extractable = extractable;\r\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\r\n        const publicKey = new EcPublicKey();\r\n        publicKey.algorithm = algorithm;\r\n        publicKey.extractable = true;\r\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\r\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_1___default().generateKeyPairSync(\"ec\", {\r\n            namedCurve: this.getOpenSSLNamedCurve(algorithm.namedCurve),\r\n            publicKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"spki\",\r\n            },\r\n            privateKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"pkcs8\",\r\n            },\r\n        });\r\n        privateKey.data = keys.privateKey;\r\n        publicKey.data = keys.publicKey;\r\n        const res = {\r\n            privateKey,\r\n            publicKey,\r\n        };\r\n        return res;\r\n    }\r\n    static async sign(algorithm, key, data) {\r\n        const cryptoAlg = ShaCrypto.getAlgorithmName(algorithm.hash);\r\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_1___default().createSign(cryptoAlg);\r\n        signer.update(Buffer.from(data));\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        const signature = signer.sign(options);\r\n        const ecSignature = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(signature, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcDsaSignature);\r\n        const signatureRaw = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcUtils.encodeSignature(ecSignature, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.get(key.algorithm.namedCurve).size);\r\n        return signatureRaw.buffer;\r\n    }\r\n    static async verify(algorithm, key, signature, data) {\r\n        const cryptoAlg = ShaCrypto.getAlgorithmName(algorithm.hash);\r\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_1___default().createVerify(cryptoAlg);\r\n        signer.update(Buffer.from(data));\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        const ecSignature = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcDsaSignature();\r\n        const namedCurve = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.get(key.algorithm.namedCurve);\r\n        const signaturePoint = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcUtils.decodeSignature(signature, namedCurve.size);\r\n        ecSignature.r = pvtsutils__WEBPACK_IMPORTED_MODULE_4__.BufferSourceConverter.toArrayBuffer(signaturePoint.r);\r\n        ecSignature.s = pvtsutils__WEBPACK_IMPORTED_MODULE_4__.BufferSourceConverter.toArrayBuffer(signaturePoint.s);\r\n        const ecSignatureRaw = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(ecSignature));\r\n        const ok = signer.verify(options, ecSignatureRaw);\r\n        return ok;\r\n    }\r\n    static async deriveBits(algorithm, baseKey, length) {\r\n        const cryptoAlg = this.getOpenSSLNamedCurve(baseKey.algorithm.namedCurve);\r\n        const ecdh = crypto__WEBPACK_IMPORTED_MODULE_1___default().createECDH(cryptoAlg);\r\n        const asnPrivateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(baseKey.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n        const asnEcPrivateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(asnPrivateKey.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\r\n        ecdh.setPrivateKey(Buffer.from(asnEcPrivateKey.privateKey));\r\n        const asnPublicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(algorithm.public.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n        const bits = ecdh.computeSecret(Buffer.from(asnPublicKey.publicKey));\r\n        if (length === null) {\r\n            return bits;\r\n        }\r\n        return new Uint8Array(bits).buffer.slice(0, length >> 3);\r\n    }\r\n    static async exportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"pkcs8\":\r\n            case \"spki\":\r\n                return new Uint8Array(key.data).buffer;\r\n            case \"raw\": {\r\n                const publicKeyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(key.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                return publicKeyInfo.publicKey;\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\": {\r\n                const jwk = keyData;\r\n                if (jwk.d) {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey });\r\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n                else {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey });\r\n                    return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n            }\r\n            case \"raw\": {\r\n                const asnKey = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyData);\r\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"spki\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                const asnKey = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyInfo.publicKey);\r\n                this.assertKeyParameters(keyInfo.publicKeyAlgorithm.parameters, algorithm.namedCurve);\r\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"pkcs8\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\r\n                this.assertKeyParameters(keyInfo.privateKeyAlgorithm.parameters, algorithm.namedCurve);\r\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', 'pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static assertKeyParameters(parameters, namedCurve) {\r\n        if (!parameters) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Key info doesn't have required parameters\");\r\n        }\r\n        let namedCurveIdentifier = \"\";\r\n        try {\r\n            namedCurveIdentifier = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(parameters, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier).value;\r\n        }\r\n        catch (e) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Cannot read key info parameters\");\r\n        }\r\n        if (getOidByNamedCurve$1(namedCurve) !== namedCurveIdentifier) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Key info parameter doesn't match to named curve\");\r\n        }\r\n    }\r\n    static async importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\r\n        keyInfo.privateKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(algorithm.namedCurve)));\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(asnKey);\r\n        const key = new EcPrivateKey();\r\n        key.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static async importPublicKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\r\n        const namedCurve = getOidByNamedCurve$1(algorithm.namedCurve);\r\n        keyInfo.publicKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(namedCurve));\r\n        keyInfo.publicKey = asnKey.value;\r\n        const key = new EcPublicKey();\r\n        key.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static getOpenSSLNamedCurve(curve) {\r\n        switch (curve.toUpperCase()) {\r\n            case \"P-256\":\r\n                return \"prime256v1\";\r\n            case \"K-256\":\r\n                return \"secp256k1\";\r\n            case \"P-384\":\r\n                return \"secp384r1\";\r\n            case \"P-521\":\r\n                return \"secp521r1\";\r\n            default:\r\n                return curve;\r\n        }\r\n    }\r\n}\r\nEcCrypto.publicKeyUsages = [\"verify\"];\r\nEcCrypto.privateKeyUsages = [\"sign\", \"deriveKey\", \"deriveBits\"];\n\nclass EcdsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdsaProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.namedCurves = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.names;\r\n        this.hashAlgorithms = [\r\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\r\n            \"shake128\", \"shake256\",\r\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\r\n        ];\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await EcCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        return EcCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        return EcCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return EcCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await EcCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof EcPrivateKey || internalKey instanceof EcPublicKey)) {\r\n            throw new TypeError(\"key: Is not EC CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass EcdhProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdhProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.namedCurves = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.names;\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await EcCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onExportKey(format, key) {\r\n        return EcCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await EcCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof EcPrivateKey || internalKey instanceof EcPublicKey)) {\r\n            throw new TypeError(\"key: Is not EC CryptoKey\");\r\n        }\r\n    }\r\n    async onDeriveBits(algorithm, baseKey, length) {\r\n        const bits = await EcCrypto.deriveBits({ ...algorithm, public: getCryptoKey(algorithm.public) }, getCryptoKey(baseKey), length);\r\n        return bits;\r\n    }\r\n}\n\nconst edOIDs = {\r\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd448]: \"Ed448\",\r\n    \"ed448\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd448,\r\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX448]: \"X448\",\r\n    \"x448\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX448,\r\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd25519]: \"Ed25519\",\r\n    \"ed25519\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd25519,\r\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX25519]: \"X25519\",\r\n    \"x25519\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX25519,\r\n};\r\nfunction getOidByNamedCurve(namedCurve) {\r\n    const oid = edOIDs[namedCurve.toLowerCase()];\r\n    if (!oid) {\r\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot convert WebCrypto named curve '${namedCurve}' to OID`);\r\n    }\r\n    return oid;\r\n}\n\nclass EdPrivateKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"private\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"OKP\",\r\n            crv: this.algorithm.namedCurve,\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        if (!json.crv) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\r\n        }\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = getOidByNamedCurve(json.crv);\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey });\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(key);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        return this;\r\n    }\r\n}\n\nclass EdPublicKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"public\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n        return keyInfo.publicKey;\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"OKP\",\r\n            crv: this.algorithm.namedCurve,\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, {\r\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64Url(key)\r\n        });\r\n    }\r\n    fromJSON(json) {\r\n        if (!json.crv) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\r\n        }\r\n        if (!json.x) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get property from JWK. Property 'x' is required`);\r\n        }\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = getOidByNamedCurve(json.crv);\r\n        keyInfo.publicKey = pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.FromBase64Url(json.x);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        return this;\r\n    }\r\n}\n\nclass EdCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const privateKey = new EdPrivateKey();\r\n        privateKey.algorithm = algorithm;\r\n        privateKey.extractable = extractable;\r\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\r\n        const publicKey = new EdPublicKey();\r\n        publicKey.algorithm = algorithm;\r\n        publicKey.extractable = true;\r\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\r\n        const type = algorithm.namedCurve.toLowerCase();\r\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_1___default().generateKeyPairSync(type, {\r\n            publicKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"spki\",\r\n            },\r\n            privateKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"pkcs8\",\r\n            },\r\n        });\r\n        privateKey.data = keys.privateKey;\r\n        publicKey.data = keys.publicKey;\r\n        const res = {\r\n            privateKey,\r\n            publicKey,\r\n        };\r\n        return res;\r\n    }\r\n    static async sign(algorithm, key, data) {\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        const signature = crypto__WEBPACK_IMPORTED_MODULE_1___default().sign(null, Buffer.from(data), options);\r\n        return webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(signature);\r\n    }\r\n    static async verify(algorithm, key, signature, data) {\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        const ok = crypto__WEBPACK_IMPORTED_MODULE_1___default().verify(null, Buffer.from(data), options, Buffer.from(signature));\r\n        return ok;\r\n    }\r\n    static async deriveBits(algorithm, baseKey, length) {\r\n        const publicKey = crypto__WEBPACK_IMPORTED_MODULE_1___default().createPublicKey({\r\n            key: algorithm.public.data,\r\n            format: \"der\",\r\n            type: \"spki\",\r\n        });\r\n        const privateKey = crypto__WEBPACK_IMPORTED_MODULE_1___default().createPrivateKey({\r\n            key: baseKey.data,\r\n            format: \"der\",\r\n            type: \"pkcs8\",\r\n        });\r\n        const bits = crypto__WEBPACK_IMPORTED_MODULE_1___default().diffieHellman({\r\n            publicKey,\r\n            privateKey,\r\n        });\r\n        return new Uint8Array(bits).buffer.slice(0, length >> 3);\r\n    }\r\n    static async exportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"pkcs8\":\r\n            case \"spki\":\r\n                return new Uint8Array(key.data).buffer;\r\n            case \"raw\": {\r\n                const publicKeyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(key.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                return publicKeyInfo.publicKey;\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\": {\r\n                const jwk = keyData;\r\n                if (jwk.d) {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey });\r\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n                else {\r\n                    if (!jwk.x) {\r\n                        throw new TypeError(\"keyData: Cannot get required 'x' filed\");\r\n                    }\r\n                    return this.importPublicKey(pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.FromBase64Url(jwk.x), algorithm, extractable, keyUsages);\r\n                }\r\n            }\r\n            case \"raw\": {\r\n                return this.importPublicKey(keyData, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"spki\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                return this.importPublicKey(keyInfo.publicKey, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"pkcs8\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey);\r\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', 'pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const key = new EdPrivateKey();\r\n        key.fromJSON({\r\n            crv: algorithm.namedCurve,\r\n            d: pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64Url(asnKey.d),\r\n        });\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static async importPublicKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const key = new EdPublicKey();\r\n        key.fromJSON({\r\n            crv: algorithm.namedCurve,\r\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64Url(asnKey),\r\n        });\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n}\r\nEdCrypto.publicKeyUsages = [\"verify\"];\r\nEdCrypto.privateKeyUsages = [\"sign\", \"deriveKey\", \"deriveBits\"];\n\nclass EdDsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EdDsaProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await EdCrypto.generateKey({\r\n            name: this.name,\r\n            namedCurve: algorithm.namedCurve.replace(/^ed/i, \"Ed\"),\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        return EdCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        return EdCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return EdCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await EdCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n}\n\nclass EcdhEsProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdhEsProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await EdCrypto.generateKey({\r\n            name: this.name,\r\n            namedCurve: algorithm.namedCurve.toUpperCase(),\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onDeriveBits(algorithm, baseKey, length) {\r\n        const bits = await EdCrypto.deriveBits({ ...algorithm, public: getCryptoKey(algorithm.public) }, getCryptoKey(baseKey), length);\r\n        return bits;\r\n    }\r\n    async onExportKey(format, key) {\r\n        return EdCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await EdCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n}\n\nclass PbkdfCryptoKey extends CryptoKey {\r\n}\n\nclass Pbkdf2Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Pbkdf2Provider {\r\n    async onDeriveBits(algorithm, baseKey, length) {\r\n        return new Promise((resolve, reject) => {\r\n            const salt = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(algorithm.salt);\r\n            const hash = algorithm.hash.name.replace(\"-\", \"\");\r\n            crypto__WEBPACK_IMPORTED_MODULE_1___default().pbkdf2(getCryptoKey(baseKey).data, Buffer.from(salt), algorithm.iterations, length >> 3, hash, (err, derivedBits) => {\r\n                if (err) {\r\n                    reject(err);\r\n                }\r\n                else {\r\n                    resolve(new Uint8Array(derivedBits).buffer);\r\n                }\r\n            });\r\n        });\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        if (format === \"raw\") {\r\n            const key = new PbkdfCryptoKey();\r\n            key.data = Buffer.from(keyData);\r\n            key.algorithm = { name: this.name };\r\n            key.extractable = false;\r\n            key.usages = keyUsages;\r\n            return setCryptoKey(key);\r\n        }\r\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'raw'\");\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof PbkdfCryptoKey)) {\r\n            throw new TypeError(\"key: Is not PBKDF CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass HmacCryptoKey extends CryptoKey {\r\n    get alg() {\r\n        const hash = this.algorithm.hash.name.toUpperCase();\r\n        return `HS${hash.replace(\"SHA-\", \"\")}`;\r\n    }\r\n    set alg(value) {\r\n    }\r\n}\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\r\n], HmacCryptoKey.prototype, \"data\", void 0);\n\nclass HmacProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.HmacProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const length = (algorithm.length || this.getDefaultLength(algorithm.hash.name)) >> 3 << 3;\r\n        const key = new HmacCryptoKey();\r\n        key.algorithm = {\r\n            ...algorithm,\r\n            length,\r\n            name: this.name,\r\n        };\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(length >> 3);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        const cryptoAlg = ShaCrypto.getAlgorithmName(key.algorithm.hash);\r\n        const hmac = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHmac(cryptoAlg, getCryptoKey(key).data)\r\n            .update(Buffer.from(data)).digest();\r\n        return new Uint8Array(hmac).buffer;\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        const cryptoAlg = ShaCrypto.getAlgorithmName(key.algorithm.hash);\r\n        const hmac = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHmac(cryptoAlg, getCryptoKey(key).data)\r\n            .update(Buffer.from(data)).digest();\r\n        return hmac.compare(Buffer.from(signature)) === 0;\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        let key;\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: HmacCryptoKey });\r\n                break;\r\n            case \"raw\":\r\n                key = new HmacCryptoKey();\r\n                key.data = Buffer.from(keyData);\r\n                break;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n        key.algorithm = {\r\n            hash: { name: algorithm.hash.name },\r\n            name: this.name,\r\n            length: key.data.length << 3,\r\n        };\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return setCryptoKey(key);\r\n    }\r\n    async onExportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(getCryptoKey(key));\r\n            case \"raw\":\r\n                return new Uint8Array(getCryptoKey(key).data).buffer;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof HmacCryptoKey)) {\r\n            throw new TypeError(\"key: Is not HMAC CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass HkdfCryptoKey extends CryptoKey {\r\n}\n\nclass HkdfProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.HkdfProvider {\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        if (format.toLowerCase() !== \"raw\") {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"Operation not supported\");\r\n        }\r\n        const key = new HkdfCryptoKey();\r\n        key.data = Buffer.from(keyData);\r\n        key.algorithm = { name: this.name };\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return setCryptoKey(key);\r\n    }\r\n    async onDeriveBits(params, baseKey, length) {\r\n        const hash = params.hash.name.replace(\"-\", \"\");\r\n        const hashLength = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(hash).digest().length;\r\n        const byteLength = length / 8;\r\n        const info = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.info);\r\n        const PRK = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHmac(hash, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.salt))\r\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(getCryptoKey(baseKey).data))\r\n            .digest();\r\n        const blocks = [Buffer.alloc(0)];\r\n        const blockCount = Math.ceil(byteLength / hashLength) + 1;\r\n        for (let i = 1; i < blockCount; ++i) {\r\n            blocks.push(crypto__WEBPACK_IMPORTED_MODULE_1___default().createHmac(hash, PRK)\r\n                .update(Buffer.concat([blocks[i - 1], info, Buffer.from([i])]))\r\n                .digest());\r\n        }\r\n        return Buffer.concat(blocks).slice(0, byteLength);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof HkdfCryptoKey)) {\r\n            throw new TypeError(\"key: Is not HKDF CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass ShakeCrypto {\r\n    static digest(algorithm, data) {\r\n        const hash = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(algorithm.name.toLowerCase(), { outputLength: algorithm.length })\r\n            .update(Buffer.from(data)).digest();\r\n        return new Uint8Array(hash).buffer;\r\n    }\r\n}\n\nclass Shake128Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Shake128Provider {\r\n    async onDigest(algorithm, data) {\r\n        return ShakeCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Shake256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Shake256Provider {\r\n    async onDigest(algorithm, data) {\r\n        return ShakeCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass SubtleCrypto extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.SubtleCrypto {\r\n    constructor() {\r\n        var _a;\r\n        super();\r\n        this.providers.set(new AesCbcProvider());\r\n        this.providers.set(new AesCtrProvider());\r\n        this.providers.set(new AesGcmProvider());\r\n        this.providers.set(new AesCmacProvider());\r\n        this.providers.set(new AesKwProvider());\r\n        this.providers.set(new AesEcbProvider());\r\n        this.providers.set(new DesCbcProvider());\r\n        this.providers.set(new DesEde3CbcProvider());\r\n        this.providers.set(new RsaSsaProvider());\r\n        this.providers.set(new RsaPssProvider());\r\n        this.providers.set(new RsaOaepProvider());\r\n        this.providers.set(new RsaEsProvider());\r\n        this.providers.set(new EcdsaProvider());\r\n        this.providers.set(new EcdhProvider());\r\n        this.providers.set(new Sha1Provider());\r\n        this.providers.set(new Sha256Provider());\r\n        this.providers.set(new Sha384Provider());\r\n        this.providers.set(new Sha512Provider());\r\n        this.providers.set(new Pbkdf2Provider());\r\n        this.providers.set(new HmacProvider());\r\n        this.providers.set(new HkdfProvider());\r\n        const nodeMajorVersion = (_a = /^v(\\d+)/.exec(process__WEBPACK_IMPORTED_MODULE_2__.version)) === null || _a === void 0 ? void 0 : _a[1];\r\n        if (nodeMajorVersion && parseInt(nodeMajorVersion, 10) >= 12) {\r\n            this.providers.set(new Shake128Provider());\r\n            this.providers.set(new Shake256Provider());\r\n        }\r\n        const hashes = crypto__WEBPACK_IMPORTED_MODULE_1__.getHashes();\r\n        if (hashes.includes(\"sha3-256\")) {\r\n            this.providers.set(new Sha3256Provider());\r\n        }\r\n        if (hashes.includes(\"sha3-384\")) {\r\n            this.providers.set(new Sha3384Provider());\r\n        }\r\n        if (hashes.includes(\"sha3-512\")) {\r\n            this.providers.set(new Sha3512Provider());\r\n        }\r\n        if (nodeMajorVersion && parseInt(nodeMajorVersion, 10) >= 14) {\r\n            this.providers.set(new EdDsaProvider());\r\n            this.providers.set(new EcdhEsProvider());\r\n        }\r\n    }\r\n}\n\nclass Crypto extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Crypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.subtle = new SubtleCrypto();\r\n    }\r\n    getRandomValues(array) {\r\n        if (!ArrayBuffer.isView(array)) {\r\n            throw new TypeError(\"Failed to execute 'getRandomValues' on 'Crypto': parameter 1 is not of type 'ArrayBufferView'\");\r\n        }\r\n        const buffer = Buffer.from(array.buffer, array.byteOffset, array.byteLength);\r\n        crypto__WEBPACK_IMPORTED_MODULE_1___default().randomFillSync(buffer);\r\n        return array;\r\n    }\r\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@peculiar/webcrypto/build/webcrypto.es.js\n");

/***/ })

};
;