"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\nclass ApiClient {\n    async makeRequest(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        // Get auth token from Clerk\n        const token = await this.getAuthToken();\n        const defaultHeaders = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            defaultHeaders[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const config = {\n            ...options,\n            headers: {\n                ...defaultHeaders,\n                ...options.headers\n            }\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        detail: \"Unknown error\"\n                    }));\n                throw new Error(errorData.detail || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            throw error;\n        }\n    }\n    async getAuthToken() {\n        // This will be implemented with Clerk integration\n        // For now, return null (unauthenticated requests)\n        return null;\n    }\n    calculateTimeSinceLive(lastSeenLiveAt) {\n        if (!lastSeenLiveAt) return null;\n        const now = new Date();\n        const lastSeen = new Date(lastSeenLiveAt);\n        const diffMs = now.getTime() - lastSeen.getTime();\n        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffDays > 0) {\n            return \"\".concat(diffDays, \" day\").concat(diffDays > 1 ? \"s\" : \"\", \" ago\");\n        } else if (diffHours > 0) {\n            return \"\".concat(diffHours, \" hour\").concat(diffHours > 1 ? \"s\" : \"\", \" ago\");\n        } else {\n            return \"Recently\";\n        }\n    }\n    formatFollowerCount(count) {\n        if (count >= 1000000) {\n            return \"\".concat((count / 1000000).toFixed(1), \"M\");\n        } else if (count >= 1000) {\n            return \"\".concat((count / 1000).toFixed(1), \"K\");\n        } else {\n            return count.toString();\n        }\n    }\n    // Streamer API methods\n    async getAvailableStreamers() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50;\n        const backendStreamers = await this.makeRequest(\"/fallback/available?limit=\".concat(limit));\n        // Transform backend data to frontend format\n        const streamers = backendStreamers.map((streamer)=>({\n                id: streamer.id || streamer.twitch_user_id,\n                twitch_user_id: streamer.twitch_user_id,\n                username: streamer.username,\n                display_name: streamer.display_name,\n                follower_count: streamer.follower_count,\n                is_live: streamer.is_live || false,\n                current_game: streamer.current_game || null,\n                stream_title: streamer.stream_title || null,\n                profile_image_url: streamer.thumbnail_url || \"https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png\",\n                language: streamer.language,\n                last_seen_live_at: streamer.last_seen_live_at || null,\n                created_at: streamer.created_at || new Date().toISOString(),\n                updated_at: streamer.updated_at || new Date().toISOString(),\n                time_since_live: this.calculateTimeSinceLive(streamer.last_seen_live_at),\n                formatted_follower_count: this.formatFollowerCount(streamer.follower_count),\n                assignment_status_for_current_user: null\n            }));\n        return {\n            streamers,\n            total: streamers.length,\n            user_status: {\n                daily_requests_used: 1,\n                daily_requests_remaining: 2,\n                can_make_request: true\n            }\n        };\n    }\n    async getLiveVerifiedStreamers() {\n        let batchSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        const response = await this.makeRequest(\"/fallback/live-verified?batch_size=\".concat(batchSize));\n        // Transform backend data to frontend format\n        const streamers = response.streamers.map((streamer)=>({\n                id: streamer.id || streamer.twitch_user_id,\n                twitch_user_id: streamer.twitch_user_id,\n                username: streamer.username,\n                display_name: streamer.display_name,\n                follower_count: streamer.follower_count,\n                is_live: true,\n                current_game: streamer.current_game || null,\n                stream_title: streamer.stream_title || null,\n                profile_image_url: streamer.thumbnail_url || \"https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png\",\n                language: streamer.language,\n                last_seen_live_at: new Date().toISOString(),\n                created_at: streamer.created_at || new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                time_since_live: \"Live now\",\n                formatted_follower_count: this.formatFollowerCount(streamer.follower_count),\n                assignment_status_for_current_user: null\n            }));\n        return {\n            streamers,\n            batch_size: response.batch_size,\n            checked_count: response.checked_count,\n            verification_method: response.verification_method,\n            status: response.status\n        };\n    }\n    async getStreamerStats() {\n        const backendStats = await this.makeRequest(\"/fallback/stats\");\n        return {\n            total_streamers: backendStats.total_streamers,\n            live_streamers: backendStats.live_streamers,\n            available_streamers: backendStats.available_streamers,\n            last_updated: new Date().toISOString()\n        };\n    }\n    async triggerScrape() {\n        return this.makeRequest(\"/api/v1/streamers/trigger-scrape\", {\n            method: \"POST\"\n        });\n    }\n    async getScraperStatus() {\n        return this.makeRequest(\"/api/v1/streamers/scraper-status\");\n    }\n    // Assignment API methods\n    async getUserAssignments() {\n        return this.makeRequest(\"/api/v1/assignments/\");\n    }\n    async updateAssignmentStatus(assignmentId, status, notes) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId, \"/status\"), {\n            method: \"PATCH\",\n            body: JSON.stringify({\n                status,\n                notes\n            })\n        });\n    }\n    async deleteAssignment(assignmentId) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId), {\n            method: \"DELETE\"\n        });\n    }\n    // Health check\n    async healthCheck() {\n        return this.makeRequest(\"/api/v1/health\");\n    }\n    constructor(){\n        this.baseURL = API_BASE_URL;\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});