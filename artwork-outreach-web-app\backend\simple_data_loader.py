#!/usr/bin/env python3
"""
Simple script to load CSV data using direct API calls to the running backend
"""

import os
import csv
import requests
import json
from pathlib import Path

# Backend API configuration
BACKEND_URL = "http://127.0.0.1:8000"
API_BASE = f"{BACKEND_URL}/api/v1"

def check_backend_health():
    """Check if backend is running"""
    try:
        response = requests.get(f"{API_BASE}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running and healthy!")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        print("💡 Make sure backend is running: uvicorn app.main:app --reload")
        return False

def load_csv_via_api(csv_path):
    """Load CSV data by calling the backend API directly"""
    
    # Read CSV file
    csv_file = Path(csv_path)
    if not csv_file.exists():
        print(f"❌ CSV file not found: {csv_path}")
        return False
    
    print(f"📊 Reading CSV file: {csv_path}")
    
    # Parse CSV
    streamers = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Convert CSV format to API format
            streamer = {
                "twitch_user_id": row.get('username', ''),  # Use username as ID if no separate ID
                "username": row.get('username', ''),
                "display_name": row.get('display_name', row.get('username', '')),
                "follower_count": int(row.get('follower_count', 0)),
                "is_live": True,  # They were live when scraped
                "current_game": row.get('game_name', ''),
                "stream_title": row.get('title', ''),
                "thumbnail_url": row.get('thumbnail_url', ''),
                "language": row.get('language', 'en')
            }
            streamers.append(streamer)
    
    print(f"✅ Parsed {len(streamers)} streamers from CSV")
    
    # Debug: Show first streamer data
    if streamers:
        print(f"🔍 Sample streamer data: {json.dumps(streamers[0], indent=2, default=str)}")
    
    # Send to backend API
    success_count = 0
    error_count = 0
    
    print("🚀 Uploading streamers to database via API...")
    
    for i, streamer in enumerate(streamers, 1):
        try:
            # Call the backend API to create/update streamer
            response = requests.post(
                f"{API_BASE}/admin/streamers",
                json=streamer,
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                success_count += 1
                print(f"✅ {i:2d}/{len(streamers)} - {streamer['username']}")
            else:
                error_count += 1
                error_detail = ""
                try:
                    error_detail = response.json().get('detail', '')
                except:
                    error_detail = response.text[:100]
                print(f"⚠️  {i:2d}/{len(streamers)} - {streamer['username']} - Error {response.status_code}: {error_detail}")
                
        except Exception as e:
            error_count += 1
            print(f"❌ {i:2d}/{len(streamers)} - {streamer['username']} - Error: {e}")
    
    print(f"\n📈 Upload Summary:")
    print(f"   ✅ Successful: {success_count}")
    print(f"   ❌ Errors: {error_count}")
    print(f"   📊 Total: {len(streamers)}")
    
    return success_count > 0

def get_database_stats():
    """Get current database statistics from API"""
    try:
        response = requests.get(f"{API_BASE}/streamers/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"\n📊 Database Statistics:")
            print(f"   Total streamers: {stats.get('total_streamers', 0)}")
            print(f"   Live streamers: {stats.get('live_streamers', 0)}")
            print(f"   Available for outreach: {stats.get('available_streamers', 0)}")
        else:
            print(f"⚠️  Could not get stats: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Could not get stats: {e}")

def main():
    """Main function"""
    print("🚀 Simple Data Loader - Loading Scraper Data via API\n")
    
    # Check if backend is running
    if not check_backend_health():
        return
    
    # Look for CSV file
    possible_files = [
        "twitch_streams_filtered.csv",
        "../twitch_streams_filtered.csv",
        "../../twitch_streams_filtered.csv"
    ]
    
    csv_file = None
    for file_path in possible_files:
        if Path(file_path).exists():
            csv_file = file_path
            break
    
    if not csv_file:
        print("❌ No CSV file found!")
        print("   Looking for: twitch_streams_filtered.csv")
        print("   Make sure you've run the scraper first")
        return
    
    # Show current stats
    print("📊 Current Database State:")
    get_database_stats()
    
    # Load CSV data
    if load_csv_via_api(csv_file):
        print("\n🎉 Data loading completed!")
        
        # Show updated stats
        print("📊 Updated Database State:")
        get_database_stats()
        
        print("\n💡 Next steps:")
        print("   1. Your backend is already running ✅")
        print("   2. Start frontend: npm run dev")
        print("   3. Access dashboard: http://localhost:3000")
        print("   4. Click 'Get New Streamers' to see real data!")
    else:
        print("\n❌ Data loading failed!")

if __name__ == "__main__":
    main()