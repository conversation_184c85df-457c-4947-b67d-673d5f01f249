import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000/api/v1';

export async function GET(request: NextRequest) {
  try {
    console.log('User status API route called - proxying to backend...');

    const response = await fetch(`${API_BASE_URL}/user/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend error: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error proxying user status to backend:', error);
    
    // Return mock user status as fallback
    const mockStatus = {
      daily_request_count: 0,
      remaining_requests: 3,
      next_reset_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      last_request_date: null
    };
    
    return NextResponse.json(mockStatus);
  }
}