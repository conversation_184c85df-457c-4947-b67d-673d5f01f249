import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from app.main import app
from app.database.models import Streamer

client = TestClient(app)

@patch('app.scraper.twitch_client.TwitchClient')
@patch('app.services.database_service.DatabaseService')
def test_scraping_workflow(mock_db_service, mock_twitch_client):
    """
    Test the entire scraping workflow from cron job trigger to database storage.
    """
    # Mock Twitch API response
    mock_twitch_client.return_value.get_streams.return_value = [
        {"user_name": "teststreamer1", "viewer_count": 100},
        {"user_name": "teststreamer2", "viewer_count": 200}
    ]
    
    # Mock database service
    mock_db_instance = mock_db_service.return_value
    
    # Trigger the scraping process
    response = client.post("/api/v1/admin/trigger-scrape")
    
    assert response.status_code == 200
    assert response.json() == {"message": "Scraping triggered successfully"}
    
    # Verify that the data was processed and stored
    assert mock_db_instance.upsert_streamer.call_count == 2
    mock_db_instance.upsert_streamer.assert_any_call({'twitch_name': 'teststreamer1', 'is_live': True, 'viewer_count': 100})
    mock_db_instance.upsert_streamer.assert_any_call({'twitch_name': 'teststreamer2', 'is_live': True, 'viewer_count': 200})