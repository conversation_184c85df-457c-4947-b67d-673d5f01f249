import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch

def test_get_streamers_sort_by_followers(client: TestClient):
    """
    Test sorting streamers by followers.
    """
    with patch("app.services.streamer_service.get_streamers") as mock_get:
        mock_get.return_value = [
            {"name": "Streamer A", "followers": 100},
            {"name": "Streamer B", "followers": 200},
        ]
        response = client.get("/api/v1/streamers?sort_by=followers")
        assert response.status_code == 200
        assert response.json()[0]["name"] == "Streamer B"
        mock_get.assert_called_with(sort_by="followers", sort_order="desc", skip=0, limit=10)

def test_get_streamers_sort_by_language(client: TestClient):
    """
    Test sorting streamers by language.
    """
    with patch("app.services.streamer_service.get_streamers") as mock_get:
        mock_get.return_value = [
            {"name": "Streamer A", "language": "en"},
            {"name": "Streamer B", "language": "es"},
        ]
        response = client.get("/api/v1/streamers?sort_by=language&sort_order=asc")
        assert response.status_code == 200
        assert response.json()[0]["name"] == "Streamer A"
        mock_get.assert_called_with(sort_by="language", sort_order="asc", skip=0, limit=10)