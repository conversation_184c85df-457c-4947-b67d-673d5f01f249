import { Streamer } from '@/types/streamer';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1';

export interface ApiStreamer {
  twitch_user_id: string;
  username: string;
  display_name: string;
  follower_count: number;
  is_live: boolean;
  current_game?: string;
  stream_title?: string;
  profile_image_url?: string;
  language: string;
  last_seen_live_at?: string;
  twitch_url?: string;
}

export interface StreamerListResponse {
  streamers: Streamer[];
  total: number;
  user_status: {
    daily_requests_used: number;
    daily_requests_remaining: number;
    can_make_request: boolean;
  };
}

export interface StreamerStats {
  total_streamers: number;
  live_streamers: number;
  available_streamers: number;
  last_updated: string;
}

export interface Assignment {
  id: string;
  streamer_id: string;
  streamer_username: string;
  streamer_display_name: string;
  status: string;
  assigned_at: string;
  updated_at: string;
  notes?: string;
}

class ApiClient {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    // Get auth token from Clerk
    const token = await this.getAuthToken();
    
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private async getAuthToken(): Promise<string | null> {
    // This will be implemented with Clerk integration
    // For now, return null (unauthenticated requests)
    return null;
  }

  private calculateTimeSinceLive(lastSeenLiveAt: string | null): string | null {
    if (!lastSeenLiveAt) return null;

    const now = new Date();
    const lastSeen = new Date(lastSeenLiveAt);
    const diffMs = now.getTime() - lastSeen.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Recently';
    }
  }

  private formatFollowerCount(count: number): string {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    } else {
      return count.toString();
    }
  }

  // Streamer API methods
  async getAvailableStreamers(limit: number = 50): Promise<StreamerListResponse> {
    const backendStreamers = await this.makeRequest<any[]>(`/api/v1/fallback/available?limit=${limit}`);

    // Transform backend data to frontend format
    const streamers: Streamer[] = backendStreamers.map((streamer: any) => ({
      id: streamer.id || streamer.twitch_user_id,
      twitch_user_id: streamer.twitch_user_id,
      username: streamer.username,
      display_name: streamer.display_name,
      follower_count: streamer.follower_count,
      is_live: streamer.is_live || false,
      current_game: streamer.current_game || null,
      stream_title: streamer.stream_title || null,
      profile_image_url: streamer.thumbnail_url || 'https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png',
      language: streamer.language,
      last_seen_live_at: streamer.last_seen_live_at || null,
      created_at: streamer.created_at || new Date().toISOString(),
      updated_at: streamer.updated_at || new Date().toISOString(),
      time_since_live: this.calculateTimeSinceLive(streamer.last_seen_live_at),
      formatted_follower_count: this.formatFollowerCount(streamer.follower_count),
      assignment_status_for_current_user: null
    }));

    return {
      streamers,
      total: streamers.length,
      user_status: {
        daily_requests_used: 1,
        daily_requests_remaining: 2,
        can_make_request: true
      }
    };
  }

  async getLiveVerifiedStreamers(batchSize: number = 10): Promise<{
    streamers: Streamer[];
    batch_size: number;
    checked_count: number;
    verification_method: string;
    status: string;
  }> {
    const response = await this.makeRequest<{
      streamers: any[];
      batch_size: number;
      checked_count: number;
      verification_method: string;
      status: string;
    }>(`/api/v1/fallback/live-verified?batch_size=${batchSize}`);

    // Transform backend data to frontend format
    const streamers: Streamer[] = response.streamers.map((streamer: any) => ({
      id: streamer.id || streamer.twitch_user_id,
      twitch_user_id: streamer.twitch_user_id,
      username: streamer.username,
      display_name: streamer.display_name,
      follower_count: streamer.follower_count,
      is_live: true, // All streamers from this endpoint are verified live
      current_game: streamer.current_game || null,
      stream_title: streamer.stream_title || null,
      profile_image_url: streamer.thumbnail_url || 'https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png',
      language: streamer.language,
      last_seen_live_at: new Date().toISOString(), // Just verified as live
      created_at: streamer.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString(), // Just updated
      time_since_live: 'Live now',
      formatted_follower_count: this.formatFollowerCount(streamer.follower_count),
      assignment_status_for_current_user: null
    }));

    return {
      streamers,
      batch_size: response.batch_size,
      checked_count: response.checked_count,
      verification_method: response.verification_method,
      status: response.status
    };
  }

  async getStreamerStats(): Promise<StreamerStats> {
    const backendStats = await this.makeRequest<any>('/api/v1/fallback/stats');

    return {
      total_streamers: backendStats.total_streamers,
      live_streamers: backendStats.live_streamers,
      available_streamers: backendStats.available_streamers,
      last_updated: new Date().toISOString()
    };
  }

  async triggerScrape(): Promise<{ message: string; status: string; triggered_by: string }> {
    return this.makeRequest('/api/v1/streamers/trigger-scrape', {
      method: 'POST',
    });
  }

  async getScraperStatus(): Promise<any> {
    return this.makeRequest('/api/v1/streamers/scraper-status');
  }

  // Assignment API methods
  async getUserAssignments(): Promise<Assignment[]> {
    return this.makeRequest<Assignment[]>('/api/v1/assignments/');
  }

  async updateAssignmentStatus(
    assignmentId: string,
    status: string,
    notes?: string
  ): Promise<{ message: string; assignment_id: string; new_status: string }> {
    return this.makeRequest(`/api/v1/assignments/${assignmentId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status, notes }),
    });
  }

  async deleteAssignment(assignmentId: string): Promise<{ message: string; assignment_id: string }> {
    return this.makeRequest(`/api/v1/assignments/${assignmentId}`, {
      method: 'DELETE',
    });
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.makeRequest('/api/v1/health');
  }
}

export const apiClient = new ApiClient();
export default apiClient;