import pytest
from unittest.mock import Magic<PERSON>ock, patch

from sqlalchemy.orm import Session

from app.services.database_service import DatabaseService
from app.database.models import Streamer
from app.schemas.streamer import StreamerCreate

@pytest.fixture
def mock_db_session_sync():
    """Fixture for a mocked SQLAlchemy Session (synchronous)."""
    session = MagicMock(spec=Session)
    session.query.return_value.filter.return_value.first = MagicMock()
    session.begin = MagicMock()
    return session

@pytest.fixture
def database_service(mock_db_session_sync: Session):
    """Fixture for the DatabaseService."""
    return DatabaseService(db=mock_db_session_sync)

def test_upsert_streamer_create(database_service: DatabaseService, mock_db_session_sync: Session):
    """Test creating a new streamer if it doesn't exist."""
    streamer_data = StreamerCreate(twitch_user_id="123", username="new_streamer")
    mock_db_session_sync.query.return_value.filter.return_value.first.return_value = None

    database_service.upsert_streamer(streamer_data)

    mock_db_session_sync.add.assert_called_once()
    mock_db_session_sync.commit.assert_called_once()

def test_upsert_streamer_update(database_service: DatabaseService, mock_db_session_sync: Session):
    """Test updating an existing streamer."""
    streamer_data = StreamerCreate(twitch_user_id="123", username="updated_streamer")
    existing_streamer = Streamer(twitch_user_id="123", username="old_streamer")
    mock_db_session_sync.query.return_value.filter.return_value.first.return_value = existing_streamer

    database_service.upsert_streamer(streamer_data)

    assert existing_streamer.username == "updated_streamer"
    mock_db_session_sync.add.assert_not_called()
    mock_db_session_sync.commit.assert_called_once()

def test_bulk_upsert_streamers(database_service: DatabaseService, mock_db_session_sync: Session):
    """Test bulk upserting streamers."""
    streamers_data = [
        StreamerCreate(twitch_user_id="1", username="streamer1"),
        StreamerCreate(twitch_user_id="2", username="streamer2"),
    ]

    database_service.bulk_upsert_streamers(streamers_data)

    # The with self.db.begin() context manager should be entered
    mock_db_session_sync.begin.__enter__.assert_called_once()
    
    # merge should be called for each streamer
    assert mock_db_session_sync.merge.call_count == 2