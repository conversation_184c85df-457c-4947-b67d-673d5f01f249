from fastapi_cache import FastAPICache

class CacheService:
    @staticmethod
    async def clear_cache_for_streamer_stats():
        """
        Clears the cache for streamer statistics.
        """
        await FastAPICache.clear(namespace="fastapi-cache", key_prefix="StreamerService.get_total_streamers_count")
        await FastAPICache.clear(namespace="fastapi-cache", key_prefix="StreamerService.get_live_streamers_count")
        await FastAPICache.clear(namespace="fastapi-cache", key_prefix="StreamerService.get_available_streamers_count")
        await FastAPICache.clear(namespace="fastapi-cache", key_prefix="StreamerService.get_average_follower_count")

    @staticmethod
    async def clear_cache_for_available_streamers():
        """
        Clears the cache for available streamers.
        """
        await FastAPICache.clear(namespace="fastapi-cache", key_prefix="StreamerService.get_available_streamers")

    @staticmethod
    async def warm_streamer_cache(streamer_service):
        """
        Warms the cache with frequently accessed streamer data.
        """
        await streamer_service.get_total_streamers_count()
        await streamer_service.get_live_streamers_count()
        await streamer_service.get_available_streamers_count()
        await streamer_service.get_average_follower_count()
        await streamer_service.get_available_streamers()