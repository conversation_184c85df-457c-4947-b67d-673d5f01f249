#!/usr/bin/env python3
"""
Configuration verification script for Artwork Outreach backend.
Run this script to verify that your environment variables are properly configured.
"""

import os
from pathlib import Path

def check_env_file():
    """Check if .env file exists and is readable."""
    env_path = Path('.env')
    if not env_path.exists():
        print("❌ .env file not found")
        return False
    
    if not env_path.is_file():
        print("❌ .env is not a file")
        return False
    
    try:
        with open(env_path, 'r') as f:
            content = f.read()
            if len(content.strip()) == 0:
                print("❌ .env file is empty")
                return False
        print("✅ .env file exists and is readable")
        return True
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def load_env_file():
    """Load environment variables from .env file."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded from .env")
        return True
    except ImportError:
        print("⚠️  python-dotenv not installed, using system environment variables")
        return True
    except Exception as e:
        print(f"❌ Error loading .env file: {e}")
        return False

def check_required_vars():
    """Check if all required environment variables are set."""
    required_vars = {
        'TWITCH_CLIENT_ID': 'Twitch API Client ID',
        'TWITCH_CLIENT_SECRET': 'Twitch API Client Secret',
        'SUPABASE_URL': 'Supabase Project URL',
        'SUPABASE_KEY': 'Supabase Anon Key',
        'CLERK_SECRET_KEY': 'Clerk Secret Key',
        'DATABASE_URL': 'Database Connection URL',
        'ENVIRONMENT': 'Application Environment',
        'CORS_ORIGINS': 'CORS Origins',
    }
    
    missing_vars = []
    configured_vars = []
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            missing_vars.append(f"{var} ({description})")
        else:
            # Show first few characters for security
            display_value = value[:10] + "..." if len(value) > 10 else value
            configured_vars.append(f"{var}: {display_value}")
    
    if configured_vars:
        print(f"\n✅ Configured variables ({len(configured_vars)}):")
        for var in configured_vars:
            print(f"   {var}")
    
    if missing_vars:
        print(f"\n❌ Missing variables ({len(missing_vars)}):")
        for var in missing_vars:
            print(f"   {var}")
        return False
    
    print(f"\n✅ All required environment variables are configured!")
    return True

def check_optional_vars():
    """Check optional environment variables."""
    optional_vars = {
        'SUPABASE_SERVICE_ROLE_KEY': 'Supabase Service Role Key (for admin operations)',
        'CLERK_PUBLISHABLE_KEY': 'Clerk Publishable Key',
        'SENTRY_DSN': 'Sentry DSN for error monitoring',
        'REDIS_URL': 'Redis connection URL',
        'SECRET_KEY': 'JWT signing secret',
    }
    
    configured_optional = []
    missing_optional = []
    
    for var, description in optional_vars.items():
        value = os.getenv(var)
        if value and value != "" and "placeholder" not in value.lower():
            display_value = value[:10] + "..." if len(value) > 10 else value
            configured_optional.append(f"{var}: {display_value}")
        else:
            missing_optional.append(f"{var} ({description})")
    
    if configured_optional:
        print(f"\n✅ Configured optional variables ({len(configured_optional)}):")
        for var in configured_optional:
            print(f"   {var}")
    
    if missing_optional:
        print(f"\n⚠️  Optional variables not configured ({len(missing_optional)}):")
        for var in missing_optional:
            print(f"   {var}")

def check_database_url_format():
    """Check if DATABASE_URL has the correct format."""
    database_url = os.getenv('DATABASE_URL', '')
    
    if '[YOUR_DB_PASSWORD]' in database_url:
        print("\n❌ DATABASE_URL contains placeholder password")
        print("   Please replace [YOUR_DB_PASSWORD] with your actual Supabase database password")
        print("   Get it from: https://supabase.com/dashboard → Settings → Database")
        return False
    
    if database_url.startswith('postgresql://') or database_url.startswith('postgresql+asyncpg://'):
        print("\n✅ DATABASE_URL format looks correct")
        return True
    elif database_url.startswith('sqlite'):
        print("\n⚠️  Using SQLite database (development only)")
        return True
    else:
        print(f"\n❌ DATABASE_URL format appears incorrect: {database_url[:30]}...")
        return False

def main():
    """Run all configuration checks."""
    print("🔧 Artwork Outreach Backend Configuration Verification\n")
    
    # Check .env file
    if not check_env_file():
        print("\n❌ Configuration verification failed")
        return False
    
    # Load environment variables
    if not load_env_file():
        print("\n❌ Configuration verification failed")
        return False
    
    # Check required variables
    required_ok = check_required_vars()
    
    # Check optional variables
    check_optional_vars()
    
    # Check database URL format
    db_ok = check_database_url_format()
    
    # Summary
    print("\n" + "="*50)
    if required_ok and db_ok:
        print("🎉 Configuration verification PASSED!")
        print("   Your backend should be ready to run.")
        print("\n💡 Next steps:")
        print("   1. Start Redis: redis-server")
        print("   2. Run backend: uvicorn app.main:app --reload")
        print("   3. Check health: http://localhost:8000/api/v1/health")
    else:
        print("❌ Configuration verification FAILED!")
        print("   Please fix the issues above before running the backend.")
        print("\n📖 For help, see: ENVIRONMENT_SETUP.md")
    
    return required_ok and db_ok

if __name__ == "__main__":
    main()