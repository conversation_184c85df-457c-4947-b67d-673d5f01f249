#!/usr/bin/env python3
"""
Script to populate the web application database with real Twitch scraper data.
This script runs the legacy scraper and inserts the results into the web app database.
"""

import sys
import os
import asyncio
import pandas as pd
from datetime import datetime
from pathlib import Path

# Add the parent directory to the path to import the scraper
sys.path.append(str(Path(__file__).parent.parent))

from twitch_scraper import TwitchScraper
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
from app.database.models import Base, Streamer, ScraperRun
from app.config import get_settings

settings = get_settings()

# Create async engine for the web app database
engine = create_async_engine(settings.DATABASE_URL)
AsyncSessionLocal = async_sessionmaker(autocommit=False, autoflush=False, bind=engine)


async def create_tables():
    """Create database tables if they don't exist."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("✅ Database tables created/verified")


async def populate_streamers_from_scraper():
    """Run the Twitch scraper and populate the database with results."""
    print("🔍 Starting Twitch scraper...")
    
    # Create a scraper run record
    async with AsyncSessionLocal() as session:
        scraper_run = ScraperRun(
            status="started",
            details="Running scraper to collect fresh data"
        )
        session.add(scraper_run)
        await session.commit()
        await session.refresh(scraper_run)
        run_id = scraper_run.id
    
    try:
        # Run the legacy scraper
        scraper = TwitchScraper()
        scraper.scrape()
        
        # Check if CSV file was created
        csv_file = Path("twitch_streams_filtered.csv")
        if not csv_file.exists():
            raise FileNotFoundError("Scraper did not produce expected CSV output")
        
        # Read the scraped data
        df = pd.read_csv(csv_file)
        print(f"📊 Scraped {len(df)} streamers")
        
        # Insert data into database
        async with AsyncSessionLocal() as session:
            streamers_added = 0
            streamers_updated = 0
            
            for _, row in df.iterrows():
                # Check if streamer already exists
                existing_streamer = await session.get(Streamer, row['username'])
                
                if existing_streamer:
                    # Update existing streamer
                    existing_streamer.display_name = row.get('display_name', row['username'])
                    existing_streamer.follower_count = int(row['follower_count'])
                    existing_streamer.is_live = True  # They were live when scraped
                    existing_streamer.current_game = row.get('game_name', '')
                    existing_streamer.stream_title = row.get('title', '')
                    existing_streamer.thumbnail_url = row.get('thumbnail_url', '')
                    existing_streamer.language = row.get('language', 'en')
                    existing_streamer.last_seen_live_at = datetime.utcnow()
                    existing_streamer.updated_at = datetime.utcnow()
                    streamers_updated += 1
                else:
                    # Create new streamer
                    streamer = Streamer(
                        twitch_user_id=row['username'],  # Using username as ID for now
                        username=row['username'],
                        display_name=row.get('display_name', row['username']),
                        follower_count=int(row['follower_count']),
                        is_live=True,  # They were live when scraped
                        current_game=row.get('game_name', ''),
                        stream_title=row.get('title', ''),
                        thumbnail_url=row.get('thumbnail_url', ''),
                        language=row.get('language', 'en'),
                        last_seen_live_at=datetime.utcnow()
                    )
                    session.add(streamer)
                    streamers_added += 1
            
            await session.commit()
            
            # Update scraper run record
            scraper_run = await session.get(ScraperRun, run_id)
            scraper_run.status = "completed"
            scraper_run.streamers_found = streamers_added + streamers_updated
            scraper_run.details = f"Added {streamers_added} new streamers, updated {streamers_updated} existing"
            await session.commit()
            
            print(f"✅ Database updated: {streamers_added} added, {streamers_updated} updated")
            
    except Exception as e:
        # Update scraper run record with error
        async with AsyncSessionLocal() as session:
            scraper_run = await session.get(ScraperRun, run_id)
            scraper_run.status = "failed"
            scraper_run.details = f"Error: {str(e)}"
            await session.commit()
        
        print(f"❌ Scraper failed: {e}")
        raise


async def get_database_stats():
    """Get current database statistics."""
    async with AsyncSessionLocal() as session:
        from sqlalchemy import text, func
        
        # Count total streamers
        total_result = await session.execute(text("SELECT COUNT(*) FROM streamers"))
        total_streamers = total_result.scalar()
        
        # Count live streamers
        live_result = await session.execute(text("SELECT COUNT(*) FROM streamers WHERE is_live = true"))
        live_streamers = live_result.scalar()
        
        # Count by language
        lang_result = await session.execute(text("SELECT language, COUNT(*) FROM streamers GROUP BY language"))
        languages = dict(lang_result.fetchall())
        
        # Recent scraper runs
        runs_result = await session.execute(text("SELECT status, COUNT(*) FROM scraper_runs GROUP BY status"))
        runs = dict(runs_result.fetchall())
        
        print("\n📈 Database Statistics:")
        print(f"   Total streamers: {total_streamers}")
        print(f"   Live streamers: {live_streamers}")
        print(f"   Languages: {languages}")
        print(f"   Scraper runs: {runs}")


async def main():
    """Main function to populate database with real Twitch data."""
    print("🚀 Populating database with real Twitch scraper data...")
    
    try:
        # Create tables
        await create_tables()
        
        # Show current stats
        await get_database_stats()
        
        # Run scraper and populate database
        await populate_streamers_from_scraper()
        
        # Show updated stats
        await get_database_stats()
        
        print("\n🎉 Database successfully populated with real Twitch data!")
        print("   You can now restart your web application to see real streamer data.")
        
    except Exception as e:
        print(f"\n💥 Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())