"""Background tasks and scheduler for Twitch scraping"""

import asyncio
import logging
from datetime import datetime
from contextlib import asynccontextmanager
from fastapi import BackgroundTasks

from app.scraper.twitch_scraper import TwitchScraper
from app.database.connection import AsyncSessionLocal
from app.database.models import <PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import select, func

logger = logging.getLogger(__name__)

class ScraperScheduler:
    """Manages scheduled scraping tasks"""
    
    def __init__(self):
        self.scraper = TwitchScraper()
        self.is_running = False
        self._lock = asyncio.Lock()
    
    async def run_scrape_task(self):
        """Run scraping task with proper locking to prevent overlapping runs"""
        async with self._lock:
            if self.is_running:
                logger.warning("Scrape already in progress, skipping")
                return
            
            self.is_running = True
            logger.info("Starting scheduled scrape task")
            
            try:
                # Check if there's been a recent successful run (within last 45 minutes)
                async with AsyncSessionLocal() as session:
                    result = await session.execute(
                        select(ScraperRun)
                        .where(ScraperRun.status == "completed")
                        .where(ScraperRun.run_at > func.now() - func.interval('45 minutes'))
                        .order_by(ScraperRun.run_at.desc())
                        .limit(1)
                    )
                    recent_run = result.scalar_one_or_none()
                    
                    if recent_run:
                        logger.info(f"Recent successful run found at {recent_run.run_at}, skipping")
                        return
                
                # Run the scraper
                streamers_found = await self.scraper.run_full_scrape()
                logger.info(f"Scheduled scrape completed: {streamers_found} streamers processed")
                
            except Exception as e:
                logger.error(f"Scheduled scrape failed: {e}")
                raise
            finally:
                self.is_running = False
    
    async def start_scheduler(self):
        """Start the background scheduler (for development/testing)"""
        logger.info("Starting scraper scheduler (development mode)")
        
        while True:
            try:
                await self.run_scrape_task()
                # Wait 1 hour between runs
                await asyncio.sleep(3600)
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                # Wait 5 minutes before retrying on error
                await asyncio.sleep(300)

# Global scheduler instance
scheduler = ScraperScheduler()

async def trigger_scrape_task():
    """Trigger a scrape task (for API endpoint)"""
    await scheduler.run_scrape_task()

async def get_scraper_status():
    """Get current scraper status"""
    async with AsyncSessionLocal() as session:
        # Get latest scraper run
        result = await session.execute(
            select(ScraperRun)
            .order_by(ScraperRun.run_at.desc())
            .limit(1)
        )
        latest_run = result.scalar_one_or_none()
        
        # Get total streamers in database
        result = await session.execute(
            select(func.count()).select_from(session.query(ScraperRun.__table__).subquery())
        )
        total_runs = result.scalar()
        
        return {
            "is_running": scheduler.is_running,
            "latest_run": {
                "run_at": latest_run.run_at.isoformat() if latest_run else None,
                "status": latest_run.status if latest_run else None,
                "streamers_found": latest_run.streamers_found if latest_run else 0,
                "details": latest_run.details if latest_run else None
            } if latest_run else None,
            "total_runs": total_runs
        }

# FastAPI background task integration
def add_scrape_task(background_tasks: BackgroundTasks):
    """Add scrape task to FastAPI background tasks"""
    background_tasks.add_task(trigger_scrape_task)

# For manual testing
if __name__ == "__main__":
    async def main():
        scraper = TwitchScraper()
        result = await scraper.run_full_scrape()
        print(f"Manual scrape completed: {result} streamers processed")
    
    asyncio.run(main())