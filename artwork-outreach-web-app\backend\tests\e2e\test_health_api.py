import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch

def test_external_service_checks(client: TestClient):
    """
    Test the detailed health check endpoint for external service status.
    """
    with patch("app.services.health_service.get_twitch_status", return_value="connected") as mock_twitch, \
         patch("app.services.health_service.get_clerk_status", return_value="connected") as mock_clerk:

        response = client.get("/api/v1/health/detailed")
        assert response.status_code == 200
        json_response = response.json()
        assert json_response["details"]["twitch_status"] == "connected"
        assert json_response["details"]["clerk_status"] == "connected"
        mock_twitch.assert_called_once()
        mock_clerk.assert_called_once()