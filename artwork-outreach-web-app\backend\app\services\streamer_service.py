import base64
from datetime import datetime, timedelta, timezone
from fastapi_cache.decorator import cache
from sqlalchemy import func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased, joinedload, selectinload
from app.database.models import Streamer, Assignment


class StreamerService:
    def __init__(self, db: AsyncSession):
        self.db = db

    @cache(expire=300)
    async def get_streamer_by_id(
        self, twitch_user_id: str
    ) -> Streamer | None:
        """
        Fetches a single streamer by their Twitch user ID, including assignment history.
        Uses selectinload for efficient loading of assignments.
        """
        query = (
            select(Streamer)
            .where(Streamer.twitch_user_id == twitch_user_id)
            .options(joinedload(Streamer.assignments))
        )
        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_available_streamers(self) -> list[Streamer]:
        """
        Fetches a list of available streamers based on specific criteria.
        - Live streamers
        - 0-50 followers
        - Language: 'en'
        - Not assigned in the last 7 days
        - Ordered by follower count descending
        - Limited to 50
        
        Optimized with LEFT JOIN instead of NOT IN for better performance.
        """
        seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)

        # Use LEFT JOIN with IS NULL instead of NOT IN for better performance
        query = (
            select(Streamer)
            .outerjoin(
                Assignment,
                (Assignment.streamer_id == Streamer.twitch_user_id) &
                (Assignment.assigned_at >= seven_days_ago)
            )
            .where(Streamer.is_live == True)
            .where(Streamer.follower_count.between(0, 50))
            .where(Streamer.language == 'en')
            .where(Assignment.id.is_(None))  # No recent assignments
            .order_by(Streamer.follower_count.desc())
            .limit(50)
        )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_unassigned_live_streamers(
        self, limit: int = 50
    ) -> list[Streamer]:
        """
        Fetches a list of live streamers who have not been assigned in the last 7 days.
        Optimized with LEFT JOIN instead of NOT IN subquery.

        Args:
            limit: The maximum number of streamers to return.

        Returns:
            A list of Streamer objects.
        """
        seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)

        # Use LEFT JOIN with IS NULL instead of NOT IN for better performance
        query = (
            select(Streamer)
            .outerjoin(
                Assignment,
                (Assignment.streamer_id == Streamer.twitch_user_id) &
                (Assignment.assigned_at >= seven_days_ago)
            )
            .where(Streamer.is_live == True)
            .where(Assignment.id.is_(None))  # No recent assignments
            .order_by(func.random())
            .limit(limit)
        )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_streamers_paginated(
        self,
        cursor: str | None,
        limit: int,
        min_followers: int | None = None,
        max_followers: int | None = None,
        language: str | None = None,
        is_live: bool | None = None,
        search: str | None = None,
        live_since: str | None = None,
        live_until: str | None = None,
        game: str | None = None,
        stream_title: str | None = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
    ):
        """
        Fetches a paginated list of streamers with optional filters and sorting using cursor-based pagination.
        """
        sort_by_mapping = {
            "follower_count": Streamer.follower_count,
            "last_seen_live": Streamer.last_seen_live_at,
            "username": Streamer.username,
            "created_at": Streamer.created_at,
        }

        if sort_by not in sort_by_mapping:
            sort_by = "created_at"
        if sort_order not in ["asc", "desc"]:
            sort_order = "desc"

        sort_column = sort_by_mapping[sort_by]
        order_by_clause = sort_column.asc() if sort_order == "asc" else sort_column.desc()

        query = select(Streamer)
        if min_followers is not None:
            query = query.where(Streamer.follower_count >= min_followers)
        if max_followers is not None:
            query = query.where(Streamer.follower_count <= max_followers)
        if language:
            query = query.where(Streamer.language == language)
        if is_live is not None:
            query = query.where(Streamer.is_live == is_live)
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    Streamer.username.ilike(search_term),
                    Streamer.display_name.ilike(search_term),
                )
            )
        if live_since:
            query = query.where(Streamer.last_seen_live_at >= datetime.fromisoformat(live_since))
        if live_until:
            query = query.where(Streamer.last_seen_live_at <= datetime.fromisoformat(live_until))
        if game:
            query = query.where(Streamer.current_game.ilike(f"%{game}%"))
        if stream_title:
            query = query.where(Streamer.stream_title.ilike(f"%{stream_title}%"))

        if cursor:
            try:
                decoded_cursor = base64.urlsafe_b64decode(cursor).decode()
                cursor_value, cursor_id = decoded_cursor.split("|")
                
                if sort_by in ["created_at", "last_seen_live"]:
                    cursor_value = datetime.fromisoformat(cursor_value)
                elif sort_by == "follower_count":
                    cursor_value = int(cursor_value)

                if sort_order == "desc":
                    query = query.where(
                        (sort_column < cursor_value)
                        | ((sort_column == cursor_value) & (Streamer.id < int(cursor_id)))
                    )
                else:  # asc
                    query = query.where(
                        (sort_column > cursor_value)
                        | ((sort_column == cursor_value) & (Streamer.id > int(cursor_id)))
                    )
            except (ValueError, TypeError):
                # Invalid cursor, treat as if no cursor was provided
                pass

        # Remove expensive assignment loading for pagination - load only when needed
        streamers_query = query.order_by(order_by_clause, Streamer.id.desc()).limit(limit + 1)
        streamers_result = await self.db.execute(streamers_query)
        streamers = streamers_result.scalars().all()

        has_next_page = len(streamers) > limit
        streamers = streamers[:limit]

        next_cursor = None
        if has_next_page:
            last_streamer = streamers[-1]
            sort_value = getattr(last_streamer, sort_by)
            if isinstance(sort_value, datetime):
                sort_value = sort_value.isoformat()
            
            cursor_str = f"{sort_value}|{last_streamer.id}"
            next_cursor = base64.urlsafe_b64encode(cursor_str.encode()).decode()

        # This implementation does not support previous_cursor easily without reversing the query.
        # For now, we will return None. A more complete implementation might require a separate query.
        previous_cursor = None

        return streamers, next_cursor, previous_cursor
    async def search_streamers(self, search_term: str) -> list[Streamer]:
        """
        Searches for streamers by username, display_name, or current_game.
        Orders results by relevance (exact username match is highest).
        """
        search_filter = or_(
            Streamer.username.ilike(f"%{search_term}%"),
            Streamer.display_name.ilike(f"%{search_term}%"),
            Streamer.current_game.ilike(f"%{search_term}%"),
        )

        query = (
            select(Streamer)
            .where(search_filter)
            .order_by(
                func.lower(Streamer.username) == search_term.lower(),
                Streamer.follower_count.desc(),
            )
        )

        result = await self.db.execute(query)
        return result.scalars().all()

    @cache(expire=300)
    async def get_total_streamers_count(self) -> int:
        """
        Returns the total number of streamers in the database.
        Cached for 5 minutes.
        """
        query = select(func.count()).select_from(Streamer)
        result = await self.db.execute(query)
        return result.scalar_one()

    @cache(expire=300)
    async def get_live_streamers_count(self) -> int:
        """
        Returns the count of streamers who are currently live.
        Cached for 5 minutes.
        """
        query = select(func.count()).where(Streamer.is_live == True)
        result = await self.db.execute(query)
        return result.scalar_one()

    @cache(expire=300)
    async def get_available_streamers_count(self) -> int:
        """
        Returns the count of streamers who meet the "available" criteria.
        Optimized with LEFT JOIN and cached for 5 minutes.
        """
        seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
        
        # Use LEFT JOIN with IS NULL instead of NOT IN for better performance
        query = (
            select(func.count(Streamer.id))
            .select_from(
                Streamer.__table__.outerjoin(
                    Assignment.__table__,
                    (Assignment.streamer_id == Streamer.twitch_user_id) &
                    (Assignment.assigned_at >= seven_days_ago)
                )
            )
            .where(Streamer.is_live == True)
            .where(Streamer.follower_count.between(0, 50))
            .where(Streamer.language == 'en')
            .where(Assignment.id.is_(None))
        )
        result = await self.db.execute(query)
        return result.scalar_one()

    @cache(expire=300)
    async def get_average_follower_count(self) -> float:
        """
        Calculates and returns the average follower count of all streamers.
        Cached for 5 minutes.
        """
        query = select(func.avg(Streamer.follower_count))
        result = await self.db.execute(query)
        return result.scalar_one() or 0.0