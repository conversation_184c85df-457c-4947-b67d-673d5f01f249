import asyncio
import os
import tempfile
from typing import AsyncGenerator, Generator
import gc

import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy import text

from app.database.connection import get_db_session
from app.database.models import Base
from app.main import app

# Set environment variables for testing BEFORE any imports
os.environ["ENVIRONMENT"] = "testing"

# Create a temporary file for test database
temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
temp_db.close()

# Test database URL
TEST_DATABASE_URL = f"sqlite+aiosqlite:///{temp_db.name}"

# Create test engine with proper configuration
test_engine = create_async_engine(
    TEST_DATABASE_URL, 
    connect_args={"check_same_thread": False},
    echo=False,
    pool_pre_ping=True
)

# Create session maker for tests
TestSessionLocal = async_sessionmaker(
    autocommit=False, 
    autoflush=False, 
    bind=test_engine,
    expire_on_commit=False
)

async def override_get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Override for database dependency that uses test database.
    """
    async with TestSessionLocal() as session:
        yield session

# Override database dependency BEFORE any tests run
app.dependency_overrides[get_db_session] = override_get_db_session

@pytest_asyncio.fixture(scope="session", autouse=True)
async def setup_test_database():
    """Create test database tables once per session."""
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    # Cleanup
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    await test_engine.dispose()
    gc.collect()
    os.unlink(temp_db.name)

@pytest_asyncio.fixture(scope="function")
async def db_session(setup_test_database) -> AsyncGenerator[AsyncSession, None]:
    """
    Provide a clean database session for each test by rolling back transactions.
    """
    connection = await test_engine.connect()
    transaction = await connection.begin()

    session = TestSessionLocal(bind=connection)

    try:
        yield session
    finally:
        await session.close()
        await transaction.rollback()
        await connection.close()

@pytest_asyncio.fixture(scope="function")
async def async_client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """
    Provide an async HTTP client for testing FastAPI endpoints.
    """
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """
    Create event loop for async tests.
    """
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()