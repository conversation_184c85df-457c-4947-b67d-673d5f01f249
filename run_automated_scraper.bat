@echo off
REM Automated Twitch Scraper Batch Script for Windows Task Scheduler
REM This script runs the automated scraper with proper error handling

echo ========================================
echo AUTOMATED TWITCH SCRAPER STARTING
echo Time: %date% %time%
echo ========================================

REM Change to the script directory
cd /d "%~dp0"

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Set log file with timestamp
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set LOGFILE=logs\batch_run_%TIMESTAMP%.log

echo Starting automated scraper... > "%LOGFILE%"
echo Timestamp: %date% %time% >> "%LOGFILE%"

REM Run the Python script
echo Running Python scraper...
echo Running Python scraper... >> "%LOGFILE%"

python automated_scraper.py >> "%LOGFILE%" 2>&1

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Automated scraper completed successfully >> "%LOGFILE%"
    echo SUCCESS: Automated scraper completed successfully
) else (
    echo ERROR: Automated scraper failed with exit code %ERRORLEVEL% >> "%LOGFILE%"
    echo ERROR: Automated scraper failed with exit code %ERRORLEVEL%
)

echo ========================================
echo AUTOMATED SCRAPER FINISHED
echo Time: %date% %time%
echo Check log file: %LOGFILE%
echo ========================================

REM Keep window open for 5 seconds if run manually
timeout /t 5 /nobreak > nul

exit /b %ERRORLEVEL%
