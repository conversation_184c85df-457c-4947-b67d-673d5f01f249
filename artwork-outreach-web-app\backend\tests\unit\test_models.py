import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.database.models import Streamer, UserProfile, Assignment

# Mark all tests in this module as async
pytestmark = pytest.mark.asyncio


async def test_create_streamer(db_session: AsyncSession):
    """
    Test case: Verifies the creation of a Streamer instance.
    """
    streamer = Streamer(
        twitch_user_id="12345",
        username="test_streamer",
        display_name="TestStreamer",
        follower_count=100,
    )
    db_session.add(streamer)
    await db_session.commit()

    retrieved_streamer = await db_session.get(Streamer, streamer.id)
    assert retrieved_streamer is not None
    assert retrieved_streamer.username == "test_streamer"
    assert retrieved_streamer.follower_count == 100


async def test_streamer_twitch_user_id_uniqueness(db_session: AsyncSession):
    """
    Test case: Ensures that the twitch_user_id for a streamer is unique.
    """
    streamer1 = Streamer(twitch_user_id="12345", username="streamer1")
    db_session.add(streamer1)
    await db_session.commit()

    streamer2 = Streamer(twitch_user_id="12345", username="streamer2")
    db_session.add(streamer2)
    with pytest.raises(IntegrityError):
        await db_session.commit()


async def test_create_user_profile(db_session: AsyncSession):
    """
    Test case: Verifies the creation of a UserProfile instance.
    """
    user = UserProfile(
        email="<EMAIL>",
        full_name="Test User",
    )
    db_session.add(user)
    await db_session.commit()

    retrieved_user = await db_session.get(UserProfile, user.id)
    assert retrieved_user is not None
    assert retrieved_user.email == "<EMAIL>"
    assert retrieved_user.role == "agent"


async def test_user_profile_email_uniqueness(db_session: AsyncSession):
    """
    Test case: Ensures that the email for a user profile is unique.
    """
    user1 = UserProfile(email="<EMAIL>", full_name="User One")
    db_session.add(user1)
    await db_session.commit()

    user2 = UserProfile(email="<EMAIL>", full_name="User Two")
    db_session.add(user2)
    with pytest.raises(IntegrityError):
        await db_session.commit()


async def test_create_assignment(db_session: AsyncSession):
    """
    Test case: Verifies the creation of an Assignment and its relationships.
    """
    user = UserProfile(email="<EMAIL>", full_name="Agent User")
    streamer = Streamer(twitch_user_id="54321", username="assigned_streamer")
    db_session.add_all([user, streamer])
    await db_session.commit()

    assignment = Assignment(
        agent_id=user.id,
        streamer_id=streamer.twitch_user_id,
        status="assigned",
    )
    db_session.add(assignment)
    await db_session.commit()

    # Eagerly load relationships to prevent lazy loading issues in async context
    stmt = (
        select(Assignment)
        .where(Assignment.id == assignment.id)
        .options(selectinload(Assignment.user), selectinload(Assignment.streamer))
    )
    result = await db_session.execute(stmt)
    retrieved_assignment = result.scalar_one_or_none()

    assert retrieved_assignment is not None
    assert retrieved_assignment.status == "assigned"
    assert retrieved_assignment.user is not None
    assert retrieved_assignment.streamer is not None
    assert retrieved_assignment.user.id == user.id
    assert retrieved_assignment.streamer.id == streamer.id


async def test_assignment_uniqueness(db_session: AsyncSession):
    """
    Test case: Ensures that an agent can only be assigned to a streamer once.
    """
    user = UserProfile(email="<EMAIL>", full_name="Test Agent")
    streamer = Streamer(twitch_user_id="98765", username="test_streamer_for_assignment")
    db_session.add_all([user, streamer])
    await db_session.commit()

    assignment1 = Assignment(agent_id=user.id, streamer_id=streamer.twitch_user_id)
    db_session.add(assignment1)
    await db_session.commit()

    assignment2 = Assignment(agent_id=user.id, streamer_id=streamer.twitch_user_id)
    db_session.add(assignment2)

    with pytest.raises(IntegrityError):
        await db_session.commit()


async def test_streamer_follower_count_constraint(db_session: AsyncSession):
    """
    Test case: Verifies the non-negative constraint on the follower_count.
    """
    streamer = Streamer(
        twitch_user_id="67890",
        username="negative_follower_streamer",
        follower_count=-10,
    )
    db_session.add(streamer)
    # The check constraint is enforced by the database, so the error occurs on flush/commit.
    with pytest.raises(IntegrityError):
        await db_session.commit()


async def test_user_daily_request_count_constraint(db_session: AsyncSession):
    """
    Test case: Verifies the non-negative constraint on daily_request_count.
    """
    user = UserProfile(
        email="<EMAIL>",
        full_name="Negative Request User",
        daily_request_count=-1,
    )
    db_session.add(user)
    with pytest.raises(IntegrityError):
        await db_session.commit()