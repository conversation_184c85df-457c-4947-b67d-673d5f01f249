import asyncio
import pytest
import time
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

from app.database.connection import get_db_session, engine
from app.database.models import Base, Streamer
from app.config import get_settings

# Use a separate test database
settings = get_settings()
TEST_DATABASE_URL = settings.DATABASE_URL.replace("artwork_outreach.db", "test_artwork_outreach.db")

test_engine = create_async_engine(TEST_DATABASE_URL)
TestingSessionLocal = sessionmaker(
    autocommit=False, autoflush=False, bind=test_engine, class_=AsyncSession
)

@pytest.fixture(scope="module")
async def test_db():
    """
    Fixture to set up the test database.
    """
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield

    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.mark.asyncio
async def test_database_connection(test_db):
    """
    Test that the application can connect to and disconnect from the test database.
    """
    session = TestingSessionLocal()
    assert session is not None
    await session.close()
    assert session.is_active is False


@pytest.mark.asyncio
async def test_transaction_handling(test_db):
    """
    Test that database transactions (commit, rollback) are handled correctly.
    """
    async with TestingSessionLocal() as session:
        async with session.begin():
            session.add(Streamer(twitch_user_id="123", username="test_user"))

        # The transaction is committed, so the user should exist in a new session
        async with TestingSessionLocal() as new_session:
            result = await new_session.execute(text("SELECT * FROM streamers WHERE twitch_user_id = '123'"))
            assert result.first() is not None

    async with TestingSessionLocal() as session:
        async with session.begin():
            session.add(Streamer(twitch_user_id="456", username="rollback_user"))
            await session.rollback()

        # The transaction is rolled back, so the user should not exist
        async with TestingSessionLocal() as new_session:
            result = await new_session.execute(text("SELECT * FROM streamers WHERE twitch_user_id = '456'"))
            assert result.first() is None


async def _concurrent_operation(session_maker, streamer_id):
    """Helper function for concurrent database operations."""
    async with session_maker() as session:
        async with session.begin():
            session.add(Streamer(twitch_user_id=str(streamer_id), username=f"user_{streamer_id}"))
            await asyncio.sleep(0.1)  # Simulate some work


@pytest.mark.asyncio
async def test_concurrent_operations(test_db):
    """
    Test the behavior of concurrent database operations.
    """
    tasks = [_concurrent_operation(TestingSessionLocal, i) for i in range(10)]
    await asyncio.gather(*tasks)

    async with TestingSessionLocal() as session:
        result = await session.execute(text("SELECT COUNT(*) FROM streamers"))
        assert result.scalar() == 10


@pytest.mark.asyncio
async def test_data_integrity(test_db):
    """
    Test that data integrity is maintained during various operations.
    """
    async with TestingSessionLocal() as session:
        async with session.begin():
            session.add(Streamer(twitch_user_id="789", username="integrity_test"))
            session.add(Streamer(twitch_user_id="789", username="integrity_test_2"))
            with pytest.raises(Exception):
                await session.commit()


@pytest.mark.asyncio
async def test_performance_queries(test_db):
    """
    Test and benchmark potentially slow queries.
    """
    async with TestingSessionLocal() as session:
        async with session.begin():
            for i in range(100):
                session.add(Streamer(twitch_user_id=str(i), username=f"perf_user_{i}"))

    start_time = time.time()
    async with TestingSessionLocal() as session:
        await session.execute(text("SELECT * FROM streamers WHERE username LIKE 'perf_user_%'"))
    end_time = time.time()

    duration = end_time - start_time
    assert duration < 0.1  # Assert that the query is reasonably fast