"""Configuration settings for Twitch stream scraper."""

import os
from dotenv import load_dotenv

load_dotenv()

# Twitch API Configuration
TWITCH_CLIENT_ID = os.getenv('TWITCH_CLIENT_ID')
TWITCH_CLIENT_SECRET = os.getenv('TWITCH_CLIENT_SECRET')
TWITCH_API_BASE_URL = 'https://api.twitch.tv/helix'

# Scraping Configuration
MAX_FOLLOWERS = 50  # Back to original requirement
MIN_FOLLOWERS = 0
TARGET_LANGUAGE = 'en'
SCROLL_PAUSE_TIME = 2
MAX_STREAMS_TO_COLLECT = 2000  # Increased capacity for larger scraping runs

# Stream Consistency Filters (for lead quality)
MIN_STREAMS_PER_WEEK = 3  # Only streamers who stream regularly
DAYS_TO_CHECK_HISTORY = 14  # Check last 2 weeks of streaming

# RPG Games to search for small streamers
RPG_GAMES = [
    'World of Warcraft',
    'Final Fantasy XIV Online',
    'The Elder Scrolls V: Skyrim',
    '<PERSON><PERSON><PERSON>\'s Gate 3',
    'Diablo IV',
    'Path of Exile',
    'Lost Ark',
    'Elden Ring',
    'The Witcher 3: Wild Hunt',
    'Cyberpunk 2077',
    'Divinity: Original Sin 2',
    'Persona 5 Royal',
    'Dragon Age: The Veilguard',
    'Hogwarts Legacy',
    'Starfield',
    'Fallout 4',
    'Dark Souls III',
    'Monster Hunter: World',
    'Genshin Impact',
    'Guild Wars 2'
]

# Selenium Configuration
HEADLESS_MODE = False  # Set to False for debugging
IMPLICIT_WAIT_TIME = 10
PAGE_LOAD_TIMEOUT = 30
CHROME_DRIVER_PATH = None  # Set to path of chromedriver.exe if manual install needed

# Output Configuration
OUTPUT_FORMAT = 'csv'  # 'csv' or 'json'
OUTPUT_FILE = 'twitch_streams_filtered.csv'

# Rate Limiting
API_RATE_LIMIT = 2  # seconds between API calls (increased to avoid rate limits)

# Concurrent Processing
MAX_WORKERS = 30  # Number of concurrent threads for API calls (25-50 recommended)
REQUESTS_PER_MINUTE = 600  # Conservative limit (Twitch allows 800/min)
MAX_SCRAPING_WORKERS = 5  # Number of concurrent browser instances for scraping
FAST_SCROLL_PAUSE = 1  # Reduced scroll pause time for faster scraping