"""Configuration settings for Twitch stream scraper."""

import os
from dotenv import load_dotenv

load_dotenv()

# Twitch API Configuration
TWITCH_CLIENT_ID = os.getenv('TWITCH_CLIENT_ID')
TWITCH_CLIENT_SECRET = os.getenv('TWITCH_CLIENT_SECRET')
TWITCH_API_BASE_URL = 'https://api.twitch.tv/helix'

# Scraping Configuration
MAX_FOLLOWERS = 50  # Back to original requirement
MIN_FOLLOWERS = 0
TARGET_LANGUAGE = 'en'
SCROLL_PAUSE_TIME = 2
MAX_STREAMS_TO_COLLECT = 5000  # Increased capacity for expanded game list (150+ games)

# Stream Consistency Filters (for lead quality)
MIN_STREAMS_PER_WEEK = 3  # Only streamers who stream regularly
DAYS_TO_CHECK_HISTORY = 14  # Check last 2 weeks of streaming

# Games to search for small streamers (expanded list)
GAMES_TO_SCRAPE = [
    # Original RPG Games
    'World of Warcraft',
    'Final Fantasy XIV Online',
    'The Elder Scrolls V: Skyrim',
    '<PERSON><PERSON>ur\'s Gate 3',
    'Diablo IV',
    'Path of Exile',
    'Lost Ark',
    'Elden Ring',
    'The Witcher 3: Wild Hunt',
    'Cyberpunk 2077',
    'Divinity: Original Sin 2',
    'Persona 5 Royal',
    'Dragon Age: The Veilguard',
    'Hogwarts Legacy',
    'Starfield',
    'Fallout 4',
    'Dark Souls III',
    'Monster Hunter: World',
    'Genshin Impact',
    'Guild Wars 2',

    # Set 1 - Adventure & Survival Games
    'Sea of Thieves',
    'Rust',
    'Raft',
    'Sifu',
    'God of War',
    'L.A. Noire',
    'Supermarket Simulator',
    'Supermarket Together',
    'Outlast',
    'Outlast 2',
    'Little Nightmares',
    'Little Nightmares 2',
    'Brotato',
    'The Binding of Isaac',
    'The Binding of Isaac: Repentance',
    'Sekiro: Shadows Die Twice',
    'The Last of Us',
    'My Hero One\'s Justice',
    'My Hero One\'s Justice 2',
    'My Hero Academia: Ultra Rumble',
    'Trackmania',
    'The Forest',
    'Sons of The Forest',
    'DayZ',
    'Undertale',
    'Blue Prince',
    'Lineage II',
    'Subnautica',

    # Set 2 - Action & Simulation Games
    'Dark and Darker',
    'Detroit: Become Human',
    'Black Myth: Wukong',
    'Lethal Company',
    'Borderlands 2',
    'The Sims 4',
    'The Sims 3',
    'Assassin\'s Creed',
    'Assassin\'s Creed II',
    'Assassin\'s Creed: Brotherhood',
    'Assassin\'s Creed: Revelations',
    'Assassin\'s Creed III',
    'Assassin\'s Creed IV: Black Flag',
    'Assassin\'s Creed Rogue',
    'Assassin\'s Creed Unity',
    'Assassin\'s Creed Syndicate',
    'Assassin\'s Creed Origins',
    'Assassin\'s Creed Odyssey',
    'Assassin\'s Creed Valhalla',
    'Assassin\'s Creed Mirage',
    'Prototype',
    'Prototype 2',
    'Muck',
    'Valheim',
    'Dark Souls',
    'Dark Souls II',
    'Hollow Knight',
    'World of Warships',
    'Euro Truck Simulator 2',
    'Factorio',
    'Satisfactory',

    # Set 3 - Horror & Adventure Games
    'Until Dawn',
    'Alien: Isolation',
    'Exit 8',
    'Star Wars Jedi: Fallen Order',
    'Star Wars Jedi: Survivor',
    'The Legend of Zelda',
    'Zelda II: The Adventure of Link',
    'A Link to the Past',
    'Link\'s Awakening',
    'Ocarina of Time',
    'Majora\'s Mask',
    'Oracle of Seasons',
    'Oracle of Ages',
    'Four Swords',
    'The Wind Waker',
    'Four Swords Adventures',
    'The Minish Cap',
    'Twilight Princess',
    'Phantom Hourglass',
    'Spirit Tracks',
    'Skyward Sword',
    'A Link Between Worlds',
    'Tri Force Heroes',
    'Breath of the Wild',
    'Tears of the Kingdom',
    'Echoes of Wisdom',
    'Left 4 Dead',
    'The Outlast Trials',
    'PowerWash Simulator',
    'Hitman: Codename 47',
    'Hitman 2: Silent Assassin',
    'Hitman: Contracts',
    'Hitman: Blood Money',
    'Hitman: Absolution',
    'Hitman',
    'Hitman 2',
    'Hitman 3',

    # Set 4 - Indie & Horror Games
    'Alan Wake 2',
    'Fast Food Simulator',
    'Phasmophobia',
    'Pacify',
    'Mouthwashing',
    'My Summer Car',
    'Tomb Raider',
    'Rise of the Tomb Raider',
    'Shadow of the Tomb Raider',
    'Life is Strange',
    'Life is Strange: Before the Storm',
    'Life is Strange 2',
    'Life is Strange: True Colors',
    'Yu-Gi-Oh!',
    'Quake',
    'Icarus',
    'Crab Game',
    'Portal 2',
    'Max Payne',
    'Harry Potter and the Chamber of Secrets',
    'Five Nights at Freddy\'s',
    'Five Nights at Freddy\'s 2',
    'Five Nights at Freddy\'s 3',
    'Five Nights at Freddy\'s 4',
    'Five Nights at Freddy\'s: Sister Location',
    'Five Nights at Freddy\'s: Help Wanted',
    'Five Nights at Freddy\'s: Security Breach',
    'Clustertruck',
    'High on Life',
    'The Mortuary Assistant',
    'Crash Bandicoot',
    'Manor Lords',
    'House Flipper 2',
    'Fears to Fathom'
]

# Keep RPG_GAMES for backward compatibility
RPG_GAMES = GAMES_TO_SCRAPE

# Selenium Configuration
HEADLESS_MODE = False  # Set to False for debugging
IMPLICIT_WAIT_TIME = 10
PAGE_LOAD_TIMEOUT = 30
CHROME_DRIVER_PATH = None  # Set to path of chromedriver.exe if manual install needed

# Output Configuration
OUTPUT_FORMAT = 'csv'  # 'csv' or 'json'
OUTPUT_FILE = 'twitch_streams_filtered.csv'

# Rate Limiting
API_RATE_LIMIT = 2  # seconds between API calls (increased to avoid rate limits)

# Concurrent Processing
MAX_WORKERS = 30  # Number of concurrent threads for API calls (25-50 recommended)
REQUESTS_PER_MINUTE = 600  # Conservative limit (Twitch allows 800/min)
MAX_SCRAPING_WORKERS = 5  # Number of concurrent browser instances for scraping
FAST_SCROLL_PAUSE = 1  # Reduced scroll pause time for faster scraping