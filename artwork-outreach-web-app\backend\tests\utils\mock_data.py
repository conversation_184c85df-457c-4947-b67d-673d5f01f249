from faker import Faker

fake = Faker()

def create_mock_streamer_data(
    twitch_user_id: str = None,
    username: str = None,
    display_name: str = None,
    follower_count: int = None,
    is_live: bool = False,
):
    """
    Generates a dictionary of mock streamer data, simulating a response from an external API.
    """
    return {
        "id": twitch_user_id or str(fake.random_number(digits=8)),
        "user_login": username or fake.user_name(),
        "user_name": display_name or fake.name(),
        "viewer_count": fake.random_number(digits=4) if is_live else 0,
        "follower_count": follower_count or fake.random_number(digits=5),
        "type": "live" if is_live else "",
        "language": "en",
        "profile_image_url": fake.image_url(),
        "title": fake.sentence() if is_live else "",
        "game_name": fake.word() if is_live else "",
    }