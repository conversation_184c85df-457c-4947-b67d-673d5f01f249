# 🚀 24/7 Twitch Scraper Automation Setup Guide

This guide will help you set up your Twitch scraper to run automatically 24/7 using Windows Task Scheduler.

## 📋 Prerequisites

- ✅ Windows 10/11
- ✅ Python 3.11+ installed
- ✅ All dependencies installed (`pip install -r requirements.txt`)
- ✅ Supabase credentials configured in `.env` files

## 🔧 Setup Instructions

### Step 1: Test Manual Execution

First, test that the automation script works manually:

```bash
# Navigate to your project directory
cd "C:\Users\<USER>\Desktop\Twitch-Scraper-main"

# Run the automated scraper
python automated_scraper.py
```

**Expected Output:**
- ✅ Scraper runs successfully
- ✅ Data loads to Supabase
- ✅ Log files created in `logs/` directory

### Step 2: Configure Windows Task Scheduler

1. **Open Task Scheduler:**
   - Press `Win + R`, type `taskschd.msc`, press Enter
   - Or search "Task Scheduler" in Start menu

2. **Create Basic Task:**
   - Click "Create Basic Task..." in the right panel
   - Name: `Twitch Scraper Automation`
   - Description: `Automated Twitch scraper for 24/7 operation`

3. **Set Trigger (Schedule):**
   - Choose "Daily" for daily scraping
   - Set start time (recommend off-peak hours like 3:00 AM)
   - Set recurrence: Every 1 day

4. **Set Action:**
   - Choose "Start a program"
   - Program/script: `C:\Users\<USER>\Desktop\Twitch-Scraper-main\run_automated_scraper.bat`
   - Start in: `C:\Users\<USER>\Desktop\Twitch-Scraper-main`

5. **Configure Advanced Settings:**
   - Right-click the created task → Properties
   - **General Tab:**
     - ✅ Run whether user is logged on or not
     - ✅ Run with highest privileges
   - **Conditions Tab:**
     - ✅ Start the task only if the computer is on AC power (uncheck for laptops)
     - ✅ Wake the computer to run this task
   - **Settings Tab:**
     - ✅ Allow task to be run on demand
     - ✅ If the running task does not end when requested, force it to stop
     - Stop the task if it runs longer than: 2 hours

## ⏰ Recommended Schedules

### Option 1: Daily Scraping (Recommended)
- **Frequency:** Every day at 3:00 AM
- **Pros:** Fresh data daily, low server load
- **Best for:** Regular content creators

### Option 2: Twice Daily
- **Frequency:** 6:00 AM and 6:00 PM
- **Pros:** More frequent updates
- **Best for:** High-activity periods

### Option 3: Weekly
- **Frequency:** Every Sunday at 2:00 AM
- **Pros:** Lower resource usage
- **Best for:** Stable streamer base

## 📊 Monitoring and Logs

### Log Files Location
```
C:\Users\<USER>\Desktop\Twitch-Scraper-main\logs\
├── scraper_automation_20250704.log    # Daily automation logs
├── batch_run_20250704_030000.log      # Batch script logs
└── ...
```

### Log File Contents
- ✅ Scraper execution status
- ✅ Number of streamers found
- ✅ Database loading results
- ✅ Error messages and stack traces
- ✅ Cleanup operations

### Monitoring Commands
```bash
# View latest automation log
type logs\scraper_automation_%date:~-4,4%%date:~-10,2%%date:~-7,2%.log

# View all recent logs
dir logs\ /od

# Check if scraper is currently running
tasklist | findstr python
```

## 🔧 Troubleshooting

### Common Issues

**1. Task doesn't run:**
- Check Task Scheduler History tab
- Verify file paths are absolute
- Ensure Python is in system PATH

**2. Python script fails:**
- Check log files for error details
- Verify all dependencies are installed
- Test manual execution first

**3. Database connection issues:**
- Verify Supabase credentials in `.env`
- Check internet connection
- Review fallback system logs

**4. Permission errors:**
- Run Task Scheduler as Administrator
- Set task to "Run with highest privileges"
- Check file/folder permissions

### Emergency Commands
```bash
# Stop all Python processes (if stuck)
taskkill /f /im python.exe

# Disable scheduled task
schtasks /change /tn "Twitch Scraper Automation" /disable

# Enable scheduled task
schtasks /change /tn "Twitch Scraper Automation" /enable
```

## 📈 Performance Optimization

### Resource Management
- **CPU Usage:** Scraper uses ~20-30% CPU during execution
- **Memory:** ~200-500MB RAM usage
- **Network:** ~50-100MB data transfer per run
- **Duration:** 10-30 minutes per execution

### Optimization Tips
1. **Schedule during off-peak hours** (2-6 AM)
2. **Monitor log file sizes** (auto-cleanup after 7 days)
3. **Adjust scraper parameters** if needed
4. **Use SSD storage** for better performance

## 🎯 Success Metrics

Your automation is working correctly when you see:

- ✅ **Regular log files** created daily
- ✅ **New streamers** appearing in dashboard
- ✅ **No error messages** in recent logs
- ✅ **Database updates** reflected in frontend
- ✅ **Consistent execution times** in Task Scheduler

## 🚨 Alerts and Notifications

### Email Notifications (Optional)
You can enhance the system with email alerts:

1. **Success notifications:** Daily summary emails
2. **Error alerts:** Immediate notification on failures
3. **Weekly reports:** Comprehensive statistics

### Dashboard Monitoring
- Check `http://localhost:3000` for updated streamer counts
- Monitor API status at `http://localhost:8000/api/v1/health`
- Review fallback endpoint performance

---

## 🎉 Congratulations!

Your Twitch scraper is now set up for 24/7 automated operation! 

The system will:
- 🤖 Run automatically on schedule
- 📊 Collect fresh streamer data
- 💾 Update your database
- 📝 Log all activities
- 🧹 Clean up old files
- 🔄 Handle errors gracefully

**Next Steps:**
1. Test the automation manually
2. Set up the Windows Task Scheduler
3. Monitor the first few automated runs
4. Enjoy your 24/7 Twitch scraper! 🚀
