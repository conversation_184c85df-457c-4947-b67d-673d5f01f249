-- Migration: Add streamer quota tracking to user_profiles table
-- Date: 2025-01-04
-- Description: Add daily_streamers_received and daily_streamer_limit columns for quota management

-- Add new columns for streamer quota tracking
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS daily_streamers_received INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS daily_streamer_limit INTEGER DEFAULT 200;

-- Add constraints for the new columns (with error handling)
DO $$
BEGIN
    -- Add constraint for daily_streamers_received
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'cc_user_daily_streamers_received_non_negative'
        AND table_name = 'user_profiles'
    ) THEN
        ALTER TABLE user_profiles
        ADD CONSTRAINT cc_user_daily_streamers_received_non_negative
        CHECK (daily_streamers_received >= 0);
    END IF;

    -- Add constraint for daily_streamer_limit
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'cc_user_daily_streamer_limit_positive'
        AND table_name = 'user_profiles'
    ) THEN
        ALTER TABLE user_profiles
        ADD CONSTRAINT cc_user_daily_streamer_limit_positive
        CHECK (daily_streamer_limit > 0);
    END IF;
END $$;

-- Update existing users to have the default streamer limit
UPDATE user_profiles 
SET daily_streamer_limit = 200 
WHERE daily_streamer_limit IS NULL;

-- Create index for better performance on quota queries
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'idx_user_profiles_quota_lookup'
    ) THEN
        CREATE INDEX idx_user_profiles_quota_lookup
        ON user_profiles (id, last_request_date, daily_streamers_received, daily_streamer_limit);
    END IF;
END $$;

-- Add comment for documentation
COMMENT ON COLUMN user_profiles.daily_streamers_received IS 'Number of live streamers received by user today';
COMMENT ON COLUMN user_profiles.daily_streamer_limit IS 'Daily limit of live streamers per user (default: 200)';
