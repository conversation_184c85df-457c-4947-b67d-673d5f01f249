import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from app.main import app

client = TestClient(app)

@pytest.fixture
def mock_admin_service():
    with patch('app.api.routes.admin.admin_service') as mock_service:
        yield mock_service

def test_trigger_scrape(mock_admin_service):
    """
    Test triggering the scraping process.
    """
    mock_admin_service.trigger_scraping.return_value = {"message": "Scraping triggered successfully"}
    
    response = client.post("/api/v1/admin/trigger-scrape")
    
    assert response.status_code == 200
    assert response.json() == {"message": "Scraping triggered successfully"}
    mock_admin_service.trigger_scraping.assert_called_once()