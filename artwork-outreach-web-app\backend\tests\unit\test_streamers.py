import pytest
from httpx import AsyncClient
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta

from app.database.models import UserProfile, Streamer
from app.api.dependencies import get_current_user
from app.main import app

def create_mock_user(email_suffix: str = ""):
    return UserProfile(
        id=uuid.uuid4(),
        full_name="Test User",
        email=f"test{email_suffix}@example.com",
        role="agent",
        daily_request_count=5,
    )

def create_mock_streamer(twitch_user_id: str, is_live: bool = False, last_seen_live: datetime = None, follower_count: int = 25):
    return Streamer(
        twitch_user_id=twitch_user_id,
        username=f"test_streamer_{twitch_user_id}",
        display_name=f"Test Streamer {twitch_user_id}",
        follower_count=follower_count,
        language="en",
        is_live=is_live,
        last_seen_live_at=last_seen_live or datetime.utcnow(),
    )

@pytest.mark.asyncio
async def test_get_available_streamers(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test retrieving available streamers.
    """
    mock_user = create_mock_user()
    db_session.add(mock_user)

    # Create some streamers
    streamer1 = create_mock_streamer("1", is_live=True)
    streamer2 = create_mock_streamer("2", is_live=True)
    streamer3 = create_mock_streamer("3", is_live=False) # Not live
    db_session.add_all([streamer1, streamer2, streamer3])
    await db_session.commit()

    app.dependency_overrides[get_current_user] = lambda: mock_user
    response = await async_client.get("/api/v1/streamers/available")
    assert response.status_code == 200
    json_response = response.json()
    assert len(json_response) == 2
    assert {s["twitch_user_id"] for s in json_response} == {"1", "2"}

@pytest.mark.asyncio
async def test_get_streamers_paginated(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test retrieving a paginated list of streamers with filters.
    """
    mock_user = create_mock_user()
    db_session.add(mock_user)

    # Create some streamers
    streamer1 = create_mock_streamer("1", follower_count=10)
    streamer2 = create_mock_streamer("2", follower_count=20)
    streamer3 = create_mock_streamer("3", follower_count=30)
    db_session.add_all([streamer1, streamer2, streamer3])
    await db_session.commit()

    app.dependency_overrides[get_current_user] = lambda: mock_user
    
    # Test with no filters
    response = await async_client.get("/api/v1/streamers/")
    assert response.status_code == 200
    json_response = response.json()
    assert len(json_response["streamers"]) == 3

    # Test with follower count filter
    response = await async_client.get("/api/v1/streamers/?min_followers=15")
    assert response.status_code == 200
    json_response = response.json()
    assert len(json_response["streamers"]) == 2
    assert {s["twitch_user_id"] for s in json_response["streamers"]} == {"2", "3"}

@pytest.mark.asyncio
async def test_get_streamer_details(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test retrieving detailed information for a single streamer.
    """
    mock_user = create_mock_user()
    db_session.add(mock_user)

    streamer = create_mock_streamer("12345")
    db_session.add(streamer)
    await db_session.commit()

    app.dependency_overrides[get_current_user] = lambda: mock_user
    response = await async_client.get(f"/api/v1/streamers/{streamer.twitch_user_id}")
    assert response.status_code == 200
    json_response = response.json()
    assert json_response["twitch_user_id"] == streamer.twitch_user_id
    assert json_response["username"] == streamer.username