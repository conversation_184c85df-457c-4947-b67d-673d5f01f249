import time
from functools import wraps
from app.utils.logging import logger

def monitor_db_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        query_time = end_time - start_time
        
        logger.info(f"Database query {func.__name__} took {query_time:.4f}s")
        
        if query_time > 0.5:
            logger.warning(f"Slow database query detected: {func.__name__} took {query_time:.4f}s")
            # Placeholder for sending an alert
            # send_alert(f"Slow database query: {func.__name__}")
            
        return result
    return wrapper