{"version": 3, "sources": ["../../../src/build/templates/middleware.ts"], "names": ["nH<PERSON><PERSON>", "mod", "_mod", "handler", "middleware", "default", "page", "Error", "opts", "adapter"], "mappings": ";;;;+BAoBA;;;eAAwBA;;;QAlBjB;yBAEiB;sEAGF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,MAAMC,MAAM;IAAE,GAAGC,aAAI;AAAC;AACtB,MAAMC,UAAUF,IAAIG,UAAU,IAAIH,IAAII,OAAO;AAE7C,MAAMC,OAAO;AAEb,IAAI,OAAOH,YAAY,YAAY;IACjC,MAAM,IAAII,MACR,CAAC,gBAAgB,EAAED,KAAK,wDAAwD,CAAC;AAErF;AAEe,SAASN,SACtBQ,IAAmE;IAEnE,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACPF;QACAH;IACF;AACF"}