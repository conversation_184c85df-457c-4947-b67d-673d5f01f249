"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_dashboard_UserStatus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/UserStatus */ \"(app-pages-browser)/./src/components/dashboard/UserStatus.tsx\");\n/* harmony import */ var _components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/StreamerList */ \"(app-pages-browser)/./src/components/dashboard/StreamerList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AssignmentList */ \"(app-pages-browser)/./src/components/dashboard/AssignmentList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/AssignmentAnalytics */ \"(app-pages-browser)/./src/components/dashboard/AssignmentAnalytics.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [streamers, setStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStatus, setUserStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [backendConnected, setBackendConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const API_BASE_URL = \"/api\";\n    // Fetch initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStats();\n        fetchUserStatus();\n        fetchAssignments();\n    }, []);\n    const fetchUserStatus = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/user/status\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUserStatus(data);\n            } else {\n                console.error(\"Failed to fetch user status - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch user status - Connection error:\", err);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            console.log(\"Fetching stats using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getStreamerStats();\n            setStats({\n                total_streamers: data.total_streamers,\n                live_streamers: data.live_streamers,\n                available_streamers: data.available_streamers,\n                average_followers: 25 // Use static value since backend doesn't calculate this\n            });\n            setBackendConnected(true);\n            console.log(\"Stats fetched successfully:\", data);\n        } catch (err) {\n            console.error(\"Failed to fetch stats - Connection error:\", err);\n            setBackendConnected(false);\n            // Set default stats if backend is not available\n            setStats({\n                total_streamers: 0,\n                live_streamers: 0,\n                available_streamers: 0,\n                average_followers: 0\n            });\n        }\n    };\n    const fetchNewStreamers = async ()=>{\n        console.log(\"=== FETCH NEW STREAMERS BUTTON CLICKED ===\");\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching streamers using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getAvailableStreamers();\n            console.log(\"Received data:\", data);\n            const streamersArray = data.streamers || [];\n            console.log(\"Streamers array:\", streamersArray);\n            setStreamers(streamersArray.slice(0, 50)); // Limit to 50 streamers\n            // Update user status from response\n            if (data.user_status) {\n                setUserStatus({\n                    daily_request_count: data.user_status.daily_requests_used,\n                    remaining_requests: data.user_status.daily_requests_remaining,\n                    next_reset_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now\n                });\n            }\n            // Refresh stats after fetching streamers\n            await fetchStats();\n            alert(\"Success! Loaded \".concat(streamersArray.length, \" real streamers from your scraper data! This counts as 1 of your 3 daily requests.\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to fetch streamers\";\n            setError(errorMessage);\n            console.error(\"=== ERROR FETCHING STREAMERS ===\", err);\n            alert(\"Error: \" + errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAssignments = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAssignments(data.assignments);\n            } else {\n                console.error(\"Failed to fetch assignments - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch assignments - Connection error:\", err);\n        }\n    };\n    const handleUpdateAssignment = async (id, status, notes)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status,\n                    notes\n                })\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to update assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to update assignment - Connection error:\", err);\n        }\n    };\n    const handleDeleteAssignment = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to delete assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to delete assignment - Connection error:\", err);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Please sign in to access the dashboard.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Agent Dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-6\",\n                children: [\n                    \"Welcome back, \",\n                    (user === null || user === void 0 ? void 0 : user.name) || \"Agent\",\n                    \"! Manage your streamer outreach campaigns.\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_UserStatus__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Total Streamers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-green-600\",\n                                children: (stats === null || stats === void 0 ? void 0 : stats.total_streamers) || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: \"In database\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-purple-600\",\n                                children: (stats === null || stats === void 0 ? void 0 : stats.available_streamers) || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: \"Ready for outreach\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            userStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 bg-blue-50 p-4 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold text-lg mb-2 text-blue-800\",\n                        children: \"\\uD83D\\uDCCA Your Daily Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: userStatus.daily_request_count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Requests Used\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: userStatus.remaining_requests\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Remaining\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-600\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Daily Limit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    userStatus.remaining_requests === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-red-100 border border-red-300 rounded text-red-700 text-sm\",\n                        children: [\n                            \"⚠️ You've reached your daily limit of 3 requests. Reset time: \",\n                            new Date(userStatus.next_reset_time).toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 bg-gray-100 p-4 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold text-lg mb-2\",\n                        children: \"\\uD83D\\uDD27 Debug Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Streamers Count: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: streamers.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Assignments Count: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: assignments.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 35\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Loading: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: loading ? \"Yes\" : \"No\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Error: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: error || \"None\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Backend Connected: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: backendConnected ? \"Yes\" : \"No\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 35\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Stats: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: stats ? \"Loaded\" : \"None\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"User Status: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: userStatus ? \"Loaded\" : \"None\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>alert(\"\\uD83C\\uDF89 Test button works! React is working properly.\"),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium mr-4\",\n                        children: \"\\uD83E\\uDDEA Test Button\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchNewStreamers,\n                        disabled: loading || (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0,\n                        className: \"px-6 py-3 rounded-lg font-medium text-white \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"bg-gray-400 cursor-not-allowed\" : \"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400\"),\n                        children: loading ? \"Fetching...\" : (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"❌ Daily Limit Reached\" : \"\\uD83C\\uDFAF Get New Streamers\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"Click to fetch available streamers for outreach\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: [\n                                \"Error: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                streamers: streamers,\n                assignments: [],\n                onInterestLevelChanged: ()=>{}\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    assignments: assignments\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    assignments: assignments,\n                    onUpdate: handleUpdateAssignment,\n                    onDelete: handleDeleteAssignment\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"API Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 rounded-full mr-2 \".concat(backendConnected === true ? \"bg-green-500\" : backendConnected === false ? \"bg-red-500\" : \"bg-yellow-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this),\n                                            backendConnected === true ? \"Backend Connected\" : backendConnected === false ? \"Backend Disconnected\" : \"Checking Connection...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: backendConnected === true ? \"FastAPI server running successfully\" : backendConnected === false ? \"Cannot connect to FastAPI server\" : \"Testing backend connection...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"API URL: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono text-sm\",\n                                                children: API_BASE_URL\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 24\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: [\n                                            \"API Documentation: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"http://127.0.0.1:8000/docs\",\n                                                target: \"_blank\",\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"http://127.0.0.1:8000/docs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 34\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this),\n                                    backendConnected === false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-800 text-sm\",\n                                            children: [\n                                                \"⚠️ Backend server not running. Please start it with: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 72\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-2 py-1 rounded text-xs mt-1 inline-block\",\n                                                    children: \"cd backend && uvicorn app.main:app --reload\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"pw5yoFpg+6vzMVV4FBiH157mCdM=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});