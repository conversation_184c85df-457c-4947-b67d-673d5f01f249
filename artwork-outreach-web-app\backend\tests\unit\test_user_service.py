import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import date, timedelta
import uuid

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.user_service import (
    UserService,
    RequestLimitService,
    get_or_create_user_profile,
    check_and_increment_request_count,
)
from app.database.models import UserProfile
from app.core.exceptions import RateLimitExceededException
from app.schemas.user import UserProfileUpdate

@pytest.fixture
def mock_db_session():
    """Fixture for a mocked SQLAlchemy AsyncSession."""
    session = AsyncMock(spec=AsyncSession)
    session.execute = AsyncMock()
    session.get = AsyncMock()
    return session

@pytest.fixture
def user_service(mock_db_session: AsyncSession):
    """Fixture for the UserService."""
    return UserService(db=mock_db_session)

@pytest.fixture
def request_limit_service(mock_db_session: AsyncSession):
    """Fixture for the RequestLimitService."""
    return RequestLimitService(db=mock_db_session)

@pytest.mark.asyncio
async def test_update_user_profile(user_service: UserService, mock_db_session: AsyncSession):
    """Test updating a user profile."""
    user_id = str(uuid.uuid4())
    profile_update = UserProfileUpdate(full_name="New Name")
    
    mock_user = UserProfile(id=user_id, full_name="Old Name")
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_user

    updated_user = await user_service.update_user_profile(user_id, profile_update)

    assert updated_user.full_name == "New Name"
    mock_db_session.commit.assert_called_once()
    mock_db_session.refresh.assert_called_once()

@pytest.mark.asyncio
async def test_check_daily_limit(request_limit_service: RequestLimitService, mock_db_session: AsyncSession):
    """Test checking the daily request limit for a user."""
    user_id = 1
    mock_user = UserProfile(id=user_id, daily_request_count=1, last_request_date=date.today())
    mock_db_session.get.return_value = mock_user

    remaining = await request_limit_service.check_daily_limit(user_id)

    assert remaining == 2

@pytest.mark.asyncio
async def test_increment_request_count(request_limit_service: RequestLimitService, mock_db_session: AsyncSession):
    """Test incrementing the request count for a user."""
    user_id = 1
    mock_user = UserProfile(id=user_id, daily_request_count=1, last_request_date=date.today())
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_user

    await request_limit_service.increment_request_count(user_id)

    assert mock_user.daily_request_count == 2
    mock_db_session.commit.assert_called_once()

@pytest.mark.asyncio
async def test_get_or_create_user_profile_existing(mock_db_session: AsyncSession):
    """Test getting an existing user profile."""
    user_id = uuid.uuid4()
    claims = {"user_id": str(user_id)}
    mock_user = UserProfile(id=user_id)
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_user

    user_profile = await get_or_create_user_profile(mock_db_session, claims)

    assert user_profile.id == user_id
    mock_db_session.add.assert_not_called()

@pytest.mark.asyncio
async def test_get_or_create_user_profile_new(mock_db_session: AsyncSession):
    """Test creating a new user profile."""
    user_id = uuid.uuid4()
    claims = {"user_id": str(user_id), "email": "<EMAIL>", "full_name": "Test User"}
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

    user_profile = await get_or_create_user_profile(mock_db_session, claims)

    mock_db_session.add.assert_called_once()
    mock_db_session.commit.assert_called_once()
    mock_db_session.refresh.assert_called_once()
    assert user_profile.id == user_id

@pytest.mark.asyncio
async def test_check_and_increment_request_count_success(mock_db_session: AsyncSession):
    """Test successfully checking and incrementing the request count."""
    user = UserProfile(daily_request_count=1, last_request_date=date.today())
    
    await check_and_increment_request_count(user, mock_db_session)

    assert user.daily_request_count == 2
    mock_db_session.commit.assert_called_once()

@pytest.mark.asyncio
async def test_check_and_increment_request_count_limit_exceeded(mock_db_session: AsyncSession):
    """Test that RateLimitExceededException is raised when the limit is reached."""
    user = UserProfile(daily_request_count=3, last_request_date=date.today())

    with pytest.raises(RateLimitExceededException):
        await check_and_increment_request_count(user, mock_db_session)

    mock_db_session.commit.assert_not_called()