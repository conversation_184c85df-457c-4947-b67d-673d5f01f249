"""Integrated Twitch scraper for FastAPI backend"""

import asyncio
import time
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import deque
import threading

from app.config import get_settings
from app.database.connection import AsyncSessionLocal
from app.database.models import Stream<PERSON>, ScraperRun
from sqlalchemy import select, update
from sqlalchemy.dialects.postgresql import insert

settings = get_settings()
logger = logging.getLogger(__name__)

# Import games configuration from main config
try:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from config import GAMES_TO_SCRAPE
    RPG_GAMES = GAMES_TO_SCRAPE
except ImportError:
    # Fallback to original list if import fails
    RPG_GAMES = [
        'World of Warcraft',
        'Final Fantasy XIV Online',
        'The Elder Scrolls V: Skyrim',
        'Baldur\'s Gate 3',
        'Diablo IV',
        'Path of Exile',
        'Lost Ark',
        'Elden Ring',
        'The Witcher 3: Wild Hunt',
        'Cyberpunk 2077',
        'Divinity: Original Sin 2',
        'Persona 5 Royal',
        'Dragon Age: The Veilguard',
        'Hogwarts Legacy',
        'Starfield',
        'Fallout 4',
        'Dark Souls III',
        'Monster Hunter: World',
        'Genshin Impact',
        'Guild Wars 2'
    ]

class RateLimiter:
    """Thread-safe rate limiter for API calls."""
    
    def __init__(self, requests_per_minute: int = 600):
        self.requests_per_minute = requests_per_minute
        self.min_interval = 60.0 / requests_per_minute
        self.request_times = deque()
        self.lock = threading.Lock()
    
    def wait_if_needed(self):
        """Wait if necessary to respect rate limits."""
        with self.lock:
            now = time.time()
            
            # Remove requests older than 1 minute
            while self.request_times and now - self.request_times[0] > 60:
                self.request_times.popleft()
            
            # If we're at the limit, wait
            if len(self.request_times) >= self.requests_per_minute:
                sleep_time = 60 - (now - self.request_times[0]) + 0.1
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    now = time.time()
                    # Clean up old requests again
                    while self.request_times and now - self.request_times[0] > 60:
                        self.request_times.popleft()
            
            # Record this request
            self.request_times.append(now)

class TwitchAPI:
    """Handle Twitch API interactions."""
    
    def __init__(self, rate_limiter: RateLimiter = None):
        self.client_id = settings.TWITCH_CLIENT_ID
        self.client_secret = settings.TWITCH_CLIENT_SECRET
        self.access_token = None
        self.headers = {}
        self.rate_limiter = rate_limiter or RateLimiter(600)
        
    def authenticate(self) -> bool:
        """Get OAuth token for Twitch API."""
        if not self.client_id or not self.client_secret:
            logger.error("Twitch API credentials not found")
            return False
            
        auth_url = "https://id.twitch.tv/oauth2/token"
        auth_params = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'client_credentials'
        }
        
        try:
            response = requests.post(auth_url, params=auth_params)
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data['access_token']
            
            self.headers = {
                'Client-ID': self.client_id,
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            logger.info("Successfully authenticated with Twitch API")
            return True
            
        except Exception as e:
            logger.error(f"Failed to authenticate with Twitch API: {e}")
            return False
    
    def get_user_info(self, username: str) -> Optional[Dict]:
        """Get user information from Twitch API."""
        if not self.headers:
            if not self.authenticate():
                return None
        
        self.rate_limiter.wait_if_needed()
        
        try:
            url = f"https://api.twitch.tv/helix/users?login={username}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            data = response.json()
            if data.get('data'):
                return data['data'][0]
            return None
            
        except Exception as e:
            logger.error(f"Error getting user info for {username}: {e}")
            return None
    
    def get_followers(self, user_id: str) -> int:
        """Get follower count for a user."""
        if not self.headers:
            if not self.authenticate():
                return 0
        
        self.rate_limiter.wait_if_needed()
        
        try:
            url = f"https://api.twitch.tv/helix/channels/followers?broadcaster_id={user_id}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            data = response.json()
            return data.get('total', 0)
            
        except Exception as e:
            logger.error(f"Error getting followers for user {user_id}: {e}")
            return 0



class TwitchScraper:
    """Main scraper class for Twitch streams."""
    
    def __init__(self):
        self.api = TwitchAPI()
        self.scraped_streamers = []
        
    def setup_driver(self) -> webdriver.Chrome:
        """Set up Chrome webdriver."""
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.implicitly_wait(10)
        
        return driver
    
    def scrape_game_streamers(self, game_name: str, max_streamers: int = 100) -> List[Dict]:
        """Scrape streamers for a specific game."""
        driver = self.setup_driver()
        streamers = []
        
        try:
            # Navigate to game directory
            game_url = f"https://www.twitch.tv/directory/game/{game_name.replace(' ', '%20')}"
            driver.get(game_url)
            
            # Wait for page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-target='directory-first-item']"))
            )
            
            # Scroll and collect streamers
            last_height = driver.execute_script("return document.body.scrollHeight")
            collected_count = 0
            
            while collected_count < max_streamers:
                # Find stream elements
                stream_elements = driver.find_elements(By.CSS_SELECTOR, "[data-target='directory-first-item']")
                
                for element in stream_elements[collected_count:]:
                    if collected_count >= max_streamers:
                        break
                    
                    try:
                        # Extract streamer information
                        username_element = element.find_element(By.CSS_SELECTOR, "[data-a-target='preview-card-channel-link']")
                        username = username_element.get_attribute('href').split('/')[-1]
                        
                        viewer_element = element.find_element(By.CSS_SELECTOR, "[data-a-target='preview-card-viewer-count']")
                        viewer_count_text = viewer_element.text.strip()
                        
                        # Skip if too many viewers (likely high follower count)
                        if 'K' in viewer_count_text or int(viewer_count_text.replace(',', '')) > 100:
                            continue
                        
                        title_element = element.find_element(By.CSS_SELECTOR, "[data-a-target='preview-card-title-link']")
                        title = title_element.text.strip()
                        
                        streamers.append({
                            'username': username,
                            'title': title,
                            'game': game_name,
                            'viewer_count': viewer_count_text
                        })
                        
                        collected_count += 1
                        
                    except Exception as e:
                        logger.debug(f"Error extracting streamer info: {e}")
                        continue
                
                # Scroll down
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                # Check if we've reached the bottom
                new_height = driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
                
        except Exception as e:
            logger.error(f"Error scraping {game_name}: {e}")
        
        finally:
            driver.quit()
        
        logger.info(f"Scraped {len(streamers)} streamers from {game_name}")
        return streamers
    
    async def process_and_store_streamers(self, raw_streamers: List[Dict]) -> int:
        """Process scraped streamers and store in database."""
        if not self.api.authenticate():
            logger.error("Failed to authenticate with Twitch API")
            return 0
        
        processed_count = 0
        
        # Process streamers with API calls
        with ThreadPoolExecutor(max_workers=30) as executor:
            future_to_streamer = {
                executor.submit(self._process_single_streamer, streamer): streamer 
                for streamer in raw_streamers
            }
            
            for future in as_completed(future_to_streamer):
                try:
                    processed_streamer = future.result()
                    if processed_streamer:
                        self.scraped_streamers.append(processed_streamer)
                        processed_count += 1
                except Exception as e:
                    logger.error(f"Error processing streamer: {e}")
        
        # Store in database
        await self._store_streamers_in_db(self.scraped_streamers)
        
        return processed_count
    
    def _process_single_streamer(self, raw_streamer: Dict) -> Optional[Dict]:
        """Process a single streamer with API calls."""
        try:
            user_info = self.api.get_user_info(raw_streamer['username'])
            if not user_info:
                return None
            
            follower_count = self.api.get_followers(user_info['id'])
            
            # Filter by follower count (0-50)
            if follower_count > 50:
                return None
            
            return {
                'twitch_user_id': user_info['id'],
                'username': user_info['login'],
                'display_name': user_info['display_name'],
                'follower_count': follower_count,
                'profile_image_url': user_info['profile_image_url'],
                'current_game': raw_streamer['game'],
                'stream_title': raw_streamer['title'],
                'is_live': True,
                'language': 'en',
                'last_seen_live_at': datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Error processing streamer {raw_streamer['username']}: {e}")
            return None
    
    async def _store_streamers_in_db(self, streamers: List[Dict]):
        """Store processed streamers in database."""
        if not streamers:
            return
        
        async with AsyncSessionLocal() as session:
            try:
                for streamer_data in streamers:
                    # Use PostgreSQL UPSERT (INSERT ... ON CONFLICT)
                    stmt = insert(Streamer).values(**streamer_data)
                    stmt = stmt.on_conflict_do_update(
                        index_elements=['twitch_user_id'],
                        set_={
                            'follower_count': stmt.excluded.follower_count,
                            'is_live': stmt.excluded.is_live,
                            'current_game': stmt.excluded.current_game,
                            'stream_title': stmt.excluded.stream_title,
                            'last_seen_live_at': stmt.excluded.last_seen_live_at,
                            'updated_at': datetime.utcnow()
                        }
                    )
                    await session.execute(stmt)
                
                await session.commit()
                logger.info(f"Stored {len(streamers)} streamers in database")
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error storing streamers: {e}")
                raise
    
    async def run_full_scrape(self) -> int:
        """Run a complete scraping cycle for all RPG games."""
        logger.info("Starting full scrape cycle")
        start_time = datetime.utcnow()
        
        # Record scraper run start
        async with AsyncSessionLocal() as session:
            scraper_run = ScraperRun(
                status="started",
                details="Starting scrape of all RPG games"
            )
            session.add(scraper_run)
            await session.commit()
            run_id = scraper_run.id
        
        total_processed = 0
        
        try:
            # Scrape each game
            for game in RPG_GAMES:
                logger.info(f"Scraping {game}...")
                raw_streamers = self.scrape_game_streamers(game, max_streamers=50)
                processed = await self.process_and_store_streamers(raw_streamers)
                total_processed += processed
                logger.info(f"Processed {processed} streamers from {game}")
                
                # Brief pause between games
                await asyncio.sleep(1)
            
            # Update scraper run as completed
            async with AsyncSessionLocal() as session:
                await session.execute(
                    update(ScraperRun)
                    .where(ScraperRun.id == run_id)
                    .values(
                        status="completed",
                        streamers_found=total_processed,
                        details=f"Successfully scraped {total_processed} streamers from {len(RPG_GAMES)} games"
                    )
                )
                await session.commit()
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            logger.info(f"Scrape completed in {duration:.1f}s. Found {total_processed} streamers.")
            
        except Exception as e:
            # Update scraper run as failed
            async with AsyncSessionLocal() as session:
                await session.execute(
                    update(ScraperRun)
                    .where(ScraperRun.id == run_id)
                    .values(
                        status="failed",
                        streamers_found=total_processed,
                        details=f"Scrape failed: {str(e)}"
                    )
                )
                await session.commit()
            
            logger.error(f"Scrape failed: {e}")
            raise
        
        return total_processed
