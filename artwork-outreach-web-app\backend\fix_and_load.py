#!/usr/bin/env python3
"""
Fix CSV merge conflicts and load data with better error handling
"""

import os
import csv
import requests
import json
from pathlib import Path

# Backend API configuration
BACKEND_URL = "http://127.0.0.1:8000"
API_BASE = f"{BACKEND_URL}/api/v1"

def clean_csv_file(input_file, output_file):
    """Remove Git merge conflict markers from CSV file"""
    print(f"🧹 Cleaning CSV file: {input_file}")
    
    lines = []
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            # Skip Git merge conflict markers
            if line.strip().startswith(('<<<<<<< HEAD', '=======', '>>>>>>>')):
                continue
            lines.append(line)
    
    # Remove duplicate headers (keep only the first one)
    header_line = "username,display_name,twitch_url,follower_count,streams_per_week,language,title,game_name,viewer_count,started_at,thumbnail_url"
    
    cleaned_lines = []
    header_added = False
    
    for line in lines:
        if line.strip() == header_line:
            if not header_added:
                cleaned_lines.append(line)
                header_added = True
        else:
            cleaned_lines.append(line)
    
    # Write cleaned file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(cleaned_lines)
    
    print(f"✅ Cleaned CSV saved to: {output_file}")
    return output_file

def test_backend_connection():
    """Test if we can connect to backend at all"""
    try:
        response = requests.get(f"{BACKEND_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is accessible!")
            return True
        else:
            print(f"⚠️  Backend responded with: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False

def load_csv_data_direct(csv_path):
    """Load CSV data using direct database queries (bypass API)"""
    import sqlite3
    
    print(f"📊 Loading data directly to local SQLite database...")
    
    # Create local SQLite database
    db_path = "test_streamers.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS streamers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            twitch_user_id TEXT UNIQUE,
            username TEXT,
            display_name TEXT,
            follower_count INTEGER,
            is_live BOOLEAN,
            current_game TEXT,
            stream_title TEXT,
            thumbnail_url TEXT,
            language TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Read and insert CSV data
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        success_count = 0
        
        for i, row in enumerate(reader, 1):
            try:
                # Clean and validate data
                username = row.get('username', '').strip()
                if not username:
                    print(f"⚠️  {i} - Skipping empty username")
                    continue
                
                # Insert data
                cursor.execute('''
                    INSERT OR REPLACE INTO streamers 
                    (twitch_user_id, username, display_name, follower_count, is_live, current_game, stream_title, thumbnail_url, language)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    username,  # Use username as ID
                    username,
                    row.get('display_name', username),
                    int(row.get('follower_count', 0)),
                    True,  # They were live when scraped
                    row.get('game_name', ''),
                    row.get('title', ''),
                    row.get('thumbnail_url', ''),
                    row.get('language', 'en')
                ))
                
                success_count += 1
                print(f"✅ {i:2d} - {username}")
                
            except Exception as e:
                print(f"❌ {i:2d} - Error: {e}")
    
    conn.commit()
    
    # Show results
    cursor.execute("SELECT COUNT(*) FROM streamers")
    total = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM streamers WHERE follower_count BETWEEN 0 AND 50")
    available = cursor.fetchone()[0]
    
    print(f"\n📈 Local Database Results:")
    print(f"   Total streamers: {total}")
    print(f"   Available (0-50 followers): {available}")
    print(f"   Database saved to: {db_path}")
    
    # Show sample data
    cursor.execute("SELECT username, display_name, follower_count, current_game FROM streamers LIMIT 5")
    samples = cursor.fetchall()
    print(f"\n📋 Sample Data:")
    for username, display_name, followers, game in samples:
        print(f"   {username} ({display_name}) - {followers} followers - {game}")
    
    conn.close()
    return success_count > 0

def main():
    """Main function"""
    print("🚀 CSV Fixer and Data Loader\n")
    
    # Find CSV file
    csv_file = "twitch_streams_filtered.csv"
    if not Path(csv_file).exists():
        print(f"❌ CSV file not found: {csv_file}")
        return
    
    # Clean CSV file
    clean_csv = "twitch_streams_clean.csv"
    cleaned_file = clean_csv_file(csv_file, clean_csv)
    
    # Test backend connection
    if test_backend_connection():
        print("🔄 Backend is running, but has database connection issues")
        print("💡 Using local SQLite database instead")
    
    # Load data directly to local database
    if load_csv_data_direct(cleaned_file):
        print("\n🎉 Data loading completed successfully!")
        print("\n📋 Summary:")
        print("   ✅ CSV file cleaned and parsed")
        print("   ✅ Data stored in local SQLite database")
        print("   📁 File: test_streamers.db")
        print("\n💡 Next steps:")
        print("   1. Fix backend Supabase connection")
        print("   2. Migrate data from SQLite to Supabase")
        print("   3. Test frontend with real data")
    else:
        print("\n❌ Data loading failed!")

if __name__ == "__main__":
    main()