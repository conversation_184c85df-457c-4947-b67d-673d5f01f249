import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta, timezone
import base64

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.streamer_service import StreamerService
from app.database.models import Streamer, Assignment

@pytest.fixture
def mock_db_session():
    """Fixture for a mocked SQLAlchemy AsyncSession."""
    session = AsyncMock(spec=AsyncSession)
    session.execute = AsyncMock()
    return session

@pytest.fixture
def streamer_service(mock_db_session: AsyncSession):
    """Fixture for the StreamerService."""
    return StreamerService(db=mock_db_session)

@pytest.mark.asyncio
async def test_get_streamer_by_id(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test fetching a streamer by their Twitch user ID."""
    mock_streamer = Streamer(twitch_user_id="12345", username="test_streamer")
    mock_db_session.execute.return_value.scalars.return_value.first.return_value = mock_streamer

    streamer = await streamer_service.get_streamer_by_id("12345")

    assert streamer is not None
    assert streamer.twitch_user_id == "12345"
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_get_available_streamers(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test fetching available streamers based on specific criteria."""
    mock_streamers = [Streamer(twitch_user_id=f"id_{i}", username=f"streamer_{i}", follower_count=i) for i in range(5)]
    mock_db_session.execute.return_value.scalars.return_value.all.return_value = mock_streamers

    streamers = await streamer_service.get_available_streamers()

    assert len(streamers) == 5
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_get_unassigned_live_streamers(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test fetching unassigned live streamers."""
    mock_streamers = [Streamer(twitch_user_id=f"id_{i}", username=f"streamer_{i}", is_live=True) for i in range(3)]
    mock_db_session.execute.return_value.scalars.return_value.all.return_value = mock_streamers

    streamers = await streamer_service.get_unassigned_live_streamers(limit=3)

    assert len(streamers) == 3
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_get_streamers_paginated(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test fetching streamers with pagination."""
    mock_streamers = [Streamer(id=i, twitch_user_id=f"id_{i}", username=f"s_{i}", created_at=datetime.now(timezone.utc)) for i in range(1, 11)]
    
    # Simulate having a next page
    mock_db_session.execute.return_value.scalars.return_value.all.return_value = mock_streamers

    streamers, next_cursor, _ = await streamer_service.get_streamers_paginated(cursor=None, limit=10)

    assert len(streamers) == 10
    assert next_cursor is not None
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_search_streamers(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test searching for streamers."""
    mock_streamers = [Streamer(username="test_streamer"), Streamer(username="another_streamer")]
    mock_db_session.execute.return_value.scalars.return_value.all.return_value = mock_streamers

    streamers = await streamer_service.search_streamers("test")

    assert len(streamers) == 2
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_get_total_streamers_count(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test getting the total count of streamers."""
    mock_db_session.execute.return_value.scalar_one.return_value = 150

    count = await streamer_service.get_total_streamers_count()

    assert count == 150
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_get_live_streamers_count(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test getting the count of live streamers."""
    mock_db_session.execute.return_value.scalar_one.return_value = 25

    count = await streamer_service.get_live_streamers_count()

    assert count == 25
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_get_available_streamers_count(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test getting the count of available streamers."""
    mock_db_session.execute.return_value.scalar_one.return_value = 10

    count = await streamer_service.get_available_streamers_count()

    assert count == 10
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_get_average_follower_count(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test getting the average follower count of streamers."""
    mock_db_session.execute.return_value.scalar_one.return_value = 1234.56

    avg_followers = await streamer_service.get_average_follower_count()

    assert avg_followers == 1234.56
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_get_streamers_paginated_with_cursor(streamer_service: StreamerService, mock_db_session: AsyncSession):
    """Test fetching streamers with pagination using a cursor."""
    last_streamer = Streamer(id=10, twitch_user_id="id_10", username="s_10", created_at=datetime.now(timezone.utc))
    cursor_str = f"{last_streamer.created_at.isoformat()}|{last_streamer.id}"
    cursor = base64.urlsafe_b64encode(cursor_str.encode()).decode()

    mock_streamers = [Streamer(id=i, twitch_user_id=f"id_{i}", username=f"s_{i}", created_at=datetime.now(timezone.utc)) for i in range(11, 21)]
    mock_db_session.execute.return_value.scalars.return_value.all.return_value = mock_streamers

    streamers, _, _ = await streamer_service.get_streamers_paginated(cursor=cursor, limit=10)

    assert len(streamers) == 10
    mock_db_session.execute.assert_called_once()