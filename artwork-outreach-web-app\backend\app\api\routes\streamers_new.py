"""Streamer API endpoints - New Implementation"""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, text
from sqlalchemy.orm import selectinload

from app.database.connection import get_db
from app.database.models import Streamer, Assignment, UserProfile
from app.schemas.streamer_simple import StreamerResponse, StreamerListResponse
from app.api.dependencies import get_current_user
from app.scraper.background_tasks import add_scrape_task, get_scraper_status

router = APIRouter(prefix="/api/v1/streamers", tags=["streamers"])

@router.get("/available", response_model=StreamerListResponse)
async def get_available_streamers(
    limit: int = 50,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get list of available streamers for assignment.
    
    Returns streamers that:
    - Have 0-50 followers
    - Are English language
    - Haven't been assigned in the last 7 days
    - Prioritizes currently live streamers
    """
    try:
        # Check user's daily request limit
        user_email = current_user.get("email")
        if not user_email:
            raise HTTPException(status_code=401, detail="User email not found")
        
        # Get or create user profile
        user_result = await db.execute(
            select(UserProfile).where(UserProfile.email == user_email)
        )
        user_profile = user_result.scalar_one_or_none()
        
        if not user_profile:
            # Create new user profile
            user_profile = UserProfile(
                email=user_email,
                full_name=current_user.get("name", ""),
                daily_request_count=0,
                last_request_date=None
            )
            db.add(user_profile)
            await db.flush()  # Get the ID
        
        # Check daily request limit (3 per day)
        today = datetime.utcnow().date()
        if (user_profile.last_request_date and 
            user_profile.last_request_date.date() == today and 
            user_profile.daily_request_count >= 3):
            raise HTTPException(
                status_code=429, 
                detail=f"Daily request limit reached (3/day). Try again tomorrow."
            )
        
        # Get streamers that haven't been assigned in the last 7 days
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        
        # Subquery for recently assigned streamer IDs
        recent_assignments_subq = (
            select(Assignment.streamer_id)
            .where(Assignment.assigned_at > seven_days_ago)
            .where(Assignment.deleted_at.is_(None))
        ).subquery()
        
        # Main query for available streamers
        query = (
            select(Streamer)
            .where(
                and_(
                    Streamer.follower_count >= 0,
                    Streamer.follower_count <= 50,
                    Streamer.language == 'en',
                    ~Streamer.twitch_user_id.in_(select(recent_assignments_subq.c.streamer_id))
                )
            )
            .order_by(
                Streamer.is_live.desc(),  # Live streamers first
                Streamer.follower_count.desc(),  # Higher followers first (within 0-50 range)
                func.random()  # Random for fairness
            )
            .limit(limit)
        )
        
        result = await db.execute(query)
        streamers = result.scalars().all()
        
        if not streamers:
            raise HTTPException(
                status_code=404, 
                detail="No available streamers found. Try again later or contact admin."
            )
        
        # Create assignments for returned streamers
        assignments = []
        for streamer in streamers:
            assignment = Assignment(
                agent_id=user_profile.id,
                streamer_id=streamer.twitch_user_id,
                status="assigned",
                assigned_at=datetime.utcnow()
            )
            db.add(assignment)
            assignments.append(assignment)
        
        # Update user's daily request count
        if not user_profile.last_request_date or user_profile.last_request_date.date() != today:
            user_profile.daily_request_count = 1
        else:
            user_profile.daily_request_count += 1
        user_profile.last_request_date = datetime.utcnow()
        
        await db.commit()
        
        # Convert to response format
        streamer_responses = [
            StreamerResponse(
                twitch_user_id=s.twitch_user_id,
                username=s.username,
                display_name=s.display_name or s.username,
                follower_count=s.follower_count,
                is_live=s.is_live,
                current_game=s.current_game,
                stream_title=s.stream_title,
                profile_image_url=s.profile_image_url,
                language=s.language,
                last_seen_live_at=s.last_seen_live_at,
                twitch_url=f"https://twitch.tv/{s.username}"
            )
            for s in streamers
        ]
        
        return StreamerListResponse(
            streamers=streamer_responses,
            total=len(streamer_responses),
            user_status={
                "daily_requests_used": user_profile.daily_request_count,
                "daily_requests_remaining": 3 - user_profile.daily_request_count,
                "can_make_request": user_profile.daily_request_count < 3
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/stats")
async def get_streamer_stats(
    db: AsyncSession = Depends(get_db)
):
    """Get statistics about streamers in the database."""
    try:
        # Total streamers
        total_result = await db.execute(
            select(func.count()).select_from(Streamer)
        )
        total_streamers = total_result.scalar()
        
        # Live streamers
        live_result = await db.execute(
            select(func.count()).select_from(Streamer).where(Streamer.is_live == True)
        )
        live_streamers = live_result.scalar()
        
        # Available streamers (not assigned in last 7 days)
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        
        recent_assignments_subq = (
            select(Assignment.streamer_id)
            .where(Assignment.assigned_at > seven_days_ago)
            .where(Assignment.deleted_at.is_(None))
        ).subquery()
        
        available_result = await db.execute(
            select(func.count()).select_from(Streamer)
            .where(
                and_(
                    Streamer.follower_count >= 0,
                    Streamer.follower_count <= 50,
                    Streamer.language == 'en',
                    ~Streamer.twitch_user_id.in_(select(recent_assignments_subq.c.streamer_id))
                )
            )
        )
        available_streamers = available_result.scalar()
        
        return {
            "total_streamers": total_streamers,
            "live_streamers": live_streamers,
            "available_streamers": available_streamers,
            "last_updated": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving stats: {str(e)}")

@router.post("/trigger-scrape")
async def trigger_scrape(
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Manually trigger a scraping task (admin only)."""
    # Add basic admin check (can be enhanced)
    user_email = current_user.get("email", "")
    # For now, allow any authenticated user to trigger scrape for testing
    
    add_scrape_task(background_tasks)
    
    return {
        "message": "Scraping task triggered",
        "status": "queued",
        "triggered_by": user_email
    }

@router.get("/scraper-status")
async def get_scraper_status_endpoint():
    """Get current status of the scraper."""
    try:
        status = await get_scraper_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting scraper status: {str(e)}")