{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/resolve-route-data.ts"], "names": ["resolveManifest", "resolveRobots", "resolveRouteData", "resolveSitemap", "data", "content", "rules", "Array", "isArray", "rule", "userAgent", "resolveArray", "agent", "allow", "item", "disallow", "crawlDelay", "host", "sitemap", "for<PERSON>ach", "hasAlternates", "some", "Object", "keys", "alternates", "length", "url", "languages", "language", "lastModified", "serializedDate", "Date", "toISOString", "changeFrequency", "priority", "JSON", "stringify", "fileType"], "mappings": ";;;;;;;;;;;;;;;;;IAiGgBA,eAAe;eAAfA;;IA7FAC,aAAa;eAAbA;;IAiGAC,gBAAgB;eAAhBA;;IAxDAC,cAAc;eAAdA;;;uBA5Ca;AAGtB,SAASF,cAAcG,IAA0B;IACtD,IAAIC,UAAU;IACd,MAAMC,QAAQC,MAAMC,OAAO,CAACJ,KAAKE,KAAK,IAAIF,KAAKE,KAAK,GAAG;QAACF,KAAKE,KAAK;KAAC;IACnE,KAAK,MAAMG,QAAQH,MAAO;QACxB,MAAMI,YAAYC,IAAAA,mBAAY,EAACF,KAAKC,SAAS,IAAI;YAAC;SAAI;QACtD,KAAK,MAAME,SAASF,UAAW;YAC7BL,WAAW,CAAC,YAAY,EAAEO,MAAM,EAAE,CAAC;QACrC;QACA,IAAIH,KAAKI,KAAK,EAAE;YACd,MAAMA,QAAQF,IAAAA,mBAAY,EAACF,KAAKI,KAAK;YACrC,KAAK,MAAMC,QAAQD,MAAO;gBACxBR,WAAW,CAAC,OAAO,EAAES,KAAK,EAAE,CAAC;YAC/B;QACF;QACA,IAAIL,KAAKM,QAAQ,EAAE;YACjB,MAAMA,WAAWJ,IAAAA,mBAAY,EAACF,KAAKM,QAAQ;YAC3C,KAAK,MAAMD,QAAQC,SAAU;gBAC3BV,WAAW,CAAC,UAAU,EAAES,KAAK,EAAE,CAAC;YAClC;QACF;QACA,IAAIL,KAAKO,UAAU,EAAE;YACnBX,WAAW,CAAC,aAAa,EAAEI,KAAKO,UAAU,CAAC,EAAE,CAAC;QAChD;QACAX,WAAW;IACb;IACA,IAAID,KAAKa,IAAI,EAAE;QACbZ,WAAW,CAAC,MAAM,EAAED,KAAKa,IAAI,CAAC,EAAE,CAAC;IACnC;IACA,IAAIb,KAAKc,OAAO,EAAE;QAChB,MAAMA,UAAUP,IAAAA,mBAAY,EAACP,KAAKc,OAAO;QACzC,+DAA+D;QAC/DA,QAAQC,OAAO,CAAC,CAACL;YACfT,WAAW,CAAC,SAAS,EAAES,KAAK,EAAE,CAAC;QACjC;IACF;IAEA,OAAOT;AACT;AAIO,SAASF,eAAeC,IAA2B;IACxD,MAAMgB,gBAAgBhB,KAAKiB,IAAI,CAC7B,CAACP,OAASQ,OAAOC,IAAI,CAACT,KAAKU,UAAU,IAAI,CAAC,GAAGC,MAAM,GAAG;IAGxD,IAAIpB,UAAU;IACdA,WAAW;IACXA,WAAW;IACX,IAAIe,eAAe;QACjBf,WAAW;IACb,OAAO;QACLA,WAAW;IACb;IACA,KAAK,MAAMS,QAAQV,KAAM;YAILU;QAHlBT,WAAW;QACXA,WAAW,CAAC,KAAK,EAAES,KAAKY,GAAG,CAAC,QAAQ,CAAC;QAErC,MAAMC,aAAYb,mBAAAA,KAAKU,UAAU,qBAAfV,iBAAiBa,SAAS;QAC5C,IAAIA,aAAaL,OAAOC,IAAI,CAACI,WAAWF,MAAM,EAAE;YAC9C,+FAA+F;YAC/F,yEAAyE;YACzE,IAAK,MAAMG,YAAYD,UAAW;gBAChCtB,WAAW,CAAC,sCAAsC,EAAEuB,SAAS,QAAQ,EACnED,SAAS,CAACC,SAAmC,CAC9C,MAAM,CAAC;YACV;QACF;QACA,IAAId,KAAKe,YAAY,EAAE;YACrB,MAAMC,iBACJhB,KAAKe,YAAY,YAAYE,OACzBjB,KAAKe,YAAY,CAACG,WAAW,KAC7BlB,KAAKe,YAAY;YAEvBxB,WAAW,CAAC,SAAS,EAAEyB,eAAe,YAAY,CAAC;QACrD;QAEA,IAAIhB,KAAKmB,eAAe,EAAE;YACxB5B,WAAW,CAAC,YAAY,EAAES,KAAKmB,eAAe,CAAC,eAAe,CAAC;QACjE;QAEA,IAAI,OAAOnB,KAAKoB,QAAQ,KAAK,UAAU;YACrC7B,WAAW,CAAC,UAAU,EAAES,KAAKoB,QAAQ,CAAC,aAAa,CAAC;QACtD;QAEA7B,WAAW;IACb;IAEAA,WAAW;IAEX,OAAOA;AACT;AAEO,SAASL,gBAAgBI,IAA4B;IAC1D,OAAO+B,KAAKC,SAAS,CAAChC;AACxB;AAEO,SAASF,iBACdE,IAA2E,EAC3EiC,QAA2C;IAE3C,IAAIA,aAAa,UAAU;QACzB,OAAOpC,cAAcG;IACvB;IACA,IAAIiC,aAAa,WAAW;QAC1B,OAAOlC,eAAeC;IACxB;IACA,IAAIiC,aAAa,YAAY;QAC3B,OAAOrC,gBAAgBI;IACzB;IACA,OAAO;AACT"}