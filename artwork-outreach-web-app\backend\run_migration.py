import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def run_migration():
    load_dotenv()
    
    # Parse DATABASE_URL to get connection parameters
    db_url = os.getenv('DATABASE_URL')
    # Convert asyncpg URL to regular postgres URL for parsing
    if 'postgresql+asyncpg://' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    
    # Read migration file
    with open('app/database/migrations/002_add_streamer_quota.sql', 'r') as f:
        migration_sql = f.read()
    
    # Connect and run migration
    conn = await asyncpg.connect(db_url)
    try:
        print('Running migration: 002_add_streamer_quota.sql')
        await conn.execute(migration_sql)
        print('✅ Migration completed successfully!')
    except Exception as e:
        print(f'❌ Migration failed: {e}')
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(run_migration())
