import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from httpx import AsyncClient

from app.database.models import UserProfile
from app.api.dependencies import get_current_user
from app.main import app
from app.services.user_service import UserService


def create_mock_user(email_suffix: str = ""):
    """Create a mock user with unique email."""
    unique_id = str(uuid.uuid4())[:8]
    email = f"test{email_suffix}_{unique_id}@example.com"
    return UserProfile(
        id=uuid.uuid4(),
        full_name="Test User",
        email=email,
        role="agent",
        daily_request_count=5,
    )


@pytest.mark.asyncio
async def test_get_user_profile_working():
    """Test user profile retrieval with mocked dependencies."""
    mock_user = create_mock_user("_profile_working")
    
    # Mock all dependencies
    with patch('app.api.routes.users.get_user_service') as mock_get_service:
        mock_service = AsyncMock(spec=UserService)
        mock_service.get_user_profile.return_value = mock_user
        mock_get_service.return_value = mock_service
        
        # Override current user dependency
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/api/v1/users/profile")
            
        # Cleanup
        app.dependency_overrides.clear()
        
        assert response.status_code == 200
        json_response = response.json()
        assert json_response["id"] == str(mock_user.id)
        assert json_response["full_name"] == mock_user.full_name
        assert json_response["email"] == mock_user.email


@pytest.mark.asyncio
async def test_get_user_status_working():
    """Test user status retrieval with mocked dependencies."""
    mock_user = create_mock_user("_status_working")
    mock_status = {
        "daily_request_count": 3,
        "remaining_requests": 0,
        "next_reset_time": "2024-01-01T00:00:00Z"
    }
    
    with patch('app.api.routes.users.get_user_service') as mock_get_service:
        mock_service = AsyncMock(spec=UserService)
        mock_service.get_user_status.return_value = mock_status
        mock_get_service.return_value = mock_service
        
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/api/v1/users/status")
            
        app.dependency_overrides.clear()
        
        assert response.status_code == 200
        json_response = response.json()
        assert json_response["daily_request_count"] == 3
        assert "remaining_requests" in json_response
        assert "next_reset_time" in json_response


@pytest.mark.asyncio
async def test_update_user_profile_working():
    """Test user profile update with mocked dependencies."""
    mock_user = create_mock_user("_update_working")
    updated_user = create_mock_user("_updated_working")
    updated_user.id = mock_user.id
    updated_user.full_name = "Updated Name"
    
    with patch('app.api.routes.users.get_user_service') as mock_get_service:
        mock_service = AsyncMock(spec=UserService)
        mock_service.update_user_profile.return_value = updated_user
        mock_get_service.return_value = mock_service
        
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.patch(
                "/api/v1/users/profile", 
                json={"full_name": "Updated Name"}
            )
            
        app.dependency_overrides.clear()
        
        assert response.status_code == 200
        json_response = response.json()
        assert json_response["full_name"] == "Updated Name"
        
        # Verify service was called correctly
        mock_service.update_user_profile.assert_called_once_with(
            mock_user.id, 
            {"full_name": "Updated Name"}
        )


@pytest.mark.asyncio
async def test_unauthenticated_access_working():
    """Test that unauthenticated requests are properly rejected."""
    # Don't override get_current_user - it should fail
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/api/v1/users/profile")
        
    assert response.status_code == 403


def test_user_model_validation():
    """Test UserProfile model creation and validation."""
    user = create_mock_user("_validation")
    
    assert user.full_name == "Test User"
    assert user.role == "agent"
    assert user.daily_request_count == 5
    assert "@example.com" in user.email
    assert isinstance(user.id, uuid.UUID)


def test_unique_email_generation():
    """Test that create_mock_user generates unique emails."""
    user1 = create_mock_user("_unique1")
    user2 = create_mock_user("_unique2")
    user3 = create_mock_user("_unique1")  # Same suffix, different UUID
    
    assert user1.email != user2.email
    assert user1.email != user3.email
    assert user2.email != user3.email
    assert user1.id != user2.id != user3.id