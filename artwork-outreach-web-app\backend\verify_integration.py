#!/usr/bin/env python3
"""
Integration verification script to test the complete Twitch scraper → web app pipeline.
This script validates that real data flows correctly from scraper to database to API.
"""

import asyncio
import requests
import json
from pathlib import Path
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
from app.database.models import Base, Streamer
from app.config import get_settings

async def test_database_connection():
    """Test that we can connect to the database and see tables."""
    settings = get_settings()
    engine = create_async_engine(settings.DATABASE_URL)
    AsyncSessionLocal = async_sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    try:
        async with engine.begin() as conn:
            # Ensure tables exist
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSessionLocal() as session:
            from sqlalchemy import text
            
            # Count streamers
            result = await session.execute(text("SELECT COUNT(*) FROM streamers"))
            total_streamers = result.scalar()
            
            # Count live streamers
            result = await session.execute(text("SELECT COUNT(*) FROM streamers WHERE is_live = 1"))
            live_streamers = result.scalar()
            
            print(f"✅ Database connection successful")
            print(f"   Total streamers in database: {total_streamers}")
            print(f"   Live streamers: {live_streamers}")
            
            return total_streamers > 0
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_backend_api():
    """Test that backend API endpoints are responding."""
    base_url = "http://localhost:8000/api/v1"
    
    try:
        # Test health endpoint
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
        
        print(f"✅ Backend health check passed")
        
        # Test streamers stats endpoint
        response = requests.get(f"{base_url}/streamers/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Streamers stats API working:")
            print(f"   Total: {stats.get('total_streamers', 0)}")
            print(f"   Live: {stats.get('live_streamers', 0)}")
            print(f"   Available: {stats.get('available_streamers', 0)}")
        else:
            print(f"⚠️  Stats endpoint returned {response.status_code} (may need auth)")
        
        # Test available streamers endpoint
        response = requests.get(f"{base_url}/streamers/available", timeout=10)
        if response.status_code == 200:
            streamers = response.json()
            print(f"✅ Available streamers API working: {len(streamers)} streamers")
            if streamers:
                print(f"   Sample streamer: {streamers[0].get('username', 'N/A')}")
        else:
            print(f"⚠️  Available streamers endpoint returned {response.status_code} (may need auth)")
        
        return True
        
    except requests.RequestException as e:
        print(f"❌ Backend API test failed: {e}")
        print("   Make sure backend is running on http://localhost:8000")
        return False

def test_frontend_api():
    """Test that frontend API routes are working."""
    base_url = "http://localhost:3000/api"
    
    try:
        # Test frontend stats route
        response = requests.get(f"{base_url}/streamers/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Frontend stats API working:")
            print(f"   Total: {stats.get('total_streamers', 0)}")
            print(f"   Live: {stats.get('live_streamers', 0)}")
            print(f"   Available: {stats.get('available_streamers', 0)}")
        else:
            print(f"❌ Frontend stats API failed: {response.status_code}")
            return False
        
        # Test frontend available streamers route
        response = requests.get(f"{base_url}/streamers/available", timeout=10)
        if response.status_code == 200:
            streamers = response.json()
            print(f"✅ Frontend available streamers API working: {len(streamers)} streamers")
            if streamers:
                sample = streamers[0]
                print(f"   Sample: {sample.get('username', 'N/A')} ({sample.get('follower_count', 0)} followers)")
        else:
            print(f"❌ Frontend available streamers API failed: {response.status_code}")
            return False
        
        return True
        
    except requests.RequestException as e:
        print(f"❌ Frontend API test failed: {e}")
        print("   Make sure frontend is running on http://localhost:3000")
        return False

def check_scraper_output():
    """Check if scraper has produced output files."""
    csv_files = [
        Path("../twitch_streams_filtered.csv"),
        Path("twitch_streams_filtered.csv")
    ]
    
    for csv_file in csv_files:
        if csv_file.exists():
            import pandas as pd
            try:
                df = pd.read_csv(csv_file)
                print(f"✅ Scraper output found: {csv_file}")
                print(f"   {len(df)} streamers in CSV")
                print(f"   Columns: {list(df.columns)}")
                return True
            except Exception as e:
                print(f"⚠️  CSV file exists but can't read it: {e}")
    
    print(f"⚠️  No scraper output found. Run the scraper first:")
    print(f"   cd /mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main")
    print(f"   python3 twitch_scraper.py")
    return False

async def main():
    """Run all integration tests."""
    print("🔍 Verifying Twitch Scraper → Web App Integration")
    print("=" * 60)
    
    # Test 1: Check scraper output
    print("\n1. Checking Scraper Output:")
    scraper_ok = check_scraper_output()
    
    # Test 2: Database connection
    print("\n2. Testing Database Connection:")
    db_ok = await test_database_connection()
    
    # Test 3: Backend API
    print("\n3. Testing Backend API:")
    backend_ok = test_backend_api()
    
    # Test 4: Frontend API
    print("\n4. Testing Frontend API:")
    frontend_ok = test_frontend_api()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 Integration Test Summary:")
    
    tests = [
        ("Scraper Output", scraper_ok),
        ("Database Connection", db_ok),
        ("Backend API", backend_ok),
        ("Frontend API", frontend_ok)
    ]
    
    passed = sum(1 for _, ok in tests if ok)
    total = len(tests)
    
    for name, ok in tests:
        status = "✅ PASS" if ok else "❌ FAIL"
        print(f"   {status} {name}")
    
    if passed == total:
        print(f"\n🎉 All tests passed! Integration is working correctly.")
        print(f"   Your web app is now serving real Twitch data!")
    else:
        print(f"\n⚠️  {passed}/{total} tests passed. Check the failures above.")
        if not db_ok:
            print(f"   💡 Try running: python3 load_existing_data.py")
        if not backend_ok:
            print(f"   💡 Try running: uvicorn app.main:app --reload --port 8000")
        if not frontend_ok:
            print(f"   💡 Try running: npm run dev")

if __name__ == "__main__":
    asyncio.run(main())