"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\nclass ApiClient {\n    async makeRequest(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        // Get auth token from Clerk\n        const token = await this.getAuthToken();\n        const defaultHeaders = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            defaultHeaders[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const config = {\n            ...options,\n            headers: {\n                ...defaultHeaders,\n                ...options.headers\n            }\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        detail: \"Unknown error\"\n                    }));\n                throw new Error(errorData.detail || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            throw error;\n        }\n    }\n    async getAuthToken() {\n        // This will be implemented with Clerk integration\n        // For now, return null (unauthenticated requests)\n        return null;\n    }\n    // Streamer API methods\n    async getAvailableStreamers() {\n        const backendStreamers = await this.makeRequest(\"/fallback/available\");\n        // Transform backend data to frontend format\n        const streamers = backendStreamers.map((streamer)=>({\n                twitch_user_id: streamer.twitch_user_id,\n                username: streamer.username,\n                display_name: streamer.display_name,\n                follower_count: streamer.follower_count,\n                is_live: false,\n                current_game: streamer.current_game,\n                stream_title: streamer.stream_title,\n                profile_image_url: streamer.thumbnail_url || \"https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png\",\n                language: streamer.language,\n                last_seen_live_at: streamer.last_seen_live_at,\n                twitch_url: \"https://twitch.tv/\".concat(streamer.username)\n            }));\n        return {\n            streamers,\n            total: streamers.length,\n            user_status: {\n                daily_requests_used: 1,\n                daily_requests_remaining: 2,\n                can_make_request: true\n            }\n        };\n    }\n    async getStreamerStats() {\n        const backendStats = await this.makeRequest(\"/fallback/stats\");\n        return {\n            total_streamers: backendStats.total_streamers,\n            live_streamers: backendStats.live_streamers,\n            available_streamers: backendStats.available_streamers,\n            last_updated: new Date().toISOString()\n        };\n    }\n    async triggerScrape() {\n        return this.makeRequest(\"/api/v1/streamers/trigger-scrape\", {\n            method: \"POST\"\n        });\n    }\n    async getScraperStatus() {\n        return this.makeRequest(\"/api/v1/streamers/scraper-status\");\n    }\n    // Assignment API methods\n    async getUserAssignments() {\n        return this.makeRequest(\"/api/v1/assignments/\");\n    }\n    async updateAssignmentStatus(assignmentId, status, notes) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId, \"/status\"), {\n            method: \"PATCH\",\n            body: JSON.stringify({\n                status,\n                notes\n            })\n        });\n    }\n    async deleteAssignment(assignmentId) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId), {\n            method: \"DELETE\"\n        });\n    }\n    // Health check\n    async healthCheck() {\n        return this.makeRequest(\"/api/v1/health\");\n    }\n    constructor(){\n        this.baseURL = API_BASE_URL;\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});