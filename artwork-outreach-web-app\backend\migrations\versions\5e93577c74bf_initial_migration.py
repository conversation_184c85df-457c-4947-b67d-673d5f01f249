"""Initial migration

Revision ID: 5e93577c74bf
Revises: 
Create Date: 2025-06-30 05:57:11.286822

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5e93577c74bf'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    is_sqlite = bind.engine.name == "sqlite"

    uuid_type = sa.String(36) if is_sqlite else sa.UUID()

    op.create_table('scraper_runs',
    sa.Column('id', uuid_type, nullable=False),
    sa.Column('run_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('streamers_found', sa.Integer(), nullable=True),
    sa.Column('details', sa.Text(), nullable=True),
    sa.CheckConstraint('streamers_found >= 0', name='cc_scraper_run_streamers_found_non_negative'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('streamers',
    sa.Column('id', uuid_type, nullable=False),
    sa.Column('twitch_user_id', sa.String(), nullable=False),
    sa.Column('username', sa.String(), nullable=False),
    sa.Column('display_name', sa.String(), nullable=True),
    sa.Column('follower_count', sa.Integer(), nullable=True),
    sa.Column('is_live', sa.Boolean(), nullable=True),
    sa.Column('current_game', sa.String(), nullable=True),
    sa.Column('stream_title', sa.String(), nullable=True),
    sa.Column('thumbnail_url', sa.String(), nullable=True),
    sa.Column('profile_image_url', sa.String(), nullable=True),
    sa.Column('language', sa.String(), nullable=True),
    sa.Column('last_seen_live_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.CheckConstraint('follower_count >= 0', name='cc_streamer_follower_count_non_negative'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_streamers_created_at'), 'streamers', ['created_at'], unique=False)
    op.create_index(op.f('ix_streamers_follower_count'), 'streamers', ['follower_count'], unique=False)
    op.create_index(op.f('ix_streamers_is_live'), 'streamers', ['is_live'], unique=False)
    op.create_index(op.f('ix_streamers_language'), 'streamers', ['language'], unique=False)
    op.create_index(op.f('ix_streamers_last_seen_live_at'), 'streamers', ['last_seen_live_at'], unique=False)
    op.create_index(op.f('ix_streamers_twitch_user_id'), 'streamers', ['twitch_user_id'], unique=True)
    op.create_index(op.f('ix_streamers_username'), 'streamers', ['username'], unique=False)
    op.create_table('users',
    sa.Column('id', uuid_type, nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('full_name', sa.String(), nullable=True),
    sa.Column('daily_request_count', sa.Integer(), nullable=True),
    sa.Column('last_request_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('role', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.CheckConstraint('daily_request_count >= 0', name='cc_user_daily_request_count_non_negative'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_last_request_date'), 'users', ['last_request_date'], unique=False)
    op.create_table('assignments',
    sa.Column('id', uuid_type, nullable=False),
    sa.Column('user_id', uuid_type, nullable=True),
    sa.Column('streamer_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('assigned_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['streamer_id'], ['streamers.twitch_user_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'streamer_id', name='uq_user_streamer')
    )
    op.create_index(op.f('ix_assignments_assigned_at'), 'assignments', ['assigned_at'], unique=False)
    op.create_index(op.f('ix_assignments_deleted_at'), 'assignments', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_assignments_status'), 'assignments', ['status'], unique=False)
    op.create_index(op.f('ix_assignments_streamer_id'), 'assignments', ['streamer_id'], unique=False)
    op.create_index(op.f('ix_assignments_user_id'), 'assignments', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_assignments_user_id'), table_name='assignments')
    op.drop_index(op.f('ix_assignments_streamer_id'), table_name='assignments')
    op.drop_index(op.f('ix_assignments_status'), table_name='assignments')
    op.drop_index(op.f('ix_assignments_deleted_at'), table_name='assignments')
    op.drop_index(op.f('ix_assignments_assigned_at'), table_name='assignments')
    op.drop_table('assignments')
    op.drop_index(op.f('ix_users_last_request_date'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_streamers_username'), table_name='streamers')
    op.drop_index(op.f('ix_streamers_twitch_user_id'), table_name='streamers')
    op.drop_index(op.f('ix_streamers_last_seen_live_at'), table_name='streamers')
    op.drop_index(op.f('ix_streamers_language'), table_name='streamers')
    op.drop_index(op.f('ix_streamers_is_live'), table_name='streamers')
    op.drop_index(op.f('ix_streamers_follower_count'), table_name='streamers')
    op.drop_index(op.f('ix_streamers_created_at'), table_name='streamers')
    op.drop_table('streamers')
    op.drop_table('scraper_runs')
    # ### end Alembic commands ###
