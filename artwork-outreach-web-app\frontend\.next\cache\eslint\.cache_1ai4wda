[{"C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\api\\streamers\\available\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\api\\streamers\\stats\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\auth\\sign-in\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\auth\\sign-out\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\auth\\sign-up\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\dashboard\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\dashboard\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\not-found.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\user-profile\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\auth\\SignInButton.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\auth\\UserButton.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\auth\\UserProfile.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\AssignmentAnalytics.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\AssignmentList.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\Dashboard.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\StreamerCard.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\StreamerDetailModal.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\StreamerFilter.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\StreamerList.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\UserStatus.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Footer.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Grid.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Header.tsx": "26", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Layout.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Navigation.tsx": "28", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Sidebar.tsx": "29", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\SWRProvider.tsx": "30", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Badge.tsx": "31", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Button.tsx": "32", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Card.tsx": "33", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Dialog.tsx": "34", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\EmptyState.tsx": "35", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\ErrorState.tsx": "36", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Input.tsx": "37", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Label.tsx": "38", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\LoadingSpinner.tsx": "39", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Skeleton.tsx": "40", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Slider.tsx": "41", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Table.tsx": "42", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Toast.tsx": "43", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Tooltip.tsx": "44", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\context\\LayoutContext.tsx": "45", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useApi.ts": "46", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useAuth.tsx": "47", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useLocalStorage.tsx": "48", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useStreamers.ts": "49", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useStreamers.tsx": "50", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useUserStatus.tsx": "51", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\lib\\api.ts": "52", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\lib\\auth.ts": "53", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\lib\\services.ts": "54", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\lib\\utils.ts": "55", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\middleware.ts": "56", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\api.ts": "57", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\assignment.ts": "58", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\streamer.ts": "59", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\user.ts": "60", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useApiClient.tsx": "61", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\clerk-mocks.d.ts": "62", "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\clerk.d.ts": "63"}, {"size": 1399, "mtime": 1751317613340, "results": "64", "hashOfConfig": "65"}, {"size": 867, "mtime": 1751317622309, "results": "66", "hashOfConfig": "65"}, {"size": 201, "mtime": 1751312176156, "results": "67", "hashOfConfig": "65"}, {"size": 252, "mtime": 1751332280209, "results": "68", "hashOfConfig": "65"}, {"size": 201, "mtime": 1751312177074, "results": "69", "hashOfConfig": "65"}, {"size": 220, "mtime": 1751290926168, "results": "70", "hashOfConfig": "65"}, {"size": 8996, "mtime": 1751329288204, "results": "71", "hashOfConfig": "65"}, {"size": 923, "mtime": 1751314078341, "results": "72", "hashOfConfig": "65"}, {"size": 341, "mtime": 1751311484440, "results": "73", "hashOfConfig": "65"}, {"size": 2484, "mtime": 1751312204438, "results": "74", "hashOfConfig": "65"}, {"size": 252, "mtime": 1751312180640, "results": "75", "hashOfConfig": "65"}, {"size": 666, "mtime": 1751366900299, "results": "76", "hashOfConfig": "65"}, {"size": 0, "mtime": 1751237862582, "results": "77", "hashOfConfig": "65"}, {"size": 253, "mtime": 1751312178511, "results": "78", "hashOfConfig": "65"}, {"size": 0, "mtime": 1751237862590, "results": "79", "hashOfConfig": "65"}, {"size": 1566, "mtime": 1751315184769, "results": "80", "hashOfConfig": "65"}, {"size": 8152, "mtime": 1751332448320, "results": "81", "hashOfConfig": "65"}, {"size": 3613, "mtime": 1751324952366, "results": "82", "hashOfConfig": "65"}, {"size": 5367, "mtime": 1751333473094, "results": "83", "hashOfConfig": "65"}, {"size": 3862, "mtime": 1751332776939, "results": "84", "hashOfConfig": "65"}, {"size": 2376, "mtime": 1751314829755, "results": "85", "hashOfConfig": "65"}, {"size": 1197, "mtime": 1751366905021, "results": "86", "hashOfConfig": "65"}, {"size": 1377, "mtime": 1751314197851, "results": "87", "hashOfConfig": "65"}, {"size": 3297, "mtime": 1751327656655, "results": "88", "hashOfConfig": "65"}, {"size": 666, "mtime": 1751291780934, "results": "89", "hashOfConfig": "65"}, {"size": 3135, "mtime": 1751327888037, "results": "90", "hashOfConfig": "65"}, {"size": 975, "mtime": 1751327846707, "results": "91", "hashOfConfig": "65"}, {"size": 1837, "mtime": 1751312232294, "results": "92", "hashOfConfig": "65"}, {"size": 1701, "mtime": 1751327023740, "results": "93", "hashOfConfig": "65"}, {"size": 503, "mtime": 1751314062645, "results": "94", "hashOfConfig": "65"}, {"size": 1115, "mtime": 1751314435074, "results": "95", "hashOfConfig": "65"}, {"size": 2241, "mtime": 1751325396529, "results": "96", "hashOfConfig": "65"}, {"size": 1989, "mtime": 1751291208742, "results": "97", "hashOfConfig": "65"}, {"size": 3970, "mtime": 1751314650485, "results": "98", "hashOfConfig": "65"}, {"size": 803, "mtime": 1751291235853, "results": "99", "hashOfConfig": "65"}, {"size": 630, "mtime": 1751291246851, "results": "100", "hashOfConfig": "65"}, {"size": 850, "mtime": 1751314763242, "results": "101", "hashOfConfig": "65"}, {"size": 757, "mtime": 1751314773895, "results": "102", "hashOfConfig": "65"}, {"size": 1234, "mtime": 1751325669018, "results": "103", "hashOfConfig": "65"}, {"size": 277, "mtime": 1751291222894, "results": "104", "hashOfConfig": "65"}, {"size": 1124, "mtime": 1751314786306, "results": "105", "hashOfConfig": "65"}, {"size": 2899, "mtime": 1751291191811, "results": "106", "hashOfConfig": "65"}, {"size": 852, "mtime": 1751291260649, "results": "107", "hashOfConfig": "65"}, {"size": 1197, "mtime": 1751314448758, "results": "108", "hashOfConfig": "65"}, {"size": 855, "mtime": 1751298896606, "results": "109", "hashOfConfig": "65"}, {"size": 124, "mtime": 1751367198125, "results": "110", "hashOfConfig": "65"}, {"size": 408, "mtime": 1751312203945, "results": "111", "hashOfConfig": "65"}, {"size": 0, "mtime": 1751237862664, "results": "112", "hashOfConfig": "65"}, {"size": 618, "mtime": 1751314896059, "results": "113", "hashOfConfig": "65"}, {"size": 3334, "mtime": 1751362981220, "results": "114", "hashOfConfig": "65"}, {"size": 596, "mtime": 1751363196895, "results": "115", "hashOfConfig": "65"}, {"size": 3196, "mtime": 1751363266108, "results": "116", "hashOfConfig": "65"}, {"size": 0, "mtime": 1751237862689, "results": "117", "hashOfConfig": "65"}, {"size": 3068, "mtime": 1751314039450, "results": "118", "hashOfConfig": "65"}, {"size": 168, "mtime": 1751292800652, "results": "119", "hashOfConfig": "65"}, {"size": 267, "mtime": 1751312280410, "results": "120", "hashOfConfig": "65"}, {"size": 614, "mtime": 1751366989440, "results": "121", "hashOfConfig": "65"}, {"size": 784, "mtime": 1751363172403, "results": "122", "hashOfConfig": "65"}, {"size": 932, "mtime": 1751332613840, "results": "123", "hashOfConfig": "65"}, {"size": 628, "mtime": 1751371354288, "results": "124", "hashOfConfig": "65"}, {"size": 2001, "mtime": 1751367170875, "results": "125", "hashOfConfig": "65"}, {"size": 568, "mtime": 1751367266545, "results": "126", "hashOfConfig": "65"}, {"size": 203, "mtime": 1751366939199, "results": "127", "hashOfConfig": "65"}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wl5vfz", {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\api\\streamers\\available\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\api\\streamers\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\auth\\sign-in\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\auth\\sign-out\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\auth\\sign-up\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\app\\user-profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\auth\\SignInButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\auth\\UserButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\auth\\UserProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\AssignmentAnalytics.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\AssignmentList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\StreamerCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\StreamerDetailModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\StreamerFilter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\StreamerList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\dashboard\\UserStatus.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\SWRProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\EmptyState.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\ErrorState.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\components\\ui\\Tooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\context\\LayoutContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useApi.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useAuth.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useLocalStorage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useStreamers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useStreamers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useUserStatus.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\lib\\services.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\assignment.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\streamer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\user.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\hooks\\useApiClient.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\clerk-mocks.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Twitch-Scraper-main\\artwork-outreach-web-app\\frontend\\src\\types\\clerk.d.ts", [], []]