import re
from typing import Any
import validators

def validate_twitch_user_id(user_id: str) -> bool:
    """
    Validates that the Twitch user ID is a non-empty string of digits.
    """
    return isinstance(user_id, str) and user_id.isdigit()

def validate_follower_count(count: int) -> bool:
    """
    Validates that the follower count is a non-negative integer.
    """
    return isinstance(count, int) and count >= 0

def validate_url(url: str) -> bool:
    """
    Validates that a given string is a valid URL.
    """
    return isinstance(url, str) and validators.url(url)

def validate_language_code(code: str) -> bool:
    """
    Validates that a language code is a two-letter ISO 639-1 code.
    """
    return isinstance(code, str) and re.fullmatch(r'[a-z]{2}', code) is not None