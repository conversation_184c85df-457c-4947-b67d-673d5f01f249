from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.database import models
from app.schemas import assignment as assignment_schema
from uuid import UUID
from sqlalchemy.orm import selectinload

async def create_assignment(db: AsyncSession, streamer_twitch_id: str, agent_id: UUID):
    db_assignment = models.Assignment(
        streamer_id=streamer_twitch_id,
        agent_id=agent_id,
        status=assignment_schema.AssignmentStatus.PENDING,
    )
    db.add(db_assignment)
    await db.commit()
    await db.refresh(db_assignment, ["streamer"])
    return db_assignment

async def get_assignments(db: AsyncSession, skip: int = 0, limit: int = 100):
    result = await db.execute(
        select(models.Assignment)
        .options(selectinload(models.Assignment.streamer))
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def update_assignment(db: AsyncSession, assignment_id: UUID, assignment: assignment_schema.UpdateAssignmentRequest):
    result = await db.execute(
        select(models.Assignment)
        .filter(models.Assignment.id == assignment_id)
        .options(selectinload(models.Assignment.streamer))
    )
    db_assignment = result.scalar_one_or_none()
    if db_assignment:
        update_data = assignment.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_assignment, key, value)
        await db.commit()
        await db.refresh(db_assignment)
    return db_assignment

async def delete_assignment(db: AsyncSession, assignment_id: UUID):
    result = await db.execute(select(models.Assignment).filter(models.Assignment.id == assignment_id))
    db_assignment = result.scalar_one_or_none()
    if db_assignment:
        await db.delete(db_assignment)
        await db.commit()
    return db_assignment