"""Simplified streamer schemas for immediate use"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel

class StreamerResponse(BaseModel):
    twitch_user_id: str
    username: str
    display_name: Optional[str] = None
    follower_count: int
    is_live: bool = False
    current_game: Optional[str] = None
    stream_title: Optional[str] = None
    profile_image_url: Optional[str] = None
    language: str = "en"
    last_seen_live_at: Optional[datetime] = None
    twitch_url: Optional[str] = None

class StreamerListResponse(BaseModel):
    streamers: List[StreamerResponse]
    total: int
    user_status: Dict[str, Any]

class AssignmentResponse(BaseModel):
    id: str
    streamer_id: str
    streamer_username: str
    streamer_display_name: str
    status: str
    assigned_at: datetime
    updated_at: datetime
    notes: Optional[str] = None

class AssignmentUpdateRequest(BaseModel):
    status: str
    notes: Optional[str] = None