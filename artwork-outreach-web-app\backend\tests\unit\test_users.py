import pytest
from httpx import AsyncClient
import uuid
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.models import User<PERSON>rofile
from app.api.dependencies import get_current_user
from app.main import app

def create_mock_user(email_suffix: str = ""):
    return UserProfile(
        id=uuid.uuid4(),
        full_name="Test User",
        email=f"test{email_suffix}@example.com",
        role="agent",
        daily_request_count=5,
    )

@pytest.mark.asyncio
async def test_get_user_profile(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test retrieving the user profile for an authenticated user.
    """
    mock_user = create_mock_user("_profile")
    db_session.add(mock_user)
    await db_session.commit()

    app.dependency_overrides[get_current_user] = lambda: mock_user
    response = await async_client.get("/api/v1/users/profile")
    assert response.status_code == 200
    json_response = response.json()
    assert json_response["id"] == str(mock_user.id)
    assert json_response["full_name"] == mock_user.full_name
    assert json_response["email"] == mock_user.email
    assert json_response["role"] == mock_user.role

@pytest.mark.asyncio
async def test_get_user_status(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test retrieving the user status for an authenticated user.
    """
    mock_user = create_mock_user("_status")
    db_session.add(mock_user)
    await db_session.commit()

    app.dependency_overrides[get_current_user] = lambda: mock_user
    response = await async_client.get("/api/v1/users/status")
    assert response.status_code == 200
    json_response = response.json()
    assert json_response["daily_request_count"] == mock_user.daily_request_count
    assert "remaining_requests" in json_response
    assert "next_reset_time" in json_response

@pytest.mark.asyncio
async def test_get_user_profile_unauthenticated(async_client: AsyncClient):
    """
    Test that an unauthenticated user cannot access the profile.
    """
    app.dependency_overrides = {}
    response = await async_client.get("/api/v1/users/profile")
    assert response.status_code == 403

@pytest.mark.asyncio
async def test_update_user_profile(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test updating the user profile.
    """
    mock_user = create_mock_user("_update")
    db_session.add(mock_user)
    await db_session.commit()
    await db_session.refresh(mock_user)

    app.dependency_overrides[get_current_user] = lambda: mock_user
    
    update_data = {"full_name": "Updated Name"}
    response = await async_client.patch("/api/v1/users/profile", json=update_data)
    
    assert response.status_code == 200
    json_response = response.json()
    assert json_response["full_name"] == "Updated Name"

    # Verify the change in the database
    updated_user = await db_session.get(UserProfile, mock_user.id)
    assert updated_user.full_name == "Updated Name"
