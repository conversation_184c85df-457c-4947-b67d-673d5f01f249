import logging
from datetime import date
import uuid
from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.core.exceptions import RateLimitExceededException
from app.database.models import UserProfile


class RequestLimitService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def check_daily_limit(self, user_id: int) -> int:
        """
        Checks the user's daily request limit and returns the number of remaining requests.
        """
        today = date.today()

        # Get from DB directly (no cache for now)
        user_profile = await self.db.get(UserProfile, user_id)
        if not user_profile:
            return 0

        if user_profile.last_request_date != today:
            user_profile.daily_request_count = 0
            user_profile.daily_streamers_received = 0  # Reset streamer count too
            user_profile.last_request_date = today
            await self.db.commit()
            await self.db.refresh(user_profile)

        request_count = user_profile.daily_request_count

        daily_limit = 3
        remaining_requests = daily_limit - request_count
        return max(0, remaining_requests)

    async def check_daily_streamer_quota(self, user_id: int) -> dict:
        """
        Checks the user's daily streamer quota and returns quota information.
        Returns dict with remaining_streamers, daily_limit, streamers_received
        """
        today = date.today()

        user_profile = await self.db.get(UserProfile, user_id)
        if not user_profile:
            return {"remaining_streamers": 0, "daily_limit": 200, "streamers_received": 0}

        # Reset counters if it's a new day
        if user_profile.last_request_date != today:
            user_profile.daily_request_count = 0
            user_profile.daily_streamers_received = 0
            user_profile.last_request_date = today
            await self.db.commit()
            await self.db.refresh(user_profile)

        daily_limit = user_profile.daily_streamer_limit or 200
        streamers_received = user_profile.daily_streamers_received or 0
        remaining_streamers = max(0, daily_limit - streamers_received)

        return {
            "remaining_streamers": remaining_streamers,
            "daily_limit": daily_limit,
            "streamers_received": streamers_received
        }

    async def increment_request_count(self, user_id: int) -> None:
        """
        Increments the user's daily request count in the database.
        """
        today = date.today()
        # Update the database
        result = await self.db.execute(
            select(UserProfile).filter_by(id=user_id).with_for_update()
        )
        user_profile = result.scalar_one_or_none()
        
        if not user_profile:
            logging.warning(f"User profile with id {user_id} not found.")
            return

        if user_profile.last_request_date != today:
            user_profile.daily_request_count = 1
            user_profile.last_request_date = today
        else:
            user_profile.daily_request_count += 1
        
        await self.db.commit()
        logging.info(f"Incremented request count for user {user_id}. New count: {user_profile.daily_request_count}")

    async def increment_streamer_count(self, user_id: int, streamer_count: int) -> None:
        """
        Increments the user's daily streamer count in the database.
        """
        today = date.today()

        result = await self.db.execute(
            select(UserProfile).filter_by(id=user_id).with_for_update()
        )
        user_profile = result.scalar_one_or_none()

        if not user_profile:
            logging.warning(f"User profile with id {user_id} not found.")
            return

        # Reset if new day
        if user_profile.last_request_date != today:
            user_profile.daily_streamers_received = streamer_count
            user_profile.last_request_date = today
        else:
            user_profile.daily_streamers_received = (user_profile.daily_streamers_received or 0) + streamer_count

        await self.db.commit()
        logging.info(f"Incremented streamer count for user {user_id} by {streamer_count}. New total: {user_profile.daily_streamers_received}")

    async def reset_daily_counts(self) -> None:
        """
        Resets the daily_request_count for all users to 0.
        """
        users = await self.db.execute(select(UserProfile))
        for user in users.scalars().all():
            user.daily_request_count = 0
        await self.db.commit()
        logging.info("Daily request counts have been reset for all users.")


async def get_or_create_user_profile(
    db_session: AsyncSession, claims: Dict[str, Any]
) -> UserProfile:
    """
    Retrieves a user profile from the database or creates a new one if it doesn't exist.
    """
    user_id_str = claims.get("user_id")  # Get user_id from token claims
    if not user_id_str:
        # For development, create a default user
        user_id_str = "dev_user_123"
    
    # Try to parse as UUID, fallback to generating one
    try:
        user_id = uuid.UUID(user_id_str)
    except ValueError:
        user_id = uuid.uuid4()

    result = await db_session.execute(
        select(UserProfile).filter(UserProfile.id == user_id)
    )
    user_profile = result.scalar_one_or_none()

    if not user_profile:
        email = claims.get("email", f"{user_id}@placeholder.email")
        full_name = claims.get("full_name", "Development User")
        user_profile = UserProfile(
            id=user_id,
            email=email,
            full_name=full_name,
        )
        db_session.add(user_profile)
        await db_session.commit()
        await db_session.refresh(user_profile)

    return user_profile


async def check_and_increment_request_count(
    user: UserProfile, db_session: AsyncSession
):
    """
    Checks the user's daily request count and increments it.
    Raises RateLimitExceededException if the count exceeds the limit.
    """
    # Lock the user row for the duration of the transaction
    await db_session.refresh(user, attribute_names=['daily_request_count', 'last_request_date'], with_for_update=True)

    today = date.today()
    if user.last_request_date != today:
        user.daily_request_count = 0
        user.last_request_date = today

    if user.daily_request_count >= 3:
        raise RateLimitExceededException()

    user.daily_request_count += 1
    await db_session.commit()


class UserService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def update_user_profile(self, user_id: str, profile_update) -> UserProfile:
        """Update user profile with new data."""
        result = await self.db.execute(
            select(UserProfile).filter(UserProfile.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            raise ValueError("User not found")
        
        update_data = profile_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        await self.db.commit()
        await self.db.refresh(user)
        return user