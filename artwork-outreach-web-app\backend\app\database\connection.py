import logging
import time
from sqlalchemy.ext.asyncio import async_sessionmaker, create_async_engine
from sqlalchemy.exc import OperationalError
from tenacity import retry, stop_after_attempt, wait_fixed, before_sleep_log

from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

@retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(2),
    before_sleep=before_sleep_log(logger, logging.INFO),
)
def _create_engine_with_retry():
    """Creates a new SQLAlchemy engine with retry logic for connection errors."""
    try:
        engine = create_async_engine(
            settings.DATABASE_URL,
            pool_pre_ping=True,
            pool_size=settings.DB_POOL_SIZE,
            max_overflow=settings.DB_MAX_OVERFLOW,
            pool_recycle=settings.DB_POOL_RECYCLE,
        )
        # Note: Connection test will be done during first actual use
        return engine
    except Exception as e:
        logger.error(f"Database engine creation failed: {e}")
        raise

engine = _create_engine_with_retry()

AsyncSessionLocal = async_sessionmaker(
    autocommit=False, autoflush=False, bind=engine
)


async def get_db_session():
    """
    Dependency to get a database session.
    Ensures the session is closed after the request is finished.
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# Alias for compatibility
get_db = get_db_session