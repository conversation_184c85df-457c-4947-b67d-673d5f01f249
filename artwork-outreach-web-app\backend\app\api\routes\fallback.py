"""
Fallback API routes that use Supabase REST API instead of database connection
"""

from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any
from app.services.supabase_api_service import supabase_api

router = APIRouter()

@router.get("/stats")
async def get_streamers_stats_fallback() -> Dict[str, Any]:
    """
    Get streamer statistics using Supabase REST API (fallback for database issues)
    """
    try:
        stats = supabase_api.get_streamers_stats()
        return {
            "total_streamers": stats["total_streamers"],
            "live_streamers": stats["live_streamers"], 
            "available_streamers": stats["available_streamers"],
            "average_followers": stats["average_followers"],
            "status": "Using Supabase REST API (fallback mode)"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving stats via fallback: {str(e)}"
        )

@router.get("/available")
async def get_available_streamers_fallback(limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get available streamers using Supabase REST API (fallback for database issues)
    """
    try:
        streamers = supabase_api.get_available_streamers(limit=limit)
        return streamers
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving streamers via fallback: {str(e)}"
        )

@router.get("/test-connection")
async def test_supabase_connection() -> Dict[str, Any]:
    """
    Test the Supabase REST API connection
    """
    try:
        stats = supabase_api.get_streamers_stats()
        if stats["total_streamers"] > 0:
            return {
                "status": "success",
                "message": "Supabase REST API connection working",
                "streamers_found": stats["total_streamers"]
            }
        else:
            return {
                "status": "warning", 
                "message": "Connection works but no streamers found",
                "streamers_found": 0
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Connection test failed: {str(e)}"
        )