"""
Fallback API routes that use Supabase REST API instead of database connection
"""

from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any
from app.services.supabase_api_service import supabase_api
from app.scraper.twitch_client import TwitchClient
from app.core.rate_limiter import RateLimiter
from app.config import get_settings
import asyncio
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/stats")
async def get_streamers_stats_fallback() -> Dict[str, Any]:
    """
    Get streamer statistics using Supabase REST API (fallback for database issues)
    """
    try:
        stats = supabase_api.get_streamers_stats()
        return {
            "total_streamers": stats["total_streamers"],
            "live_streamers": stats["live_streamers"], 
            "available_streamers": stats["available_streamers"],
            "average_followers": stats["average_followers"],
            "status": "Using Supabase REST API (fallback mode)"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving stats via fallback: {str(e)}"
        )

@router.get("/available")
async def get_available_streamers_fallback(limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get available streamers using Supabase REST API (fallback for database issues)
    """
    try:
        streamers = supabase_api.get_available_streamers(limit=limit)
        return streamers
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving streamers via fallback: {str(e)}"
        )

@router.get("/live-verified")
async def get_live_verified_streamers(batch_size: int = 100) -> Dict[str, Any]:
    """
    Get verified live streamers in batches with real-time Twitch API verification.
    Only returns streamers that are confirmed live right now.

    Args:
        batch_size: Number of verified live streamers to return (default: 100, max: 500)
    """
    # Limit batch size to reasonable maximum (500 streamers)
    batch_size = min(batch_size, 500)

    try:
        # Initialize Twitch client with proper dependencies
        settings = get_settings()
        rate_limiter = RateLimiter(requests_per_minute=800)  # 800 requests per minute
        twitch_client = TwitchClient(settings, rate_limiter)

        if not twitch_client.authenticate():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Unable to authenticate with Twitch API"
            )

        # Get potential live streamers from database (get more than batch_size to account for offline ones)
        candidate_streamers = supabase_api.get_available_streamers(limit=batch_size * 3)

        verified_live_streamers = []
        checked_count = 0

        logger.info(f"Checking {len(candidate_streamers)} candidate streamers for live status")

        for streamer in candidate_streamers:
            if len(verified_live_streamers) >= batch_size:
                break

            checked_count += 1
            username = streamer.get('username')

            if not username:
                continue

            # Real-time verification with Twitch API
            stream_info = twitch_client.get_stream_info(username)

            if stream_info:
                # Streamer is confirmed live - update their info with latest data
                streamer['is_live'] = True
                streamer['current_game'] = stream_info.get('game_name')
                streamer['stream_title'] = stream_info.get('title')
                streamer['thumbnail_url'] = stream_info.get('thumbnail_url', '').replace('{width}', '320').replace('{height}', '180')
                streamer['viewer_count'] = stream_info.get('viewer_count', 0)

                verified_live_streamers.append(streamer)
                logger.info(f"✅ {username} is live - {stream_info.get('game_name')} - {stream_info.get('viewer_count', 0)} viewers")
            else:
                logger.info(f"❌ {username} is offline")

        return {
            "streamers": verified_live_streamers,
            "batch_size": len(verified_live_streamers),
            "checked_count": checked_count,
            "verification_method": "Real-time Twitch API",
            "status": f"Found {len(verified_live_streamers)} verified live streamers"
        }

    except Exception as e:
        logger.error(f"Error in live verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error verifying live streamers: {str(e)}"
        )

@router.get("/test-connection")
async def test_supabase_connection() -> Dict[str, Any]:
    """
    Test the Supabase REST API connection
    """
    try:
        stats = supabase_api.get_streamers_stats()
        if stats["total_streamers"] > 0:
            return {
                "status": "success",
                "message": "Supabase REST API connection working",
                "streamers_found": stats["total_streamers"]
            }
        else:
            return {
                "status": "warning",
                "message": "Connection works but no streamers found",
                "streamers_found": 0
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Connection test failed: {str(e)}"
        )