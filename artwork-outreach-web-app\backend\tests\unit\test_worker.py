import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from app.main import app
from app.schemas.worker import Worker, WorkerCreate

client = TestClient(app)

@pytest.fixture
def mock_worker_service():
    with patch('app.api.routes.worker.worker_service') as mock_service:
        yield mock_service

def test_create_worker(mock_worker_service):
    """
    Test creating a new worker.
    """
    worker_data = {"username": "testworker", "is_active": True}
    mock_worker_service.create_worker.return_value = Worker(id=1, **worker_data)
    
    response = client.post("/api/v1/workers/", json=worker_data)
    
    assert response.status_code == 200
    assert response.json()["username"] == "testworker"
    mock_worker_service.create_worker.assert_called_once()

def test_get_workers(mock_worker_service):
    """
    Test retrieving all workers.
    """
    workers_data = [
        Worker(id=1, username="worker1", is_active=True),
        Worker(id=2, username="worker2", is_active=False)
    ]
    mock_worker_service.get_workers.return_value = workers_data
    
    response = client.get("/api/v1/workers/")
    
    assert response.status_code == 200
    assert len(response.json()) == 2
    mock_worker_service.get_workers.assert_called_once()

def test_get_worker(mock_worker_service):
    """
    Test retrieving a single worker by ID.
    """
    worker_data = Worker(id=1, username="testworker", is_active=True)
    mock_worker_service.get_worker.return_value = worker_data
    
    response = client.get("/api/v1/workers/1")
    
    assert response.status_code == 200
    assert response.json()["id"] == 1
    mock_worker_service.get_worker.assert_called_with(worker_id=1)

def test_update_worker(mock_worker_service):
    """
    Test updating a worker's information.
    """
    updated_data = {"username": "updatedworker", "is_active": False}
    mock_worker_service.update_worker.return_value = Worker(id=1, **updated_data)
    
    response = client.put("/api/v1/workers/1", json=updated_data)
    
    assert response.status_code == 200
    assert response.json()["username"] == "updatedworker"
    mock_worker_service.update_worker.assert_called_once()

def test_delete_worker(mock_worker_service):
    """
    Test deleting a worker.
    """
    mock_worker_service.delete_worker.return_value = {"message": "Worker deleted successfully"}
    
    response = client.delete("/api/v1/workers/1")
    
    assert response.status_code == 200
    assert response.json() == {"message": "Worker deleted successfully"}
    mock_worker_service.delete_worker.assert_called_with(worker_id=1)