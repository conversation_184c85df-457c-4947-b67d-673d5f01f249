import requests
from typing import Optional, Dict
from datetime import datetime, timedelta
import logging

from app.core.rate_limiter import RateLimiter
from app.config import Settings

logger = logging.getLogger(__name__)

TWITCH_API_BASE_URL = "https://api.twitch.tv/helix"

class TwitchClient:
    """Handle Twitch API interactions."""

    def __init__(self, settings: Settings, rate_limiter: RateLimiter):
        self.client_id = settings.TWITCH_CLIENT_ID
        self.client_secret = settings.TWITCH_CLIENT_SECRET
        self.access_token = None
        self.headers = {}
        self.rate_limiter = rate_limiter

    def authenticate(self) -> bool:
        """Get OAuth token for Twitch API."""
        if not self.client_id or not self.client_secret:
            logger.error("Twitch API credentials not found in settings.")
            return False

        auth_url = "https://id.twitch.tv/oauth2/token"
        auth_params = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'client_credentials'
        }

        try:
            response = requests.post(auth_url, params=auth_params)
            response.raise_for_status()

            token_data = response.json()
            self.access_token = token_data['access_token']

            self.headers = {
                'Client-ID': self.client_id,
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            logger.info("Successfully authenticated with Twitch API")
            return True

        except requests.RequestException as e:
            logger.error(f"Failed to authenticate with Twitch API: {e}")
            return False

    def get_user_info(self, username: str) -> Optional[Dict]:
        """Get user information including follower count."""
        if not self.access_token:
            return None

        url = f"{TWITCH_API_BASE_URL}/users"
        params = {'login': username}

        try:
            self.rate_limiter.wait_if_needed()
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            data = response.json()
            if data['data']:
                user_data = data['data'][0]

                self.rate_limiter.wait_if_needed()
                followers_url = f"{TWITCH_API_BASE_URL}/channels/followers"
                followers_params = {'broadcaster_id': user_data['id']}

                followers_response = requests.get(followers_url, headers=self.headers, params=followers_params)
                followers_response.raise_for_status()

                followers_data = followers_response.json()
                user_data['follower_count'] = followers_data.get('total', 0)

                return user_data

        except requests.RequestException as e:
            logger.warning(f"Failed to get user info for {username}: {e}")

        return None

    def get_stream_info(self, username: str) -> Optional[Dict]:
        """Get current stream information."""
        if not self.access_token:
            return None

        url = f"{TWITCH_API_BASE_URL}/streams"
        params = {'user_login': username}

        try:
            self.rate_limiter.wait_if_needed()
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            data = response.json()
            if data['data']:
                return data['data'][0]

        except requests.RequestException as e:
            logger.warning(f"Failed to get stream info for {username}: {e}")

        return None
    
    def get_stream_history(self, user_id: str, days_to_check: int) -> int:
        """Get stream count for the past period to check consistency."""
        if not self.access_token:
            return 0
            
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days_to_check)
        
        url = f"{TWITCH_API_BASE_URL}/videos"
        params = {
            'user_id': user_id,
            'type': 'archive',
            'period': 'all',
            'first': 100 
        }
        
        try:
            self.rate_limiter.wait_if_needed()
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            videos = data.get('data', [])
            
            stream_count = 0
            for video in videos:
                created_at_str = video['created_at'].replace('Z', '')
                video_date = datetime.fromisoformat(created_at_str)
                if start_time <= video_date <= end_time:
                    stream_count += 1
            
            return stream_count
            
        except requests.RequestException as e:
            logger.warning(f"Failed to get stream history for user {user_id}: {e}")
            return 0