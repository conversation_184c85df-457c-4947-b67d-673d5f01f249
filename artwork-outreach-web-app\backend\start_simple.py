#!/usr/bin/env python3
"""
Simple backend startup script without dependencies
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Create FastAPI app
app = FastAPI(
    title="Artwork Outreach API",
    description="API for the Artwork Outreach platform.",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple health check endpoint
@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "timestamp": "2025-01-01T00:00:00Z"}

# Mock streamers stats endpoint
@app.get("/api/v1/streamers/stats")
async def get_streamer_stats():
    return {
        "total_streamers": 247,
        "live_streamers": 32,
        "available_streamers": 89,
        "last_updated": "2025-01-01T12:00:00Z"
    }

# Mock available streamers endpoint
@app.get("/api/v1/streamers/available")
async def get_available_streamers():
    # Sample streamers data
    sample_streamers = [
        {
            "twitch_user_id": "123456789",
            "username": "pixel_artist_jane",
            "display_name": "Pixel Artist Jane",
            "follower_count": 42,
            "is_live": True,
            "current_game": "Art",
            "stream_title": "Creating pixel art for indie game",
            "profile_image_url": "https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png",
            "language": "en",
            "last_seen_live_at": "2025-01-01T12:00:00Z",
            "twitch_url": "https://twitch.tv/pixel_artist_jane"
        },
        {
            "twitch_user_id": "987654321",
            "username": "small_rpg_gamer",
            "display_name": "Small RPG Gamer",
            "follower_count": 28,
            "is_live": False,
            "current_game": "Baldur's Gate 3",
            "stream_title": "First time playing this!",
            "profile_image_url": "https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png",
            "language": "en",
            "last_seen_live_at": "2025-01-01T10:30:00Z",
            "twitch_url": "https://twitch.tv/small_rpg_gamer"
        },
        {
            "twitch_user_id": "456789123",
            "username": "indie_dev_alex",
            "display_name": "Indie Dev Alex",
            "follower_count": 15,
            "is_live": True,
            "current_game": "Software and Game Development",
            "stream_title": "Building a 2D platformer",
            "profile_image_url": "https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png",
            "language": "en",
            "last_seen_live_at": "2025-01-01T12:15:00Z",
            "twitch_url": "https://twitch.tv/indie_dev_alex"
        }
    ]
    
    return {
        "streamers": sample_streamers,
        "total": len(sample_streamers),
        "user_status": {
            "daily_requests_used": 1,
            "daily_requests_remaining": 2,
            "can_make_request": True
        }
    }

# Mock trigger scrape endpoint
@app.post("/api/v1/streamers/trigger-scrape")
async def trigger_scrape():
    return {
        "message": "Scraping task triggered (mock)",
        "status": "queued",
        "triggered_by": "<EMAIL>"
    }

if __name__ == "__main__":
    print("🚀 Starting Simple Artwork Outreach Backend...")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔧 Health Check: http://localhost:8000/api/v1/health")
    print("📊 Stats: http://localhost:8000/api/v1/streamers/stats")
    print("📋 Streamers: http://localhost:8000/api/v1/streamers/available")
    
    uvicorn.run(
        "start_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )