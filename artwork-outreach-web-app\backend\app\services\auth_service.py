import jwt
import httpx
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any
from app.config import get_settings, Settings

security = HTTPBearer()

class ClerkAuthService:
    def __init__(self, settings: Settings):
        self.settings = settings
        self.secret_key = settings.CLERK_SECRET_KEY
        
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """
        Verifies a JWT token from <PERSON>.
        For development, we'll create a mock verification.
        """
        try:
            # For development mode, create a mock user
            if self.settings.ENVIRONMENT == "development":
                return {
                    "user_id": "dev_user_123",
                    "email": "<EMAIL>",
                    "full_name": "Development User"
                }
            
            # In production, you would verify with Clerk's JWKS
            # For now, we'll decode without verification for testing
            decoded = jwt.decode(token, options={"verify_signature": False})
            return decoded
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid or expired token: {e}",
                headers={"WWW-Authenticate": "Bearer"},
            )

def get_auth_service(settings: Settings = Depends(get_settings)) -> ClerkAuthService:
    """Returns the authentication service."""
    return ClerkAuthService(settings)

def get_clerk_client(settings: Settings) -> ClerkAuthService:
    """Returns clerk client for backward compatibility."""
    return ClerkAuthService(settings)

async def verify_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: ClerkAuthService = Depends(get_auth_service)
) -> Dict[str, Any]:
    """
    Dependency to verify JWT token and return user claims.
    """
    return await auth_service.verify_token(credentials.credentials)

# Standalone function for middleware usage
async def verify_token_direct(token: str, client: ClerkAuthService) -> Dict[str, Any]:
    """Direct token verification for middleware usage."""
    return await client.verify_token(token)