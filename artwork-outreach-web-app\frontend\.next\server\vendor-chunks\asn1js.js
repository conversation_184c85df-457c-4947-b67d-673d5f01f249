"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/asn1js";
exports.ids = ["vendor-chunks/asn1js"];
exports.modules = {

/***/ "(rsc)/./node_modules/asn1js/build/index.es.js":
/*!***********************************************!*\
  !*** ./node_modules/asn1js/build/index.es.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* binding */ Any),\n/* harmony export */   BaseBlock: () => (/* binding */ BaseBlock),\n/* harmony export */   BaseStringBlock: () => (/* binding */ BaseStringBlock),\n/* harmony export */   BitString: () => (/* binding */ BitString),\n/* harmony export */   BmpString: () => (/* binding */ BmpString),\n/* harmony export */   Boolean: () => (/* binding */ Boolean),\n/* harmony export */   CharacterString: () => (/* binding */ CharacterString),\n/* harmony export */   Choice: () => (/* binding */ Choice),\n/* harmony export */   Constructed: () => (/* binding */ Constructed),\n/* harmony export */   DATE: () => (/* binding */ DATE),\n/* harmony export */   DateTime: () => (/* binding */ DateTime),\n/* harmony export */   Duration: () => (/* binding */ Duration),\n/* harmony export */   EndOfContent: () => (/* binding */ EndOfContent),\n/* harmony export */   Enumerated: () => (/* binding */ Enumerated),\n/* harmony export */   GeneralString: () => (/* binding */ GeneralString),\n/* harmony export */   GeneralizedTime: () => (/* binding */ GeneralizedTime),\n/* harmony export */   GraphicString: () => (/* binding */ GraphicString),\n/* harmony export */   HexBlock: () => (/* binding */ HexBlock),\n/* harmony export */   IA5String: () => (/* binding */ IA5String),\n/* harmony export */   Integer: () => (/* binding */ Integer),\n/* harmony export */   Null: () => (/* binding */ Null),\n/* harmony export */   NumericString: () => (/* binding */ NumericString),\n/* harmony export */   ObjectIdentifier: () => (/* binding */ ObjectIdentifier),\n/* harmony export */   OctetString: () => (/* binding */ OctetString),\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   PrintableString: () => (/* binding */ PrintableString),\n/* harmony export */   RawData: () => (/* binding */ RawData),\n/* harmony export */   RelativeObjectIdentifier: () => (/* binding */ RelativeObjectIdentifier),\n/* harmony export */   Repeated: () => (/* binding */ Repeated),\n/* harmony export */   Sequence: () => (/* binding */ Sequence),\n/* harmony export */   Set: () => (/* binding */ Set),\n/* harmony export */   TIME: () => (/* binding */ TIME),\n/* harmony export */   TeletexString: () => (/* binding */ TeletexString),\n/* harmony export */   TimeOfDay: () => (/* binding */ TimeOfDay),\n/* harmony export */   UTCTime: () => (/* binding */ UTCTime),\n/* harmony export */   UniversalString: () => (/* binding */ UniversalString),\n/* harmony export */   Utf8String: () => (/* binding */ Utf8String),\n/* harmony export */   ValueBlock: () => (/* binding */ ValueBlock),\n/* harmony export */   VideotexString: () => (/* binding */ VideotexString),\n/* harmony export */   ViewWriter: () => (/* binding */ ViewWriter),\n/* harmony export */   VisibleString: () => (/* binding */ VisibleString),\n/* harmony export */   compareSchema: () => (/* binding */ compareSchema),\n/* harmony export */   fromBER: () => (/* binding */ fromBER),\n/* harmony export */   verifySchema: () => (/* binding */ verifySchema)\n/* harmony export */ });\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/./node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var pvutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvutils */ \"(rsc)/./node_modules/pvutils/build/utils.es.js\");\n/*!\n * Copyright (c) 2014, GMO GlobalSign\n * Copyright (c) 2015-2022, Peculiar Ventures\n * All rights reserved.\n * \n * Author 2014-2019, Yury Strozhevsky\n * \n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n * \n * * Redistributions of source code must retain the above copyright notice, this\n *   list of conditions and the following disclaimer.\n * \n * * Redistributions in binary form must reproduce the above copyright notice, this\n *   list of conditions and the following disclaimer in the documentation and/or\n *   other materials provided with the distribution.\n * \n * * Neither the name of the copyright holder nor the names of its\n *   contributors may be used to endorse or promote products derived from\n *   this software without specific prior written permission.\n * \n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR\n * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n * \n */\n\n\n\n\nfunction assertBigInt() {\n    if (typeof BigInt === \"undefined\") {\n        throw new Error(\"BigInt is not defined. Your environment doesn't implement BigInt.\");\n    }\n}\nfunction concat(buffers) {\n    let outputLength = 0;\n    let prevLength = 0;\n    for (let i = 0; i < buffers.length; i++) {\n        const buffer = buffers[i];\n        outputLength += buffer.byteLength;\n    }\n    const retView = new Uint8Array(outputLength);\n    for (let i = 0; i < buffers.length; i++) {\n        const buffer = buffers[i];\n        retView.set(new Uint8Array(buffer), prevLength);\n        prevLength += buffer.byteLength;\n    }\n    return retView.buffer;\n}\nfunction checkBufferParams(baseBlock, inputBuffer, inputOffset, inputLength) {\n    if (!(inputBuffer instanceof Uint8Array)) {\n        baseBlock.error = \"Wrong parameter: inputBuffer must be 'Uint8Array'\";\n        return false;\n    }\n    if (!inputBuffer.byteLength) {\n        baseBlock.error = \"Wrong parameter: inputBuffer has zero length\";\n        return false;\n    }\n    if (inputOffset < 0) {\n        baseBlock.error = \"Wrong parameter: inputOffset less than zero\";\n        return false;\n    }\n    if (inputLength < 0) {\n        baseBlock.error = \"Wrong parameter: inputLength less than zero\";\n        return false;\n    }\n    if ((inputBuffer.byteLength - inputOffset - inputLength) < 0) {\n        baseBlock.error = \"End of input reached before message was fully decoded (inconsistent offset and length values)\";\n        return false;\n    }\n    return true;\n}\n\nclass ViewWriter {\n    constructor() {\n        this.items = [];\n    }\n    write(buf) {\n        this.items.push(buf);\n    }\n    final() {\n        return concat(this.items);\n    }\n}\n\nconst powers2 = [new Uint8Array([1])];\nconst digitsString = \"0123456789\";\nconst NAME = \"name\";\nconst VALUE_HEX_VIEW = \"valueHexView\";\nconst IS_HEX_ONLY = \"isHexOnly\";\nconst ID_BLOCK = \"idBlock\";\nconst TAG_CLASS = \"tagClass\";\nconst TAG_NUMBER = \"tagNumber\";\nconst IS_CONSTRUCTED = \"isConstructed\";\nconst FROM_BER = \"fromBER\";\nconst TO_BER = \"toBER\";\nconst LOCAL = \"local\";\nconst EMPTY_STRING = \"\";\nconst EMPTY_BUFFER = new ArrayBuffer(0);\nconst EMPTY_VIEW = new Uint8Array(0);\nconst END_OF_CONTENT_NAME = \"EndOfContent\";\nconst OCTET_STRING_NAME = \"OCTET STRING\";\nconst BIT_STRING_NAME = \"BIT STRING\";\n\nfunction HexBlock(BaseClass) {\n    var _a;\n    return _a = class Some extends BaseClass {\n            get valueHex() {\n                return this.valueHexView.slice().buffer;\n            }\n            set valueHex(value) {\n                this.valueHexView = new Uint8Array(value);\n            }\n            constructor(...args) {\n                var _b;\n                super(...args);\n                const params = args[0] || {};\n                this.isHexOnly = (_b = params.isHexOnly) !== null && _b !== void 0 ? _b : false;\n                this.valueHexView = params.valueHex ? pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.valueHex) : EMPTY_VIEW;\n            }\n            fromBER(inputBuffer, inputOffset, inputLength) {\n                const view = inputBuffer instanceof ArrayBuffer ? new Uint8Array(inputBuffer) : inputBuffer;\n                if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n                    return -1;\n                }\n                const endLength = inputOffset + inputLength;\n                this.valueHexView = view.subarray(inputOffset, endLength);\n                if (!this.valueHexView.length) {\n                    this.warnings.push(\"Zero buffer length\");\n                    return inputOffset;\n                }\n                this.blockLength = inputLength;\n                return endLength;\n            }\n            toBER(sizeOnly = false) {\n                if (!this.isHexOnly) {\n                    this.error = \"Flag 'isHexOnly' is not set, abort\";\n                    return EMPTY_BUFFER;\n                }\n                if (sizeOnly) {\n                    return new ArrayBuffer(this.valueHexView.byteLength);\n                }\n                return (this.valueHexView.byteLength === this.valueHexView.buffer.byteLength)\n                    ? this.valueHexView.buffer\n                    : this.valueHexView.slice().buffer;\n            }\n            toJSON() {\n                return {\n                    ...super.toJSON(),\n                    isHexOnly: this.isHexOnly,\n                    valueHex: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView),\n                };\n            }\n        },\n        _a.NAME = \"hexBlock\",\n        _a;\n}\n\nclass LocalBaseBlock {\n    static blockName() {\n        return this.NAME;\n    }\n    get valueBeforeDecode() {\n        return this.valueBeforeDecodeView.slice().buffer;\n    }\n    set valueBeforeDecode(value) {\n        this.valueBeforeDecodeView = new Uint8Array(value);\n    }\n    constructor({ blockLength = 0, error = EMPTY_STRING, warnings = [], valueBeforeDecode = EMPTY_VIEW, } = {}) {\n        this.blockLength = blockLength;\n        this.error = error;\n        this.warnings = warnings;\n        this.valueBeforeDecodeView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(valueBeforeDecode);\n    }\n    toJSON() {\n        return {\n            blockName: this.constructor.NAME,\n            blockLength: this.blockLength,\n            error: this.error,\n            warnings: this.warnings,\n            valueBeforeDecode: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBeforeDecodeView),\n        };\n    }\n}\nLocalBaseBlock.NAME = \"baseBlock\";\n\nclass ValueBlock extends LocalBaseBlock {\n    fromBER(_inputBuffer, _inputOffset, _inputLength) {\n        throw TypeError(\"User need to make a specific function in a class which extends 'ValueBlock'\");\n    }\n    toBER(_sizeOnly, _writer) {\n        throw TypeError(\"User need to make a specific function in a class which extends 'ValueBlock'\");\n    }\n}\nValueBlock.NAME = \"valueBlock\";\n\nclass LocalIdentificationBlock extends HexBlock(LocalBaseBlock) {\n    constructor({ idBlock = {} } = {}) {\n        var _a, _b, _c, _d;\n        super();\n        if (idBlock) {\n            this.isHexOnly = (_a = idBlock.isHexOnly) !== null && _a !== void 0 ? _a : false;\n            this.valueHexView = idBlock.valueHex\n                ? pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(idBlock.valueHex)\n                : EMPTY_VIEW;\n            this.tagClass = (_b = idBlock.tagClass) !== null && _b !== void 0 ? _b : -1;\n            this.tagNumber = (_c = idBlock.tagNumber) !== null && _c !== void 0 ? _c : -1;\n            this.isConstructed = (_d = idBlock.isConstructed) !== null && _d !== void 0 ? _d : false;\n        }\n        else {\n            this.tagClass = -1;\n            this.tagNumber = -1;\n            this.isConstructed = false;\n        }\n    }\n    toBER(sizeOnly = false) {\n        let firstOctet = 0;\n        switch (this.tagClass) {\n            case 1:\n                firstOctet |= 0x00;\n                break;\n            case 2:\n                firstOctet |= 0x40;\n                break;\n            case 3:\n                firstOctet |= 0x80;\n                break;\n            case 4:\n                firstOctet |= 0xC0;\n                break;\n            default:\n                this.error = \"Unknown tag class\";\n                return EMPTY_BUFFER;\n        }\n        if (this.isConstructed)\n            firstOctet |= 0x20;\n        if (this.tagNumber < 31 && !this.isHexOnly) {\n            const retView = new Uint8Array(1);\n            if (!sizeOnly) {\n                let number = this.tagNumber;\n                number &= 0x1F;\n                firstOctet |= number;\n                retView[0] = firstOctet;\n            }\n            return retView.buffer;\n        }\n        if (!this.isHexOnly) {\n            const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.tagNumber, 7);\n            const encodedView = new Uint8Array(encodedBuf);\n            const size = encodedBuf.byteLength;\n            const retView = new Uint8Array(size + 1);\n            retView[0] = (firstOctet | 0x1F);\n            if (!sizeOnly) {\n                for (let i = 0; i < (size - 1); i++)\n                    retView[i + 1] = encodedView[i] | 0x80;\n                retView[size] = encodedView[size - 1];\n            }\n            return retView.buffer;\n        }\n        const retView = new Uint8Array(this.valueHexView.byteLength + 1);\n        retView[0] = (firstOctet | 0x1F);\n        if (!sizeOnly) {\n            const curView = this.valueHexView;\n            for (let i = 0; i < (curView.length - 1); i++)\n                retView[i + 1] = curView[i] | 0x80;\n            retView[this.valueHexView.byteLength] = curView[curView.length - 1];\n        }\n        return retView.buffer;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        if (intBuffer.length === 0) {\n            this.error = \"Zero buffer length\";\n            return -1;\n        }\n        const tagClassMask = intBuffer[0] & 0xC0;\n        switch (tagClassMask) {\n            case 0x00:\n                this.tagClass = (1);\n                break;\n            case 0x40:\n                this.tagClass = (2);\n                break;\n            case 0x80:\n                this.tagClass = (3);\n                break;\n            case 0xC0:\n                this.tagClass = (4);\n                break;\n            default:\n                this.error = \"Unknown tag class\";\n                return -1;\n        }\n        this.isConstructed = (intBuffer[0] & 0x20) === 0x20;\n        this.isHexOnly = false;\n        const tagNumberMask = intBuffer[0] & 0x1F;\n        if (tagNumberMask !== 0x1F) {\n            this.tagNumber = (tagNumberMask);\n            this.blockLength = 1;\n        }\n        else {\n            let count = 1;\n            let intTagNumberBuffer = this.valueHexView = new Uint8Array(255);\n            let tagNumberBufferMaxLength = 255;\n            while (intBuffer[count] & 0x80) {\n                intTagNumberBuffer[count - 1] = intBuffer[count] & 0x7F;\n                count++;\n                if (count >= intBuffer.length) {\n                    this.error = \"End of input reached before message was fully decoded\";\n                    return -1;\n                }\n                if (count === tagNumberBufferMaxLength) {\n                    tagNumberBufferMaxLength += 255;\n                    const tempBufferView = new Uint8Array(tagNumberBufferMaxLength);\n                    for (let i = 0; i < intTagNumberBuffer.length; i++)\n                        tempBufferView[i] = intTagNumberBuffer[i];\n                    intTagNumberBuffer = this.valueHexView = new Uint8Array(tagNumberBufferMaxLength);\n                }\n            }\n            this.blockLength = (count + 1);\n            intTagNumberBuffer[count - 1] = intBuffer[count] & 0x7F;\n            const tempBufferView = new Uint8Array(count);\n            for (let i = 0; i < count; i++)\n                tempBufferView[i] = intTagNumberBuffer[i];\n            intTagNumberBuffer = this.valueHexView = new Uint8Array(count);\n            intTagNumberBuffer.set(tempBufferView);\n            if (this.blockLength <= 9)\n                this.tagNumber = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(intTagNumberBuffer, 7);\n            else {\n                this.isHexOnly = true;\n                this.warnings.push(\"Tag too long, represented as hex-coded\");\n            }\n        }\n        if (((this.tagClass === 1))\n            && (this.isConstructed)) {\n            switch (this.tagNumber) {\n                case 1:\n                case 2:\n                case 5:\n                case 6:\n                case 9:\n                case 13:\n                case 14:\n                case 23:\n                case 24:\n                case 31:\n                case 32:\n                case 33:\n                case 34:\n                    this.error = \"Constructed encoding used for primitive type\";\n                    return -1;\n            }\n        }\n        return (inputOffset + this.blockLength);\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            tagClass: this.tagClass,\n            tagNumber: this.tagNumber,\n            isConstructed: this.isConstructed,\n        };\n    }\n}\nLocalIdentificationBlock.NAME = \"identificationBlock\";\n\nclass LocalLengthBlock extends LocalBaseBlock {\n    constructor({ lenBlock = {} } = {}) {\n        var _a, _b, _c;\n        super();\n        this.isIndefiniteForm = (_a = lenBlock.isIndefiniteForm) !== null && _a !== void 0 ? _a : false;\n        this.longFormUsed = (_b = lenBlock.longFormUsed) !== null && _b !== void 0 ? _b : false;\n        this.length = (_c = lenBlock.length) !== null && _c !== void 0 ? _c : 0;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = view.subarray(inputOffset, inputOffset + inputLength);\n        if (intBuffer.length === 0) {\n            this.error = \"Zero buffer length\";\n            return -1;\n        }\n        if (intBuffer[0] === 0xFF) {\n            this.error = \"Length block 0xFF is reserved by standard\";\n            return -1;\n        }\n        this.isIndefiniteForm = intBuffer[0] === 0x80;\n        if (this.isIndefiniteForm) {\n            this.blockLength = 1;\n            return (inputOffset + this.blockLength);\n        }\n        this.longFormUsed = !!(intBuffer[0] & 0x80);\n        if (this.longFormUsed === false) {\n            this.length = (intBuffer[0]);\n            this.blockLength = 1;\n            return (inputOffset + this.blockLength);\n        }\n        const count = intBuffer[0] & 0x7F;\n        if (count > 8) {\n            this.error = \"Too big integer\";\n            return -1;\n        }\n        if ((count + 1) > intBuffer.length) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        const lenOffset = inputOffset + 1;\n        const lengthBufferView = view.subarray(lenOffset, lenOffset + count);\n        if (lengthBufferView[count - 1] === 0x00)\n            this.warnings.push(\"Needlessly long encoded length\");\n        this.length = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(lengthBufferView, 8);\n        if (this.longFormUsed && (this.length <= 127))\n            this.warnings.push(\"Unnecessary usage of long length form\");\n        this.blockLength = count + 1;\n        return (inputOffset + this.blockLength);\n    }\n    toBER(sizeOnly = false) {\n        let retBuf;\n        let retView;\n        if (this.length > 127)\n            this.longFormUsed = true;\n        if (this.isIndefiniteForm) {\n            retBuf = new ArrayBuffer(1);\n            if (sizeOnly === false) {\n                retView = new Uint8Array(retBuf);\n                retView[0] = 0x80;\n            }\n            return retBuf;\n        }\n        if (this.longFormUsed) {\n            const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.length, 8);\n            if (encodedBuf.byteLength > 127) {\n                this.error = \"Too big length\";\n                return (EMPTY_BUFFER);\n            }\n            retBuf = new ArrayBuffer(encodedBuf.byteLength + 1);\n            if (sizeOnly)\n                return retBuf;\n            const encodedView = new Uint8Array(encodedBuf);\n            retView = new Uint8Array(retBuf);\n            retView[0] = encodedBuf.byteLength | 0x80;\n            for (let i = 0; i < encodedBuf.byteLength; i++)\n                retView[i + 1] = encodedView[i];\n            return retBuf;\n        }\n        retBuf = new ArrayBuffer(1);\n        if (sizeOnly === false) {\n            retView = new Uint8Array(retBuf);\n            retView[0] = this.length;\n        }\n        return retBuf;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            isIndefiniteForm: this.isIndefiniteForm,\n            longFormUsed: this.longFormUsed,\n            length: this.length,\n        };\n    }\n}\nLocalLengthBlock.NAME = \"lengthBlock\";\n\nconst typeStore = {};\n\nclass BaseBlock extends LocalBaseBlock {\n    constructor({ name = EMPTY_STRING, optional = false, primitiveSchema, ...parameters } = {}, valueBlockType) {\n        super(parameters);\n        this.name = name;\n        this.optional = optional;\n        if (primitiveSchema) {\n            this.primitiveSchema = primitiveSchema;\n        }\n        this.idBlock = new LocalIdentificationBlock(parameters);\n        this.lenBlock = new LocalLengthBlock(parameters);\n        this.valueBlock = valueBlockType ? new valueBlockType(parameters) : new ValueBlock(parameters);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, (this.lenBlock.isIndefiniteForm)\n            ? inputLength\n            : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        if (!this.idBlock.error.length)\n            this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length)\n            this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length)\n            this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    toBER(sizeOnly, writer) {\n        const _writer = writer || new ViewWriter();\n        if (!writer) {\n            prepareIndefiniteForm(this);\n        }\n        const idBlockBuf = this.idBlock.toBER(sizeOnly);\n        _writer.write(idBlockBuf);\n        if (this.lenBlock.isIndefiniteForm) {\n            _writer.write(new Uint8Array([0x80]).buffer);\n            this.valueBlock.toBER(sizeOnly, _writer);\n            _writer.write(new ArrayBuffer(2));\n        }\n        else {\n            const valueBlockBuf = this.valueBlock.toBER(sizeOnly);\n            this.lenBlock.length = valueBlockBuf.byteLength;\n            const lenBlockBuf = this.lenBlock.toBER(sizeOnly);\n            _writer.write(lenBlockBuf);\n            _writer.write(valueBlockBuf);\n        }\n        if (!writer) {\n            return _writer.final();\n        }\n        return EMPTY_BUFFER;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            idBlock: this.idBlock.toJSON(),\n            lenBlock: this.lenBlock.toJSON(),\n            valueBlock: this.valueBlock.toJSON(),\n            name: this.name,\n            optional: this.optional,\n        };\n        if (this.primitiveSchema)\n            object.primitiveSchema = this.primitiveSchema.toJSON();\n        return object;\n    }\n    toString(encoding = \"ascii\") {\n        if (encoding === \"ascii\") {\n            return this.onAsciiEncoding();\n        }\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.toBER());\n    }\n    onAsciiEncoding() {\n        const name = this.constructor.NAME;\n        const value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBlock.valueBeforeDecodeView);\n        return `${name} : ${value}`;\n    }\n    isEqual(other) {\n        if (this === other) {\n            return true;\n        }\n        if (!(other instanceof this.constructor)) {\n            return false;\n        }\n        const thisRaw = this.toBER();\n        const otherRaw = other.toBER();\n        return pvutils__WEBPACK_IMPORTED_MODULE_1__.isEqualBuffer(thisRaw, otherRaw);\n    }\n}\nBaseBlock.NAME = \"BaseBlock\";\nfunction prepareIndefiniteForm(baseBlock) {\n    var _a;\n    if (baseBlock instanceof typeStore.Constructed) {\n        for (const value of baseBlock.valueBlock.value) {\n            if (prepareIndefiniteForm(value)) {\n                baseBlock.lenBlock.isIndefiniteForm = true;\n            }\n        }\n    }\n    return !!((_a = baseBlock.lenBlock) === null || _a === void 0 ? void 0 : _a.isIndefiniteForm);\n}\n\nclass BaseStringBlock extends BaseBlock {\n    getValue() {\n        return this.valueBlock.value;\n    }\n    setValue(value) {\n        this.valueBlock.value = value;\n    }\n    constructor({ value = EMPTY_STRING, ...parameters } = {}, stringValueBlockType) {\n        super(parameters, stringValueBlockType);\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, (this.lenBlock.isIndefiniteForm)\n            ? inputLength\n            : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        this.fromBuffer(this.valueBlock.valueHexView);\n        if (!this.idBlock.error.length)\n            this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length)\n            this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length)\n            this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : '${this.valueBlock.value}'`;\n    }\n}\nBaseStringBlock.NAME = \"BaseStringBlock\";\n\nclass LocalPrimitiveValueBlock extends HexBlock(ValueBlock) {\n    constructor({ isHexOnly = true, ...parameters } = {}) {\n        super(parameters);\n        this.isHexOnly = isHexOnly;\n    }\n}\nLocalPrimitiveValueBlock.NAME = \"PrimitiveValueBlock\";\n\nvar _a$w;\nclass Primitive extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, LocalPrimitiveValueBlock);\n        this.idBlock.isConstructed = false;\n    }\n}\n_a$w = Primitive;\n(() => {\n    typeStore.Primitive = _a$w;\n})();\nPrimitive.NAME = \"PRIMITIVE\";\n\nfunction localChangeType(inputObject, newType) {\n    if (inputObject instanceof newType) {\n        return inputObject;\n    }\n    const newObject = new newType();\n    newObject.idBlock = inputObject.idBlock;\n    newObject.lenBlock = inputObject.lenBlock;\n    newObject.warnings = inputObject.warnings;\n    newObject.valueBeforeDecodeView = inputObject.valueBeforeDecodeView;\n    return newObject;\n}\nfunction localFromBER(inputBuffer, inputOffset = 0, inputLength = inputBuffer.length) {\n    const incomingOffset = inputOffset;\n    let returnObject = new BaseBlock({}, ValueBlock);\n    const baseBlock = new LocalBaseBlock();\n    if (!checkBufferParams(baseBlock, inputBuffer, inputOffset, inputLength)) {\n        returnObject.error = baseBlock.error;\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    const intBuffer = inputBuffer.subarray(inputOffset, inputOffset + inputLength);\n    if (!intBuffer.length) {\n        returnObject.error = \"Zero buffer length\";\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    let resultOffset = returnObject.idBlock.fromBER(inputBuffer, inputOffset, inputLength);\n    if (returnObject.idBlock.warnings.length) {\n        returnObject.warnings.concat(returnObject.idBlock.warnings);\n    }\n    if (resultOffset === -1) {\n        returnObject.error = returnObject.idBlock.error;\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    inputOffset = resultOffset;\n    inputLength -= returnObject.idBlock.blockLength;\n    resultOffset = returnObject.lenBlock.fromBER(inputBuffer, inputOffset, inputLength);\n    if (returnObject.lenBlock.warnings.length) {\n        returnObject.warnings.concat(returnObject.lenBlock.warnings);\n    }\n    if (resultOffset === -1) {\n        returnObject.error = returnObject.lenBlock.error;\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    inputOffset = resultOffset;\n    inputLength -= returnObject.lenBlock.blockLength;\n    if (!returnObject.idBlock.isConstructed\n        && returnObject.lenBlock.isIndefiniteForm) {\n        returnObject.error = \"Indefinite length form used for primitive encoding form\";\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    let newASN1Type = BaseBlock;\n    switch (returnObject.idBlock.tagClass) {\n        case 1:\n            if ((returnObject.idBlock.tagNumber >= 37)\n                && (returnObject.idBlock.isHexOnly === false)) {\n                returnObject.error = \"UNIVERSAL 37 and upper tags are reserved by ASN.1 standard\";\n                return {\n                    offset: -1,\n                    result: returnObject,\n                };\n            }\n            switch (returnObject.idBlock.tagNumber) {\n                case 0:\n                    if ((returnObject.idBlock.isConstructed)\n                        && (returnObject.lenBlock.length > 0)) {\n                        returnObject.error = \"Type [UNIVERSAL 0] is reserved\";\n                        return {\n                            offset: -1,\n                            result: returnObject,\n                        };\n                    }\n                    newASN1Type = typeStore.EndOfContent;\n                    break;\n                case 1:\n                    newASN1Type = typeStore.Boolean;\n                    break;\n                case 2:\n                    newASN1Type = typeStore.Integer;\n                    break;\n                case 3:\n                    newASN1Type = typeStore.BitString;\n                    break;\n                case 4:\n                    newASN1Type = typeStore.OctetString;\n                    break;\n                case 5:\n                    newASN1Type = typeStore.Null;\n                    break;\n                case 6:\n                    newASN1Type = typeStore.ObjectIdentifier;\n                    break;\n                case 10:\n                    newASN1Type = typeStore.Enumerated;\n                    break;\n                case 12:\n                    newASN1Type = typeStore.Utf8String;\n                    break;\n                case 13:\n                    newASN1Type = typeStore.RelativeObjectIdentifier;\n                    break;\n                case 14:\n                    newASN1Type = typeStore.TIME;\n                    break;\n                case 15:\n                    returnObject.error = \"[UNIVERSAL 15] is reserved by ASN.1 standard\";\n                    return {\n                        offset: -1,\n                        result: returnObject,\n                    };\n                case 16:\n                    newASN1Type = typeStore.Sequence;\n                    break;\n                case 17:\n                    newASN1Type = typeStore.Set;\n                    break;\n                case 18:\n                    newASN1Type = typeStore.NumericString;\n                    break;\n                case 19:\n                    newASN1Type = typeStore.PrintableString;\n                    break;\n                case 20:\n                    newASN1Type = typeStore.TeletexString;\n                    break;\n                case 21:\n                    newASN1Type = typeStore.VideotexString;\n                    break;\n                case 22:\n                    newASN1Type = typeStore.IA5String;\n                    break;\n                case 23:\n                    newASN1Type = typeStore.UTCTime;\n                    break;\n                case 24:\n                    newASN1Type = typeStore.GeneralizedTime;\n                    break;\n                case 25:\n                    newASN1Type = typeStore.GraphicString;\n                    break;\n                case 26:\n                    newASN1Type = typeStore.VisibleString;\n                    break;\n                case 27:\n                    newASN1Type = typeStore.GeneralString;\n                    break;\n                case 28:\n                    newASN1Type = typeStore.UniversalString;\n                    break;\n                case 29:\n                    newASN1Type = typeStore.CharacterString;\n                    break;\n                case 30:\n                    newASN1Type = typeStore.BmpString;\n                    break;\n                case 31:\n                    newASN1Type = typeStore.DATE;\n                    break;\n                case 32:\n                    newASN1Type = typeStore.TimeOfDay;\n                    break;\n                case 33:\n                    newASN1Type = typeStore.DateTime;\n                    break;\n                case 34:\n                    newASN1Type = typeStore.Duration;\n                    break;\n                default: {\n                    const newObject = returnObject.idBlock.isConstructed\n                        ? new typeStore.Constructed()\n                        : new typeStore.Primitive();\n                    newObject.idBlock = returnObject.idBlock;\n                    newObject.lenBlock = returnObject.lenBlock;\n                    newObject.warnings = returnObject.warnings;\n                    returnObject = newObject;\n                }\n            }\n            break;\n        case 2:\n        case 3:\n        case 4:\n        default: {\n            newASN1Type = returnObject.idBlock.isConstructed\n                ? typeStore.Constructed\n                : typeStore.Primitive;\n        }\n    }\n    returnObject = localChangeType(returnObject, newASN1Type);\n    resultOffset = returnObject.fromBER(inputBuffer, inputOffset, returnObject.lenBlock.isIndefiniteForm ? inputLength : returnObject.lenBlock.length);\n    returnObject.valueBeforeDecodeView = inputBuffer.subarray(incomingOffset, incomingOffset + returnObject.blockLength);\n    return {\n        offset: resultOffset,\n        result: returnObject,\n    };\n}\nfunction fromBER(inputBuffer) {\n    if (!inputBuffer.byteLength) {\n        const result = new BaseBlock({}, ValueBlock);\n        result.error = \"Input buffer has zero length\";\n        return {\n            offset: -1,\n            result,\n        };\n    }\n    return localFromBER(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer).slice(), 0, inputBuffer.byteLength);\n}\n\nfunction checkLen(indefiniteLength, length) {\n    if (indefiniteLength) {\n        return 1;\n    }\n    return length;\n}\nclass LocalConstructedValueBlock extends ValueBlock {\n    constructor({ value = [], isIndefiniteForm = false, ...parameters } = {}) {\n        super(parameters);\n        this.value = value;\n        this.isIndefiniteForm = isIndefiniteForm;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n            return -1;\n        }\n        this.valueBeforeDecodeView = view.subarray(inputOffset, inputOffset + inputLength);\n        if (this.valueBeforeDecodeView.length === 0) {\n            this.warnings.push(\"Zero buffer length\");\n            return inputOffset;\n        }\n        let currentOffset = inputOffset;\n        while (checkLen(this.isIndefiniteForm, inputLength) > 0) {\n            const returnObject = localFromBER(view, currentOffset, inputLength);\n            if (returnObject.offset === -1) {\n                this.error = returnObject.result.error;\n                this.warnings.concat(returnObject.result.warnings);\n                return -1;\n            }\n            currentOffset = returnObject.offset;\n            this.blockLength += returnObject.result.blockLength;\n            inputLength -= returnObject.result.blockLength;\n            this.value.push(returnObject.result);\n            if (this.isIndefiniteForm && returnObject.result.constructor.NAME === END_OF_CONTENT_NAME) {\n                break;\n            }\n        }\n        if (this.isIndefiniteForm) {\n            if (this.value[this.value.length - 1].constructor.NAME === END_OF_CONTENT_NAME) {\n                this.value.pop();\n            }\n            else {\n                this.warnings.push(\"No EndOfContent block encoded\");\n            }\n        }\n        return currentOffset;\n    }\n    toBER(sizeOnly, writer) {\n        const _writer = writer || new ViewWriter();\n        for (let i = 0; i < this.value.length; i++) {\n            this.value[i].toBER(sizeOnly, _writer);\n        }\n        if (!writer) {\n            return _writer.final();\n        }\n        return EMPTY_BUFFER;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            isIndefiniteForm: this.isIndefiniteForm,\n            value: [],\n        };\n        for (const value of this.value) {\n            object.value.push(value.toJSON());\n        }\n        return object;\n    }\n}\nLocalConstructedValueBlock.NAME = \"ConstructedValueBlock\";\n\nvar _a$v;\nclass Constructed extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, LocalConstructedValueBlock);\n        this.idBlock.isConstructed = true;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, (this.lenBlock.isIndefiniteForm) ? inputLength : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        if (!this.idBlock.error.length)\n            this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length)\n            this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length)\n            this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    onAsciiEncoding() {\n        const values = [];\n        for (const value of this.valueBlock.value) {\n            values.push(value.toString(\"ascii\").split(\"\\n\").map((o) => `  ${o}`).join(\"\\n\"));\n        }\n        const blockName = this.idBlock.tagClass === 3\n            ? `[${this.idBlock.tagNumber}]`\n            : this.constructor.NAME;\n        return values.length\n            ? `${blockName} :\\n${values.join(\"\\n\")}`\n            : `${blockName} :`;\n    }\n}\n_a$v = Constructed;\n(() => {\n    typeStore.Constructed = _a$v;\n})();\nConstructed.NAME = \"CONSTRUCTED\";\n\nclass LocalEndOfContentValueBlock extends ValueBlock {\n    fromBER(inputBuffer, inputOffset, _inputLength) {\n        return inputOffset;\n    }\n    toBER(_sizeOnly) {\n        return EMPTY_BUFFER;\n    }\n}\nLocalEndOfContentValueBlock.override = \"EndOfContentValueBlock\";\n\nvar _a$u;\nclass EndOfContent extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, LocalEndOfContentValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 0;\n    }\n}\n_a$u = EndOfContent;\n(() => {\n    typeStore.EndOfContent = _a$u;\n})();\nEndOfContent.NAME = END_OF_CONTENT_NAME;\n\nvar _a$t;\nclass Null extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, ValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 5;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (this.lenBlock.length > 0)\n            this.warnings.push(\"Non-zero length of value block for Null type\");\n        if (!this.idBlock.error.length)\n            this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length)\n            this.blockLength += this.lenBlock.blockLength;\n        this.blockLength += inputLength;\n        if ((inputOffset + inputLength) > inputBuffer.byteLength) {\n            this.error = \"End of input reached before message was fully decoded (inconsistent offset and length values)\";\n            return -1;\n        }\n        return (inputOffset + inputLength);\n    }\n    toBER(sizeOnly, writer) {\n        const retBuf = new ArrayBuffer(2);\n        if (!sizeOnly) {\n            const retView = new Uint8Array(retBuf);\n            retView[0] = 0x05;\n            retView[1] = 0x00;\n        }\n        if (writer) {\n            writer.write(retBuf);\n        }\n        return retBuf;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME}`;\n    }\n}\n_a$t = Null;\n(() => {\n    typeStore.Null = _a$t;\n})();\nNull.NAME = \"NULL\";\n\nclass LocalBooleanValueBlock extends HexBlock(ValueBlock) {\n    get value() {\n        for (const octet of this.valueHexView) {\n            if (octet > 0) {\n                return true;\n            }\n        }\n        return false;\n    }\n    set value(value) {\n        this.valueHexView[0] = value ? 0xFF : 0x00;\n    }\n    constructor({ value, ...parameters } = {}) {\n        super(parameters);\n        if (parameters.valueHex) {\n            this.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(parameters.valueHex);\n        }\n        else {\n            this.valueHexView = new Uint8Array(1);\n        }\n        if (value) {\n            this.value = value;\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        this.valueHexView = inputView.subarray(inputOffset, inputOffset + inputLength);\n        if (inputLength > 1)\n            this.warnings.push(\"Boolean value encoded in more then 1 octet\");\n        this.isHexOnly = true;\n        pvutils__WEBPACK_IMPORTED_MODULE_1__.utilDecodeTC.call(this);\n        this.blockLength = inputLength;\n        return (inputOffset + inputLength);\n    }\n    toBER() {\n        return this.valueHexView.slice();\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.value,\n        };\n    }\n}\nLocalBooleanValueBlock.NAME = \"BooleanValueBlock\";\n\nvar _a$s;\nclass Boolean extends BaseBlock {\n    getValue() {\n        return this.valueBlock.value;\n    }\n    setValue(value) {\n        this.valueBlock.value = value;\n    }\n    constructor(parameters = {}) {\n        super(parameters, LocalBooleanValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 1;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.getValue}`;\n    }\n}\n_a$s = Boolean;\n(() => {\n    typeStore.Boolean = _a$s;\n})();\nBoolean.NAME = \"BOOLEAN\";\n\nclass LocalOctetStringValueBlock extends HexBlock(LocalConstructedValueBlock) {\n    constructor({ isConstructed = false, ...parameters } = {}) {\n        super(parameters);\n        this.isConstructed = isConstructed;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = 0;\n        if (this.isConstructed) {\n            this.isHexOnly = false;\n            resultOffset = LocalConstructedValueBlock.prototype.fromBER.call(this, inputBuffer, inputOffset, inputLength);\n            if (resultOffset === -1)\n                return resultOffset;\n            for (let i = 0; i < this.value.length; i++) {\n                const currentBlockName = this.value[i].constructor.NAME;\n                if (currentBlockName === END_OF_CONTENT_NAME) {\n                    if (this.isIndefiniteForm)\n                        break;\n                    else {\n                        this.error = \"EndOfContent is unexpected, OCTET STRING may consists of OCTET STRINGs only\";\n                        return -1;\n                    }\n                }\n                if (currentBlockName !== OCTET_STRING_NAME) {\n                    this.error = \"OCTET STRING may consists of OCTET STRINGs only\";\n                    return -1;\n                }\n            }\n        }\n        else {\n            this.isHexOnly = true;\n            resultOffset = super.fromBER(inputBuffer, inputOffset, inputLength);\n            this.blockLength = inputLength;\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly, writer) {\n        if (this.isConstructed)\n            return LocalConstructedValueBlock.prototype.toBER.call(this, sizeOnly, writer);\n        return sizeOnly\n            ? new ArrayBuffer(this.valueHexView.byteLength)\n            : this.valueHexView.slice().buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            isConstructed: this.isConstructed,\n        };\n    }\n}\nLocalOctetStringValueBlock.NAME = \"OctetStringValueBlock\";\n\nvar _a$r;\nclass OctetString extends BaseBlock {\n    constructor({ idBlock = {}, lenBlock = {}, ...parameters } = {}) {\n        var _b, _c;\n        (_b = parameters.isConstructed) !== null && _b !== void 0 ? _b : (parameters.isConstructed = !!((_c = parameters.value) === null || _c === void 0 ? void 0 : _c.length));\n        super({\n            idBlock: {\n                isConstructed: parameters.isConstructed,\n                ...idBlock,\n            },\n            lenBlock: {\n                ...lenBlock,\n                isIndefiniteForm: !!parameters.isIndefiniteForm,\n            },\n            ...parameters,\n        }, LocalOctetStringValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 4;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isConstructed = this.idBlock.isConstructed;\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        if (inputLength === 0) {\n            if (this.idBlock.error.length === 0)\n                this.blockLength += this.idBlock.blockLength;\n            if (this.lenBlock.error.length === 0)\n                this.blockLength += this.lenBlock.blockLength;\n            return inputOffset;\n        }\n        if (!this.valueBlock.isConstructed) {\n            const view = inputBuffer instanceof ArrayBuffer ? new Uint8Array(inputBuffer) : inputBuffer;\n            const buf = view.subarray(inputOffset, inputOffset + inputLength);\n            try {\n                if (buf.byteLength) {\n                    const asn = localFromBER(buf, 0, buf.byteLength);\n                    if (asn.offset !== -1 && asn.offset === inputLength) {\n                        this.valueBlock.value = [asn.result];\n                    }\n                }\n            }\n            catch {\n            }\n        }\n        return super.fromBER(inputBuffer, inputOffset, inputLength);\n    }\n    onAsciiEncoding() {\n        if (this.valueBlock.isConstructed || (this.valueBlock.value && this.valueBlock.value.length)) {\n            return Constructed.prototype.onAsciiEncoding.call(this);\n        }\n        const name = this.constructor.NAME;\n        const value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBlock.valueHexView);\n        return `${name} : ${value}`;\n    }\n    getValue() {\n        if (!this.idBlock.isConstructed) {\n            return this.valueBlock.valueHexView.slice().buffer;\n        }\n        const array = [];\n        for (const content of this.valueBlock.value) {\n            if (content instanceof _a$r) {\n                array.push(content.valueBlock.valueHexView);\n            }\n        }\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.concat(array);\n    }\n}\n_a$r = OctetString;\n(() => {\n    typeStore.OctetString = _a$r;\n})();\nOctetString.NAME = OCTET_STRING_NAME;\n\nclass LocalBitStringValueBlock extends HexBlock(LocalConstructedValueBlock) {\n    constructor({ unusedBits = 0, isConstructed = false, ...parameters } = {}) {\n        super(parameters);\n        this.unusedBits = unusedBits;\n        this.isConstructed = isConstructed;\n        this.blockLength = this.valueHexView.byteLength;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (!inputLength) {\n            return inputOffset;\n        }\n        let resultOffset = -1;\n        if (this.isConstructed) {\n            resultOffset = LocalConstructedValueBlock.prototype.fromBER.call(this, inputBuffer, inputOffset, inputLength);\n            if (resultOffset === -1)\n                return resultOffset;\n            for (const value of this.value) {\n                const currentBlockName = value.constructor.NAME;\n                if (currentBlockName === END_OF_CONTENT_NAME) {\n                    if (this.isIndefiniteForm)\n                        break;\n                    else {\n                        this.error = \"EndOfContent is unexpected, BIT STRING may consists of BIT STRINGs only\";\n                        return -1;\n                    }\n                }\n                if (currentBlockName !== BIT_STRING_NAME) {\n                    this.error = \"BIT STRING may consists of BIT STRINGs only\";\n                    return -1;\n                }\n                const valueBlock = value.valueBlock;\n                if ((this.unusedBits > 0) && (valueBlock.unusedBits > 0)) {\n                    this.error = \"Using of \\\"unused bits\\\" inside constructive BIT STRING allowed for least one only\";\n                    return -1;\n                }\n                this.unusedBits = valueBlock.unusedBits;\n            }\n            return resultOffset;\n        }\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.unusedBits = intBuffer[0];\n        if (this.unusedBits > 7) {\n            this.error = \"Unused bits for BitString must be in range 0-7\";\n            return -1;\n        }\n        if (!this.unusedBits) {\n            const buf = intBuffer.subarray(1);\n            try {\n                if (buf.byteLength) {\n                    const asn = localFromBER(buf, 0, buf.byteLength);\n                    if (asn.offset !== -1 && asn.offset === (inputLength - 1)) {\n                        this.value = [asn.result];\n                    }\n                }\n            }\n            catch {\n            }\n        }\n        this.valueHexView = intBuffer.subarray(1);\n        this.blockLength = intBuffer.length;\n        return (inputOffset + inputLength);\n    }\n    toBER(sizeOnly, writer) {\n        if (this.isConstructed) {\n            return LocalConstructedValueBlock.prototype.toBER.call(this, sizeOnly, writer);\n        }\n        if (sizeOnly) {\n            return new ArrayBuffer(this.valueHexView.byteLength + 1);\n        }\n        if (!this.valueHexView.byteLength) {\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(this.valueHexView.length + 1);\n        retView[0] = this.unusedBits;\n        retView.set(this.valueHexView, 1);\n        return retView.buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            unusedBits: this.unusedBits,\n            isConstructed: this.isConstructed,\n        };\n    }\n}\nLocalBitStringValueBlock.NAME = \"BitStringValueBlock\";\n\nvar _a$q;\nclass BitString extends BaseBlock {\n    constructor({ idBlock = {}, lenBlock = {}, ...parameters } = {}) {\n        var _b, _c;\n        (_b = parameters.isConstructed) !== null && _b !== void 0 ? _b : (parameters.isConstructed = !!((_c = parameters.value) === null || _c === void 0 ? void 0 : _c.length));\n        super({\n            idBlock: {\n                isConstructed: parameters.isConstructed,\n                ...idBlock,\n            },\n            lenBlock: {\n                ...lenBlock,\n                isIndefiniteForm: !!parameters.isIndefiniteForm,\n            },\n            ...parameters,\n        }, LocalBitStringValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 3;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isConstructed = this.idBlock.isConstructed;\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        return super.fromBER(inputBuffer, inputOffset, inputLength);\n    }\n    onAsciiEncoding() {\n        if (this.valueBlock.isConstructed || (this.valueBlock.value && this.valueBlock.value.length)) {\n            return Constructed.prototype.onAsciiEncoding.call(this);\n        }\n        else {\n            const bits = [];\n            const valueHex = this.valueBlock.valueHexView;\n            for (const byte of valueHex) {\n                bits.push(byte.toString(2).padStart(8, \"0\"));\n            }\n            const bitsStr = bits.join(\"\");\n            const name = this.constructor.NAME;\n            const value = bitsStr.substring(0, bitsStr.length - this.valueBlock.unusedBits);\n            return `${name} : ${value}`;\n        }\n    }\n}\n_a$q = BitString;\n(() => {\n    typeStore.BitString = _a$q;\n})();\nBitString.NAME = BIT_STRING_NAME;\n\nvar _a$p;\nfunction viewAdd(first, second) {\n    const c = new Uint8Array([0]);\n    const firstView = new Uint8Array(first);\n    const secondView = new Uint8Array(second);\n    let firstViewCopy = firstView.slice(0);\n    const firstViewCopyLength = firstViewCopy.length - 1;\n    const secondViewCopy = secondView.slice(0);\n    const secondViewCopyLength = secondViewCopy.length - 1;\n    let value = 0;\n    const max = (secondViewCopyLength < firstViewCopyLength) ? firstViewCopyLength : secondViewCopyLength;\n    let counter = 0;\n    for (let i = max; i >= 0; i--, counter++) {\n        switch (true) {\n            case (counter < secondViewCopy.length):\n                value = firstViewCopy[firstViewCopyLength - counter] + secondViewCopy[secondViewCopyLength - counter] + c[0];\n                break;\n            default:\n                value = firstViewCopy[firstViewCopyLength - counter] + c[0];\n        }\n        c[0] = value / 10;\n        switch (true) {\n            case (counter >= firstViewCopy.length):\n                firstViewCopy = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(new Uint8Array([value % 10]), firstViewCopy);\n                break;\n            default:\n                firstViewCopy[firstViewCopyLength - counter] = value % 10;\n        }\n    }\n    if (c[0] > 0)\n        firstViewCopy = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(c, firstViewCopy);\n    return firstViewCopy;\n}\nfunction power2(n) {\n    if (n >= powers2.length) {\n        for (let p = powers2.length; p <= n; p++) {\n            const c = new Uint8Array([0]);\n            let digits = (powers2[p - 1]).slice(0);\n            for (let i = (digits.length - 1); i >= 0; i--) {\n                const newValue = new Uint8Array([(digits[i] << 1) + c[0]]);\n                c[0] = newValue[0] / 10;\n                digits[i] = newValue[0] % 10;\n            }\n            if (c[0] > 0)\n                digits = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(c, digits);\n            powers2.push(digits);\n        }\n    }\n    return powers2[n];\n}\nfunction viewSub(first, second) {\n    let b = 0;\n    const firstView = new Uint8Array(first);\n    const secondView = new Uint8Array(second);\n    const firstViewCopy = firstView.slice(0);\n    const firstViewCopyLength = firstViewCopy.length - 1;\n    const secondViewCopy = secondView.slice(0);\n    const secondViewCopyLength = secondViewCopy.length - 1;\n    let value;\n    let counter = 0;\n    for (let i = secondViewCopyLength; i >= 0; i--, counter++) {\n        value = firstViewCopy[firstViewCopyLength - counter] - secondViewCopy[secondViewCopyLength - counter] - b;\n        switch (true) {\n            case (value < 0):\n                b = 1;\n                firstViewCopy[firstViewCopyLength - counter] = value + 10;\n                break;\n            default:\n                b = 0;\n                firstViewCopy[firstViewCopyLength - counter] = value;\n        }\n    }\n    if (b > 0) {\n        for (let i = (firstViewCopyLength - secondViewCopyLength + 1); i >= 0; i--, counter++) {\n            value = firstViewCopy[firstViewCopyLength - counter] - b;\n            if (value < 0) {\n                b = 1;\n                firstViewCopy[firstViewCopyLength - counter] = value + 10;\n            }\n            else {\n                b = 0;\n                firstViewCopy[firstViewCopyLength - counter] = value;\n                break;\n            }\n        }\n    }\n    return firstViewCopy.slice();\n}\nclass LocalIntegerValueBlock extends HexBlock(ValueBlock) {\n    setValueHex() {\n        if (this.valueHexView.length >= 4) {\n            this.warnings.push(\"Too big Integer for decoding, hex only\");\n            this.isHexOnly = true;\n            this._valueDec = 0;\n        }\n        else {\n            this.isHexOnly = false;\n            if (this.valueHexView.length > 0) {\n                this._valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilDecodeTC.call(this);\n            }\n        }\n    }\n    constructor({ value, ...parameters } = {}) {\n        super(parameters);\n        this._valueDec = 0;\n        if (parameters.valueHex) {\n            this.setValueHex();\n        }\n        if (value !== undefined) {\n            this.valueDec = value;\n        }\n    }\n    set valueDec(v) {\n        this._valueDec = v;\n        this.isHexOnly = false;\n        this.valueHexView = new Uint8Array(pvutils__WEBPACK_IMPORTED_MODULE_1__.utilEncodeTC(v));\n    }\n    get valueDec() {\n        return this._valueDec;\n    }\n    fromDER(inputBuffer, inputOffset, inputLength, expectedLength = 0) {\n        const offset = this.fromBER(inputBuffer, inputOffset, inputLength);\n        if (offset === -1)\n            return offset;\n        const view = this.valueHexView;\n        if ((view[0] === 0x00) && ((view[1] & 0x80) !== 0)) {\n            this.valueHexView = view.subarray(1);\n        }\n        else {\n            if (expectedLength !== 0) {\n                if (view.length < expectedLength) {\n                    if ((expectedLength - view.length) > 1)\n                        expectedLength = view.length + 1;\n                    this.valueHexView = view.subarray(expectedLength - view.length);\n                }\n            }\n        }\n        return offset;\n    }\n    toDER(sizeOnly = false) {\n        const view = this.valueHexView;\n        switch (true) {\n            case ((view[0] & 0x80) !== 0):\n                {\n                    const updatedView = new Uint8Array(this.valueHexView.length + 1);\n                    updatedView[0] = 0x00;\n                    updatedView.set(view, 1);\n                    this.valueHexView = updatedView;\n                }\n                break;\n            case ((view[0] === 0x00) && ((view[1] & 0x80) === 0)):\n                {\n                    this.valueHexView = this.valueHexView.subarray(1);\n                }\n                break;\n        }\n        return this.toBER(sizeOnly);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = super.fromBER(inputBuffer, inputOffset, inputLength);\n        if (resultOffset === -1) {\n            return resultOffset;\n        }\n        this.setValueHex();\n        return resultOffset;\n    }\n    toBER(sizeOnly) {\n        return sizeOnly\n            ? new ArrayBuffer(this.valueHexView.length)\n            : this.valueHexView.slice().buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec,\n        };\n    }\n    toString() {\n        const firstBit = (this.valueHexView.length * 8) - 1;\n        let digits = new Uint8Array((this.valueHexView.length * 8) / 3);\n        let bitNumber = 0;\n        let currentByte;\n        const asn1View = this.valueHexView;\n        let result = \"\";\n        let flag = false;\n        for (let byteNumber = (asn1View.byteLength - 1); byteNumber >= 0; byteNumber--) {\n            currentByte = asn1View[byteNumber];\n            for (let i = 0; i < 8; i++) {\n                if ((currentByte & 1) === 1) {\n                    switch (bitNumber) {\n                        case firstBit:\n                            digits = viewSub(power2(bitNumber), digits);\n                            result = \"-\";\n                            break;\n                        default:\n                            digits = viewAdd(digits, power2(bitNumber));\n                    }\n                }\n                bitNumber++;\n                currentByte >>= 1;\n            }\n        }\n        for (let i = 0; i < digits.length; i++) {\n            if (digits[i])\n                flag = true;\n            if (flag)\n                result += digitsString.charAt(digits[i]);\n        }\n        if (flag === false)\n            result += digitsString.charAt(0);\n        return result;\n    }\n}\n_a$p = LocalIntegerValueBlock;\nLocalIntegerValueBlock.NAME = \"IntegerValueBlock\";\n(() => {\n    Object.defineProperty(_a$p.prototype, \"valueHex\", {\n        set: function (v) {\n            this.valueHexView = new Uint8Array(v);\n            this.setValueHex();\n        },\n        get: function () {\n            return this.valueHexView.slice().buffer;\n        },\n    });\n})();\n\nvar _a$o;\nclass Integer extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, LocalIntegerValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 2;\n    }\n    toBigInt() {\n        assertBigInt();\n        return BigInt(this.valueBlock.toString());\n    }\n    static fromBigInt(value) {\n        assertBigInt();\n        const bigIntValue = BigInt(value);\n        const writer = new ViewWriter();\n        const hex = bigIntValue.toString(16).replace(/^-/, \"\");\n        const view = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromHex(hex));\n        if (bigIntValue < 0) {\n            const first = new Uint8Array(view.length + (view[0] & 0x80 ? 1 : 0));\n            first[0] |= 0x80;\n            const firstInt = BigInt(`0x${pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(first)}`);\n            const secondInt = firstInt + bigIntValue;\n            const second = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromHex(secondInt.toString(16)));\n            second[0] |= 0x80;\n            writer.write(second);\n        }\n        else {\n            if (view[0] & 0x80) {\n                writer.write(new Uint8Array([0]));\n            }\n            writer.write(view);\n        }\n        const res = new _a$o({ valueHex: writer.final() });\n        return res;\n    }\n    convertToDER() {\n        const integer = new _a$o({ valueHex: this.valueBlock.valueHexView });\n        integer.valueBlock.toDER();\n        return integer;\n    }\n    convertFromDER() {\n        return new _a$o({\n            valueHex: this.valueBlock.valueHexView[0] === 0\n                ? this.valueBlock.valueHexView.subarray(1)\n                : this.valueBlock.valueHexView,\n        });\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString()}`;\n    }\n}\n_a$o = Integer;\n(() => {\n    typeStore.Integer = _a$o;\n})();\nInteger.NAME = \"INTEGER\";\n\nvar _a$n;\nclass Enumerated extends Integer {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 10;\n    }\n}\n_a$n = Enumerated;\n(() => {\n    typeStore.Enumerated = _a$n;\n})();\nEnumerated.NAME = \"ENUMERATED\";\n\nclass LocalSidValueBlock extends HexBlock(ValueBlock) {\n    constructor({ valueDec = -1, isFirstSid = false, ...parameters } = {}) {\n        super(parameters);\n        this.valueDec = valueDec;\n        this.isFirstSid = isFirstSid;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (!inputLength) {\n            return inputOffset;\n        }\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.valueHexView = new Uint8Array(inputLength);\n        for (let i = 0; i < inputLength; i++) {\n            this.valueHexView[i] = intBuffer[i] & 0x7F;\n            this.blockLength++;\n            if ((intBuffer[i] & 0x80) === 0x00)\n                break;\n        }\n        const tempView = new Uint8Array(this.blockLength);\n        for (let i = 0; i < this.blockLength; i++) {\n            tempView[i] = this.valueHexView[i];\n        }\n        this.valueHexView = tempView;\n        if ((intBuffer[this.blockLength - 1] & 0x80) !== 0x00) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        if (this.valueHexView[0] === 0x00)\n            this.warnings.push(\"Needlessly long format of SID encoding\");\n        if (this.blockLength <= 8)\n            this.valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(this.valueHexView, 7);\n        else {\n            this.isHexOnly = true;\n            this.warnings.push(\"Too big SID for decoding, hex only\");\n        }\n        return (inputOffset + this.blockLength);\n    }\n    set valueBigInt(value) {\n        assertBigInt();\n        let bits = BigInt(value).toString(2);\n        while (bits.length % 7) {\n            bits = \"0\" + bits;\n        }\n        const bytes = new Uint8Array(bits.length / 7);\n        for (let i = 0; i < bytes.length; i++) {\n            bytes[i] = parseInt(bits.slice(i * 7, i * 7 + 7), 2) + (i + 1 < bytes.length ? 0x80 : 0);\n        }\n        this.fromBER(bytes.buffer, 0, bytes.length);\n    }\n    toBER(sizeOnly) {\n        if (this.isHexOnly) {\n            if (sizeOnly)\n                return (new ArrayBuffer(this.valueHexView.byteLength));\n            const curView = this.valueHexView;\n            const retView = new Uint8Array(this.blockLength);\n            for (let i = 0; i < (this.blockLength - 1); i++)\n                retView[i] = curView[i] | 0x80;\n            retView[this.blockLength - 1] = curView[this.blockLength - 1];\n            return retView.buffer;\n        }\n        const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.valueDec, 7);\n        if (encodedBuf.byteLength === 0) {\n            this.error = \"Error during encoding SID value\";\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(encodedBuf.byteLength);\n        if (!sizeOnly) {\n            const encodedView = new Uint8Array(encodedBuf);\n            const len = encodedBuf.byteLength - 1;\n            for (let i = 0; i < len; i++)\n                retView[i] = encodedView[i] | 0x80;\n            retView[len] = encodedView[len];\n        }\n        return retView;\n    }\n    toString() {\n        let result = \"\";\n        if (this.isHexOnly)\n            result = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView);\n        else {\n            if (this.isFirstSid) {\n                let sidValue = this.valueDec;\n                if (this.valueDec <= 39)\n                    result = \"0.\";\n                else {\n                    if (this.valueDec <= 79) {\n                        result = \"1.\";\n                        sidValue -= 40;\n                    }\n                    else {\n                        result = \"2.\";\n                        sidValue -= 80;\n                    }\n                }\n                result += sidValue.toString();\n            }\n            else\n                result = this.valueDec.toString();\n        }\n        return result;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec,\n            isFirstSid: this.isFirstSid,\n        };\n    }\n}\nLocalSidValueBlock.NAME = \"sidBlock\";\n\nclass LocalObjectIdentifierValueBlock extends ValueBlock {\n    constructor({ value = EMPTY_STRING, ...parameters } = {}) {\n        super(parameters);\n        this.value = [];\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = inputOffset;\n        while (inputLength > 0) {\n            const sidBlock = new LocalSidValueBlock();\n            resultOffset = sidBlock.fromBER(inputBuffer, resultOffset, inputLength);\n            if (resultOffset === -1) {\n                this.blockLength = 0;\n                this.error = sidBlock.error;\n                return resultOffset;\n            }\n            if (this.value.length === 0)\n                sidBlock.isFirstSid = true;\n            this.blockLength += sidBlock.blockLength;\n            inputLength -= sidBlock.blockLength;\n            this.value.push(sidBlock);\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly) {\n        const retBuffers = [];\n        for (let i = 0; i < this.value.length; i++) {\n            const valueBuf = this.value[i].toBER(sizeOnly);\n            if (valueBuf.byteLength === 0) {\n                this.error = this.value[i].error;\n                return EMPTY_BUFFER;\n            }\n            retBuffers.push(valueBuf);\n        }\n        return concat(retBuffers);\n    }\n    fromString(string) {\n        this.value = [];\n        let pos1 = 0;\n        let pos2 = 0;\n        let sid = \"\";\n        let flag = false;\n        do {\n            pos2 = string.indexOf(\".\", pos1);\n            if (pos2 === -1)\n                sid = string.substring(pos1);\n            else\n                sid = string.substring(pos1, pos2);\n            pos1 = pos2 + 1;\n            if (flag) {\n                const sidBlock = this.value[0];\n                let plus = 0;\n                switch (sidBlock.valueDec) {\n                    case 0:\n                        break;\n                    case 1:\n                        plus = 40;\n                        break;\n                    case 2:\n                        plus = 80;\n                        break;\n                    default:\n                        this.value = [];\n                        return;\n                }\n                const parsedSID = parseInt(sid, 10);\n                if (isNaN(parsedSID))\n                    return;\n                sidBlock.valueDec = parsedSID + plus;\n                flag = false;\n            }\n            else {\n                const sidBlock = new LocalSidValueBlock();\n                if (sid > Number.MAX_SAFE_INTEGER) {\n                    assertBigInt();\n                    const sidValue = BigInt(sid);\n                    sidBlock.valueBigInt = sidValue;\n                }\n                else {\n                    sidBlock.valueDec = parseInt(sid, 10);\n                    if (isNaN(sidBlock.valueDec))\n                        return;\n                }\n                if (!this.value.length) {\n                    sidBlock.isFirstSid = true;\n                    flag = true;\n                }\n                this.value.push(sidBlock);\n            }\n        } while (pos2 !== -1);\n    }\n    toString() {\n        let result = \"\";\n        let isHexOnly = false;\n        for (let i = 0; i < this.value.length; i++) {\n            isHexOnly = this.value[i].isHexOnly;\n            let sidStr = this.value[i].toString();\n            if (i !== 0)\n                result = `${result}.`;\n            if (isHexOnly) {\n                sidStr = `{${sidStr}}`;\n                if (this.value[i].isFirstSid)\n                    result = `2.{${sidStr} - 80}`;\n                else\n                    result += sidStr;\n            }\n            else\n                result += sidStr;\n        }\n        return result;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            value: this.toString(),\n            sidArray: [],\n        };\n        for (let i = 0; i < this.value.length; i++) {\n            object.sidArray.push(this.value[i].toJSON());\n        }\n        return object;\n    }\n}\nLocalObjectIdentifierValueBlock.NAME = \"ObjectIdentifierValueBlock\";\n\nvar _a$m;\nclass ObjectIdentifier extends BaseBlock {\n    getValue() {\n        return this.valueBlock.toString();\n    }\n    setValue(value) {\n        this.valueBlock.fromString(value);\n    }\n    constructor(parameters = {}) {\n        super(parameters, LocalObjectIdentifierValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 6;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString() || \"empty\"}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.getValue(),\n        };\n    }\n}\n_a$m = ObjectIdentifier;\n(() => {\n    typeStore.ObjectIdentifier = _a$m;\n})();\nObjectIdentifier.NAME = \"OBJECT IDENTIFIER\";\n\nclass LocalRelativeSidValueBlock extends HexBlock(LocalBaseBlock) {\n    constructor({ valueDec = 0, ...parameters } = {}) {\n        super(parameters);\n        this.valueDec = valueDec;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (inputLength === 0)\n            return inputOffset;\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength))\n            return -1;\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.valueHexView = new Uint8Array(inputLength);\n        for (let i = 0; i < inputLength; i++) {\n            this.valueHexView[i] = intBuffer[i] & 0x7F;\n            this.blockLength++;\n            if ((intBuffer[i] & 0x80) === 0x00)\n                break;\n        }\n        const tempView = new Uint8Array(this.blockLength);\n        for (let i = 0; i < this.blockLength; i++)\n            tempView[i] = this.valueHexView[i];\n        this.valueHexView = tempView;\n        if ((intBuffer[this.blockLength - 1] & 0x80) !== 0x00) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        if (this.valueHexView[0] === 0x00)\n            this.warnings.push(\"Needlessly long format of SID encoding\");\n        if (this.blockLength <= 8)\n            this.valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(this.valueHexView, 7);\n        else {\n            this.isHexOnly = true;\n            this.warnings.push(\"Too big SID for decoding, hex only\");\n        }\n        return (inputOffset + this.blockLength);\n    }\n    toBER(sizeOnly) {\n        if (this.isHexOnly) {\n            if (sizeOnly)\n                return (new ArrayBuffer(this.valueHexView.byteLength));\n            const curView = this.valueHexView;\n            const retView = new Uint8Array(this.blockLength);\n            for (let i = 0; i < (this.blockLength - 1); i++)\n                retView[i] = curView[i] | 0x80;\n            retView[this.blockLength - 1] = curView[this.blockLength - 1];\n            return retView.buffer;\n        }\n        const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.valueDec, 7);\n        if (encodedBuf.byteLength === 0) {\n            this.error = \"Error during encoding SID value\";\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(encodedBuf.byteLength);\n        if (!sizeOnly) {\n            const encodedView = new Uint8Array(encodedBuf);\n            const len = encodedBuf.byteLength - 1;\n            for (let i = 0; i < len; i++)\n                retView[i] = encodedView[i] | 0x80;\n            retView[len] = encodedView[len];\n        }\n        return retView.buffer;\n    }\n    toString() {\n        let result = \"\";\n        if (this.isHexOnly)\n            result = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView);\n        else {\n            result = this.valueDec.toString();\n        }\n        return result;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec,\n        };\n    }\n}\nLocalRelativeSidValueBlock.NAME = \"relativeSidBlock\";\n\nclass LocalRelativeObjectIdentifierValueBlock extends ValueBlock {\n    constructor({ value = EMPTY_STRING, ...parameters } = {}) {\n        super(parameters);\n        this.value = [];\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = inputOffset;\n        while (inputLength > 0) {\n            const sidBlock = new LocalRelativeSidValueBlock();\n            resultOffset = sidBlock.fromBER(inputBuffer, resultOffset, inputLength);\n            if (resultOffset === -1) {\n                this.blockLength = 0;\n                this.error = sidBlock.error;\n                return resultOffset;\n            }\n            this.blockLength += sidBlock.blockLength;\n            inputLength -= sidBlock.blockLength;\n            this.value.push(sidBlock);\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly, _writer) {\n        const retBuffers = [];\n        for (let i = 0; i < this.value.length; i++) {\n            const valueBuf = this.value[i].toBER(sizeOnly);\n            if (valueBuf.byteLength === 0) {\n                this.error = this.value[i].error;\n                return EMPTY_BUFFER;\n            }\n            retBuffers.push(valueBuf);\n        }\n        return concat(retBuffers);\n    }\n    fromString(string) {\n        this.value = [];\n        let pos1 = 0;\n        let pos2 = 0;\n        let sid = \"\";\n        do {\n            pos2 = string.indexOf(\".\", pos1);\n            if (pos2 === -1)\n                sid = string.substring(pos1);\n            else\n                sid = string.substring(pos1, pos2);\n            pos1 = pos2 + 1;\n            const sidBlock = new LocalRelativeSidValueBlock();\n            sidBlock.valueDec = parseInt(sid, 10);\n            if (isNaN(sidBlock.valueDec))\n                return true;\n            this.value.push(sidBlock);\n        } while (pos2 !== -1);\n        return true;\n    }\n    toString() {\n        let result = \"\";\n        let isHexOnly = false;\n        for (let i = 0; i < this.value.length; i++) {\n            isHexOnly = this.value[i].isHexOnly;\n            let sidStr = this.value[i].toString();\n            if (i !== 0)\n                result = `${result}.`;\n            if (isHexOnly) {\n                sidStr = `{${sidStr}}`;\n                result += sidStr;\n            }\n            else\n                result += sidStr;\n        }\n        return result;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            value: this.toString(),\n            sidArray: [],\n        };\n        for (let i = 0; i < this.value.length; i++)\n            object.sidArray.push(this.value[i].toJSON());\n        return object;\n    }\n}\nLocalRelativeObjectIdentifierValueBlock.NAME = \"RelativeObjectIdentifierValueBlock\";\n\nvar _a$l;\nclass RelativeObjectIdentifier extends BaseBlock {\n    getValue() {\n        return this.valueBlock.toString();\n    }\n    setValue(value) {\n        this.valueBlock.fromString(value);\n    }\n    constructor(parameters = {}) {\n        super(parameters, LocalRelativeObjectIdentifierValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 13;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString() || \"empty\"}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.getValue(),\n        };\n    }\n}\n_a$l = RelativeObjectIdentifier;\n(() => {\n    typeStore.RelativeObjectIdentifier = _a$l;\n})();\nRelativeObjectIdentifier.NAME = \"RelativeObjectIdentifier\";\n\nvar _a$k;\nclass Sequence extends Constructed {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 16;\n    }\n}\n_a$k = Sequence;\n(() => {\n    typeStore.Sequence = _a$k;\n})();\nSequence.NAME = \"SEQUENCE\";\n\nvar _a$j;\nclass Set extends Constructed {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 17;\n    }\n}\n_a$j = Set;\n(() => {\n    typeStore.Set = _a$j;\n})();\nSet.NAME = \"SET\";\n\nclass LocalStringValueBlock extends HexBlock(ValueBlock) {\n    constructor({ ...parameters } = {}) {\n        super(parameters);\n        this.isHexOnly = true;\n        this.value = EMPTY_STRING;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.value,\n        };\n    }\n}\nLocalStringValueBlock.NAME = \"StringValueBlock\";\n\nclass LocalSimpleStringValueBlock extends LocalStringValueBlock {\n}\nLocalSimpleStringValueBlock.NAME = \"SimpleStringValueBlock\";\n\nclass LocalSimpleStringBlock extends BaseStringBlock {\n    constructor({ ...parameters } = {}) {\n        super(parameters, LocalSimpleStringValueBlock);\n    }\n    fromBuffer(inputBuffer) {\n        this.valueBlock.value = String.fromCharCode.apply(null, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer));\n    }\n    fromString(inputString) {\n        const strLen = inputString.length;\n        const view = this.valueBlock.valueHexView = new Uint8Array(strLen);\n        for (let i = 0; i < strLen; i++)\n            view[i] = inputString.charCodeAt(i);\n        this.valueBlock.value = inputString;\n    }\n}\nLocalSimpleStringBlock.NAME = \"SIMPLE STRING\";\n\nclass LocalUtf8StringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        this.valueBlock.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        try {\n            this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf8String(inputBuffer);\n        }\n        catch (ex) {\n            this.warnings.push(`Error during \"decodeURIComponent\": ${ex}, using raw string`);\n            this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBinary(inputBuffer);\n        }\n    }\n    fromString(inputString) {\n        this.valueBlock.valueHexView = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf8String(inputString));\n        this.valueBlock.value = inputString;\n    }\n}\nLocalUtf8StringValueBlock.NAME = \"Utf8StringValueBlock\";\n\nvar _a$i;\nclass Utf8String extends LocalUtf8StringValueBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 12;\n    }\n}\n_a$i = Utf8String;\n(() => {\n    typeStore.Utf8String = _a$i;\n})();\nUtf8String.NAME = \"UTF8String\";\n\nclass LocalBmpStringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf16String(inputBuffer);\n        this.valueBlock.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n    }\n    fromString(inputString) {\n        this.valueBlock.value = inputString;\n        this.valueBlock.valueHexView = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf16String(inputString));\n    }\n}\nLocalBmpStringValueBlock.NAME = \"BmpStringValueBlock\";\n\nvar _a$h;\nclass BmpString extends LocalBmpStringValueBlock {\n    constructor({ ...parameters } = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 30;\n    }\n}\n_a$h = BmpString;\n(() => {\n    typeStore.BmpString = _a$h;\n})();\nBmpString.NAME = \"BMPString\";\n\nclass LocalUniversalStringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        const copyBuffer = ArrayBuffer.isView(inputBuffer) ? inputBuffer.slice().buffer : inputBuffer.slice(0);\n        const valueView = new Uint8Array(copyBuffer);\n        for (let i = 0; i < valueView.length; i += 4) {\n            valueView[i] = valueView[i + 3];\n            valueView[i + 1] = valueView[i + 2];\n            valueView[i + 2] = 0x00;\n            valueView[i + 3] = 0x00;\n        }\n        this.valueBlock.value = String.fromCharCode.apply(null, new Uint32Array(copyBuffer));\n    }\n    fromString(inputString) {\n        const strLength = inputString.length;\n        const valueHexView = this.valueBlock.valueHexView = new Uint8Array(strLength * 4);\n        for (let i = 0; i < strLength; i++) {\n            const codeBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(inputString.charCodeAt(i), 8);\n            const codeView = new Uint8Array(codeBuf);\n            if (codeView.length > 4)\n                continue;\n            const dif = 4 - codeView.length;\n            for (let j = (codeView.length - 1); j >= 0; j--)\n                valueHexView[i * 4 + j + dif] = codeView[j];\n        }\n        this.valueBlock.value = inputString;\n    }\n}\nLocalUniversalStringValueBlock.NAME = \"UniversalStringValueBlock\";\n\nvar _a$g;\nclass UniversalString extends LocalUniversalStringValueBlock {\n    constructor({ ...parameters } = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 28;\n    }\n}\n_a$g = UniversalString;\n(() => {\n    typeStore.UniversalString = _a$g;\n})();\nUniversalString.NAME = \"UniversalString\";\n\nvar _a$f;\nclass NumericString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 18;\n    }\n}\n_a$f = NumericString;\n(() => {\n    typeStore.NumericString = _a$f;\n})();\nNumericString.NAME = \"NumericString\";\n\nvar _a$e;\nclass PrintableString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 19;\n    }\n}\n_a$e = PrintableString;\n(() => {\n    typeStore.PrintableString = _a$e;\n})();\nPrintableString.NAME = \"PrintableString\";\n\nvar _a$d;\nclass TeletexString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 20;\n    }\n}\n_a$d = TeletexString;\n(() => {\n    typeStore.TeletexString = _a$d;\n})();\nTeletexString.NAME = \"TeletexString\";\n\nvar _a$c;\nclass VideotexString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 21;\n    }\n}\n_a$c = VideotexString;\n(() => {\n    typeStore.VideotexString = _a$c;\n})();\nVideotexString.NAME = \"VideotexString\";\n\nvar _a$b;\nclass IA5String extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 22;\n    }\n}\n_a$b = IA5String;\n(() => {\n    typeStore.IA5String = _a$b;\n})();\nIA5String.NAME = \"IA5String\";\n\nvar _a$a;\nclass GraphicString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 25;\n    }\n}\n_a$a = GraphicString;\n(() => {\n    typeStore.GraphicString = _a$a;\n})();\nGraphicString.NAME = \"GraphicString\";\n\nvar _a$9;\nclass VisibleString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 26;\n    }\n}\n_a$9 = VisibleString;\n(() => {\n    typeStore.VisibleString = _a$9;\n})();\nVisibleString.NAME = \"VisibleString\";\n\nvar _a$8;\nclass GeneralString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 27;\n    }\n}\n_a$8 = GeneralString;\n(() => {\n    typeStore.GeneralString = _a$8;\n})();\nGeneralString.NAME = \"GeneralString\";\n\nvar _a$7;\nclass CharacterString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 29;\n    }\n}\n_a$7 = CharacterString;\n(() => {\n    typeStore.CharacterString = _a$7;\n})();\nCharacterString.NAME = \"CharacterString\";\n\nvar _a$6;\nclass UTCTime extends VisibleString {\n    constructor({ value, valueDate, ...parameters } = {}) {\n        super(parameters);\n        this.year = 0;\n        this.month = 0;\n        this.day = 0;\n        this.hour = 0;\n        this.minute = 0;\n        this.second = 0;\n        if (value) {\n            this.fromString(value);\n            this.valueBlock.valueHexView = new Uint8Array(value.length);\n            for (let i = 0; i < value.length; i++)\n                this.valueBlock.valueHexView[i] = value.charCodeAt(i);\n        }\n        if (valueDate) {\n            this.fromDate(valueDate);\n            this.valueBlock.valueHexView = new Uint8Array(this.toBuffer());\n        }\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 23;\n    }\n    fromBuffer(inputBuffer) {\n        this.fromString(String.fromCharCode.apply(null, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer)));\n    }\n    toBuffer() {\n        const str = this.toString();\n        const buffer = new ArrayBuffer(str.length);\n        const view = new Uint8Array(buffer);\n        for (let i = 0; i < str.length; i++)\n            view[i] = str.charCodeAt(i);\n        return buffer;\n    }\n    fromDate(inputDate) {\n        this.year = inputDate.getUTCFullYear();\n        this.month = inputDate.getUTCMonth() + 1;\n        this.day = inputDate.getUTCDate();\n        this.hour = inputDate.getUTCHours();\n        this.minute = inputDate.getUTCMinutes();\n        this.second = inputDate.getUTCSeconds();\n    }\n    toDate() {\n        return (new Date(Date.UTC(this.year, this.month - 1, this.day, this.hour, this.minute, this.second)));\n    }\n    fromString(inputString) {\n        const parser = /(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})Z/ig;\n        const parserArray = parser.exec(inputString);\n        if (parserArray === null) {\n            this.error = \"Wrong input string for conversion\";\n            return;\n        }\n        const year = parseInt(parserArray[1], 10);\n        if (year >= 50)\n            this.year = 1900 + year;\n        else\n            this.year = 2000 + year;\n        this.month = parseInt(parserArray[2], 10);\n        this.day = parseInt(parserArray[3], 10);\n        this.hour = parseInt(parserArray[4], 10);\n        this.minute = parseInt(parserArray[5], 10);\n        this.second = parseInt(parserArray[6], 10);\n    }\n    toString(encoding = \"iso\") {\n        if (encoding === \"iso\") {\n            const outputArray = new Array(7);\n            outputArray[0] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(((this.year < 2000) ? (this.year - 1900) : (this.year - 2000)), 2);\n            outputArray[1] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.month, 2);\n            outputArray[2] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.day, 2);\n            outputArray[3] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.hour, 2);\n            outputArray[4] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.minute, 2);\n            outputArray[5] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.second, 2);\n            outputArray[6] = \"Z\";\n            return outputArray.join(\"\");\n        }\n        return super.toString(encoding);\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.toDate().toISOString()}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            year: this.year,\n            month: this.month,\n            day: this.day,\n            hour: this.hour,\n            minute: this.minute,\n            second: this.second,\n        };\n    }\n}\n_a$6 = UTCTime;\n(() => {\n    typeStore.UTCTime = _a$6;\n})();\nUTCTime.NAME = \"UTCTime\";\n\nvar _a$5;\nclass GeneralizedTime extends UTCTime {\n    constructor(parameters = {}) {\n        var _b;\n        super(parameters);\n        (_b = this.millisecond) !== null && _b !== void 0 ? _b : (this.millisecond = 0);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 24;\n    }\n    fromDate(inputDate) {\n        super.fromDate(inputDate);\n        this.millisecond = inputDate.getUTCMilliseconds();\n    }\n    toDate() {\n        const utcDate = Date.UTC(this.year, this.month - 1, this.day, this.hour, this.minute, this.second, this.millisecond);\n        return (new Date(utcDate));\n    }\n    fromString(inputString) {\n        let isUTC = false;\n        let timeString = \"\";\n        let dateTimeString = \"\";\n        let fractionPart = 0;\n        let parser;\n        let hourDifference = 0;\n        let minuteDifference = 0;\n        if (inputString[inputString.length - 1] === \"Z\") {\n            timeString = inputString.substring(0, inputString.length - 1);\n            isUTC = true;\n        }\n        else {\n            const number = new Number(inputString[inputString.length - 1]);\n            if (isNaN(number.valueOf()))\n                throw new Error(\"Wrong input string for conversion\");\n            timeString = inputString;\n        }\n        if (isUTC) {\n            if (timeString.indexOf(\"+\") !== -1)\n                throw new Error(\"Wrong input string for conversion\");\n            if (timeString.indexOf(\"-\") !== -1)\n                throw new Error(\"Wrong input string for conversion\");\n        }\n        else {\n            let multiplier = 1;\n            let differencePosition = timeString.indexOf(\"+\");\n            let differenceString = \"\";\n            if (differencePosition === -1) {\n                differencePosition = timeString.indexOf(\"-\");\n                multiplier = -1;\n            }\n            if (differencePosition !== -1) {\n                differenceString = timeString.substring(differencePosition + 1);\n                timeString = timeString.substring(0, differencePosition);\n                if ((differenceString.length !== 2) && (differenceString.length !== 4))\n                    throw new Error(\"Wrong input string for conversion\");\n                let number = parseInt(differenceString.substring(0, 2), 10);\n                if (isNaN(number.valueOf()))\n                    throw new Error(\"Wrong input string for conversion\");\n                hourDifference = multiplier * number;\n                if (differenceString.length === 4) {\n                    number = parseInt(differenceString.substring(2, 4), 10);\n                    if (isNaN(number.valueOf()))\n                        throw new Error(\"Wrong input string for conversion\");\n                    minuteDifference = multiplier * number;\n                }\n            }\n        }\n        let fractionPointPosition = timeString.indexOf(\".\");\n        if (fractionPointPosition === -1)\n            fractionPointPosition = timeString.indexOf(\",\");\n        if (fractionPointPosition !== -1) {\n            const fractionPartCheck = new Number(`0${timeString.substring(fractionPointPosition)}`);\n            if (isNaN(fractionPartCheck.valueOf()))\n                throw new Error(\"Wrong input string for conversion\");\n            fractionPart = fractionPartCheck.valueOf();\n            dateTimeString = timeString.substring(0, fractionPointPosition);\n        }\n        else\n            dateTimeString = timeString;\n        switch (true) {\n            case (dateTimeString.length === 8):\n                parser = /(\\d{4})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1)\n                    throw new Error(\"Wrong input string for conversion\");\n                break;\n            case (dateTimeString.length === 10):\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    let fractionResult = 60 * fractionPart;\n                    this.minute = Math.floor(fractionResult);\n                    fractionResult = 60 * (fractionResult - this.minute);\n                    this.second = Math.floor(fractionResult);\n                    fractionResult = 1000 * (fractionResult - this.second);\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            case (dateTimeString.length === 12):\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    let fractionResult = 60 * fractionPart;\n                    this.second = Math.floor(fractionResult);\n                    fractionResult = 1000 * (fractionResult - this.second);\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            case (dateTimeString.length === 14):\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    const fractionResult = 1000 * fractionPart;\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            default:\n                throw new Error(\"Wrong input string for conversion\");\n        }\n        const parserArray = parser.exec(dateTimeString);\n        if (parserArray === null)\n            throw new Error(\"Wrong input string for conversion\");\n        for (let j = 1; j < parserArray.length; j++) {\n            switch (j) {\n                case 1:\n                    this.year = parseInt(parserArray[j], 10);\n                    break;\n                case 2:\n                    this.month = parseInt(parserArray[j], 10);\n                    break;\n                case 3:\n                    this.day = parseInt(parserArray[j], 10);\n                    break;\n                case 4:\n                    this.hour = parseInt(parserArray[j], 10) + hourDifference;\n                    break;\n                case 5:\n                    this.minute = parseInt(parserArray[j], 10) + minuteDifference;\n                    break;\n                case 6:\n                    this.second = parseInt(parserArray[j], 10);\n                    break;\n                default:\n                    throw new Error(\"Wrong input string for conversion\");\n            }\n        }\n        if (isUTC === false) {\n            const tempDate = new Date(this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n            this.year = tempDate.getUTCFullYear();\n            this.month = tempDate.getUTCMonth();\n            this.day = tempDate.getUTCDay();\n            this.hour = tempDate.getUTCHours();\n            this.minute = tempDate.getUTCMinutes();\n            this.second = tempDate.getUTCSeconds();\n            this.millisecond = tempDate.getUTCMilliseconds();\n        }\n    }\n    toString(encoding = \"iso\") {\n        if (encoding === \"iso\") {\n            const outputArray = [];\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.year, 4));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.month, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.day, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.hour, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.minute, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.second, 2));\n            if (this.millisecond !== 0) {\n                outputArray.push(\".\");\n                outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.millisecond, 3));\n            }\n            outputArray.push(\"Z\");\n            return outputArray.join(\"\");\n        }\n        return super.toString(encoding);\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            millisecond: this.millisecond,\n        };\n    }\n}\n_a$5 = GeneralizedTime;\n(() => {\n    typeStore.GeneralizedTime = _a$5;\n})();\nGeneralizedTime.NAME = \"GeneralizedTime\";\n\nvar _a$4;\nclass DATE extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 31;\n    }\n}\n_a$4 = DATE;\n(() => {\n    typeStore.DATE = _a$4;\n})();\nDATE.NAME = \"DATE\";\n\nvar _a$3;\nclass TimeOfDay extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 32;\n    }\n}\n_a$3 = TimeOfDay;\n(() => {\n    typeStore.TimeOfDay = _a$3;\n})();\nTimeOfDay.NAME = \"TimeOfDay\";\n\nvar _a$2;\nclass DateTime extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 33;\n    }\n}\n_a$2 = DateTime;\n(() => {\n    typeStore.DateTime = _a$2;\n})();\nDateTime.NAME = \"DateTime\";\n\nvar _a$1;\nclass Duration extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 34;\n    }\n}\n_a$1 = Duration;\n(() => {\n    typeStore.Duration = _a$1;\n})();\nDuration.NAME = \"Duration\";\n\nvar _a;\nclass TIME extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 14;\n    }\n}\n_a = TIME;\n(() => {\n    typeStore.TIME = _a;\n})();\nTIME.NAME = \"TIME\";\n\nclass Any {\n    constructor({ name = EMPTY_STRING, optional = false } = {}) {\n        this.name = name;\n        this.optional = optional;\n    }\n}\n\nclass Choice extends Any {\n    constructor({ value = [], ...parameters } = {}) {\n        super(parameters);\n        this.value = value;\n    }\n}\n\nclass Repeated extends Any {\n    constructor({ value = new Any(), local = false, ...parameters } = {}) {\n        super(parameters);\n        this.value = value;\n        this.local = local;\n    }\n}\n\nclass RawData {\n    get data() {\n        return this.dataView.slice().buffer;\n    }\n    set data(value) {\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(value);\n    }\n    constructor({ data = EMPTY_VIEW } = {}) {\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const endLength = inputOffset + inputLength;\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer).subarray(inputOffset, endLength);\n        return endLength;\n    }\n    toBER(_sizeOnly) {\n        return this.dataView.slice().buffer;\n    }\n}\n\nfunction compareSchema(root, inputData, inputSchema) {\n    if (inputSchema instanceof Choice) {\n        for (const element of inputSchema.value) {\n            const result = compareSchema(root, inputData, element);\n            if (result.verified) {\n                return {\n                    verified: true,\n                    result: root,\n                };\n            }\n        }\n        {\n            const _result = {\n                verified: false,\n                result: { error: \"Wrong values for Choice type\" },\n            };\n            if (inputSchema.hasOwnProperty(NAME))\n                _result.name = inputSchema.name;\n            return _result;\n        }\n    }\n    if (inputSchema instanceof Any) {\n        if (inputSchema.hasOwnProperty(NAME))\n            root[inputSchema.name] = inputData;\n        return {\n            verified: true,\n            result: root,\n        };\n    }\n    if ((root instanceof Object) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong root object\" },\n        };\n    }\n    if ((inputData instanceof Object) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 data\" },\n        };\n    }\n    if ((inputSchema instanceof Object) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if ((ID_BLOCK in inputSchema) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if ((FROM_BER in inputSchema.idBlock) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if ((TO_BER in inputSchema.idBlock) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    const encodedId = inputSchema.idBlock.toBER(false);\n    if (encodedId.byteLength === 0) {\n        return {\n            verified: false,\n            result: { error: \"Error encoding idBlock for ASN.1 schema\" },\n        };\n    }\n    const decodedOffset = inputSchema.idBlock.fromBER(encodedId, 0, encodedId.byteLength);\n    if (decodedOffset === -1) {\n        return {\n            verified: false,\n            result: { error: \"Error decoding idBlock for ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(TAG_CLASS) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.tagClass !== inputData.idBlock.tagClass) {\n        return {\n            verified: false,\n            result: root,\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(TAG_NUMBER) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.tagNumber !== inputData.idBlock.tagNumber) {\n        return {\n            verified: false,\n            result: root,\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(IS_CONSTRUCTED) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.isConstructed !== inputData.idBlock.isConstructed) {\n        return {\n            verified: false,\n            result: root,\n        };\n    }\n    if (!(IS_HEX_ONLY in inputSchema.idBlock)) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.isHexOnly !== inputData.idBlock.isHexOnly) {\n        return {\n            verified: false,\n            result: root,\n        };\n    }\n    if (inputSchema.idBlock.isHexOnly) {\n        if ((VALUE_HEX_VIEW in inputSchema.idBlock) === false) {\n            return {\n                verified: false,\n                result: { error: \"Wrong ASN.1 schema\" },\n            };\n        }\n        const schemaView = inputSchema.idBlock.valueHexView;\n        const asn1View = inputData.idBlock.valueHexView;\n        if (schemaView.length !== asn1View.length) {\n            return {\n                verified: false,\n                result: root,\n            };\n        }\n        for (let i = 0; i < schemaView.length; i++) {\n            if (schemaView[i] !== asn1View[1]) {\n                return {\n                    verified: false,\n                    result: root,\n                };\n            }\n        }\n    }\n    if (inputSchema.name) {\n        inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n        if (inputSchema.name)\n            root[inputSchema.name] = inputData;\n    }\n    if (inputSchema instanceof typeStore.Constructed) {\n        let admission = 0;\n        let result = {\n            verified: false,\n            result: { error: \"Unknown error\" },\n        };\n        let maxLength = inputSchema.valueBlock.value.length;\n        if (maxLength > 0) {\n            if (inputSchema.valueBlock.value[0] instanceof Repeated) {\n                maxLength = inputData.valueBlock.value.length;\n            }\n        }\n        if (maxLength === 0) {\n            return {\n                verified: true,\n                result: root,\n            };\n        }\n        if ((inputData.valueBlock.value.length === 0)\n            && (inputSchema.valueBlock.value.length !== 0)) {\n            let _optional = true;\n            for (let i = 0; i < inputSchema.valueBlock.value.length; i++)\n                _optional = _optional && (inputSchema.valueBlock.value[i].optional || false);\n            if (_optional) {\n                return {\n                    verified: true,\n                    result: root,\n                };\n            }\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name)\n                    delete root[inputSchema.name];\n            }\n            root.error = \"Inconsistent object length\";\n            return {\n                verified: false,\n                result: root,\n            };\n        }\n        for (let i = 0; i < maxLength; i++) {\n            if ((i - admission) >= inputData.valueBlock.value.length) {\n                if (inputSchema.valueBlock.value[i].optional === false) {\n                    const _result = {\n                        verified: false,\n                        result: root,\n                    };\n                    root.error = \"Inconsistent length between ASN.1 data and schema\";\n                    if (inputSchema.name) {\n                        inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                        if (inputSchema.name) {\n                            delete root[inputSchema.name];\n                            _result.name = inputSchema.name;\n                        }\n                    }\n                    return _result;\n                }\n            }\n            else {\n                if (inputSchema.valueBlock.value[0] instanceof Repeated) {\n                    result = compareSchema(root, inputData.valueBlock.value[i], inputSchema.valueBlock.value[0].value);\n                    if (result.verified === false) {\n                        if (inputSchema.valueBlock.value[0].optional)\n                            admission++;\n                        else {\n                            if (inputSchema.name) {\n                                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                                if (inputSchema.name)\n                                    delete root[inputSchema.name];\n                            }\n                            return result;\n                        }\n                    }\n                    if ((NAME in inputSchema.valueBlock.value[0]) && (inputSchema.valueBlock.value[0].name.length > 0)) {\n                        let arrayRoot = {};\n                        if ((LOCAL in inputSchema.valueBlock.value[0]) && (inputSchema.valueBlock.value[0].local))\n                            arrayRoot = inputData;\n                        else\n                            arrayRoot = root;\n                        if (typeof arrayRoot[inputSchema.valueBlock.value[0].name] === \"undefined\")\n                            arrayRoot[inputSchema.valueBlock.value[0].name] = [];\n                        arrayRoot[inputSchema.valueBlock.value[0].name].push(inputData.valueBlock.value[i]);\n                    }\n                }\n                else {\n                    result = compareSchema(root, inputData.valueBlock.value[i - admission], inputSchema.valueBlock.value[i]);\n                    if (result.verified === false) {\n                        if (inputSchema.valueBlock.value[i].optional)\n                            admission++;\n                        else {\n                            if (inputSchema.name) {\n                                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                                if (inputSchema.name)\n                                    delete root[inputSchema.name];\n                            }\n                            return result;\n                        }\n                    }\n                }\n            }\n        }\n        if (result.verified === false) {\n            const _result = {\n                verified: false,\n                result: root,\n            };\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name) {\n                    delete root[inputSchema.name];\n                    _result.name = inputSchema.name;\n                }\n            }\n            return _result;\n        }\n        return {\n            verified: true,\n            result: root,\n        };\n    }\n    if (inputSchema.primitiveSchema\n        && (VALUE_HEX_VIEW in inputData.valueBlock)) {\n        const asn1 = localFromBER(inputData.valueBlock.valueHexView);\n        if (asn1.offset === -1) {\n            const _result = {\n                verified: false,\n                result: asn1.result,\n            };\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name) {\n                    delete root[inputSchema.name];\n                    _result.name = inputSchema.name;\n                }\n            }\n            return _result;\n        }\n        return compareSchema(root, asn1.result, inputSchema.primitiveSchema);\n    }\n    return {\n        verified: true,\n        result: root,\n    };\n}\nfunction verifySchema(inputBuffer, inputSchema) {\n    if ((inputSchema instanceof Object) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema type\" },\n        };\n    }\n    const asn1 = localFromBER(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer));\n    if (asn1.offset === -1) {\n        return {\n            verified: false,\n            result: asn1.result,\n        };\n    }\n    return compareSchema(asn1.result, asn1.result, inputSchema);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYXNuMWpzL2J1aWxkL2luZGV4LmVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFdUM7QUFDSjs7QUFFbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixvQkFBb0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isb0JBQW9CO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCw0REFBK0I7QUFDckY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsOENBQWlCO0FBQy9DO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHdGQUF3RixJQUFJO0FBQzlHO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyw0REFBK0I7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsOENBQWlCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGtCQUFrQixlQUFlLElBQUk7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiw0REFBK0I7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsK0NBQWtCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsZ0JBQWdCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QiwwQkFBMEI7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDREQUErQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLCtCQUErQjtBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixXQUFXO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGlEQUFvQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGtCQUFrQixnQkFBZ0IsSUFBSTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiw0REFBK0I7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLGlEQUFvQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQiwrQ0FBa0I7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsMkJBQTJCO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxrQkFBa0Isd0VBQXdFLElBQUk7QUFDOUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDhDQUFpQjtBQUNoQztBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsOENBQWlCO0FBQ3ZDLGtCQUFrQixNQUFNLElBQUksTUFBTTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsa0RBQXFCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isc0NBQXNDLElBQUk7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix1QkFBdUIsS0FBSyxzQkFBc0I7QUFDcEU7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLGtDQUFrQyxJQUFJO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsNERBQStCO0FBQ3ZEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHNEQUFzRCxJQUFJO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsNERBQStCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix1QkFBdUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRFQUE0RSxFQUFFO0FBQzlFO0FBQ0E7QUFDQSxrQkFBa0IsdUJBQXVCO0FBQ3pDO0FBQ0E7QUFDQSxpQkFBaUIsV0FBVyxLQUFLLGtCQUFrQjtBQUNuRCxpQkFBaUIsV0FBVztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixzQkFBc0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsdUJBQXVCLElBQUk7QUFDN0M7QUFDQTtBQUNBLGdDQUFnQyw0REFBK0I7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDREQUErQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaURBQW9CO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHVCQUF1QixJQUFJLGNBQWM7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBLGtCQUFrQix1Q0FBdUMsSUFBSTtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix1QkFBdUI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGtCQUFrQixZQUFZLGVBQWUsa0JBQWtCLElBQUk7QUFDbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw4Q0FBaUI7QUFDdkMsa0JBQWtCLE1BQU0sSUFBSSxNQUFNO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDREQUErQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0Esa0JBQWtCLHVEQUF1RCxJQUFJO0FBQzdFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDREQUErQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxrQkFBa0IsWUFBWSxlQUFlLGtCQUFrQixJQUFJO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLE1BQU0sSUFBSSxNQUFNO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsUUFBUTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxtREFBc0I7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLG1EQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxRQUFRO0FBQzdDO0FBQ0E7QUFDQSw4Q0FBOEMsUUFBUTtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLG1EQUFzQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsUUFBUTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1RUFBdUUsUUFBUTtBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsaURBQW9CO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix1QkFBdUIsSUFBSTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsaURBQW9CO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCxpQkFBaUI7QUFDMUU7QUFDQSw0QkFBNEIsT0FBTztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLG1CQUFtQjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMLENBQUM7O0FBRUQ7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyw4Q0FBaUI7QUFDckQ7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLDhDQUFpQixjQUFjO0FBQ3hFO0FBQ0EsMkJBQTJCLDREQUErQixjQUFjLDhDQUFpQjtBQUN6RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsMEJBQTBCO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyx3Q0FBd0M7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0Esa0JBQWtCLHVCQUF1QixJQUFJLDJCQUEyQjtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBLGtCQUFrQixtREFBbUQsSUFBSTtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDREQUErQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGlCQUFpQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isc0JBQXNCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGlEQUFvQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixrQkFBa0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsNEJBQTRCO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLCtDQUFrQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLFNBQVM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiw4Q0FBaUI7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxrQkFBa0Isc0NBQXNDLElBQUk7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix1QkFBdUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix1QkFBdUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLE9BQU87QUFDbkM7QUFDQSwyQkFBMkIsRUFBRSxRQUFRO0FBQ3JDO0FBQ0EsaUNBQWlDLEVBQUUsUUFBUSxLQUFLO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsdUJBQXVCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsdUJBQXVCLElBQUksc0NBQXNDO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQSxrQkFBa0IsOEJBQThCLElBQUk7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDREQUErQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixpQkFBaUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHNCQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsaURBQW9CO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw0QkFBNEI7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsK0NBQWtCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsU0FBUztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDhDQUFpQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGtCQUFrQixzQ0FBc0MsSUFBSTtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsdUJBQXVCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix1QkFBdUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLE9BQU87QUFDbkM7QUFDQSwyQkFBMkIsRUFBRSxRQUFRO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHVCQUF1QjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix1QkFBdUIsSUFBSSxzQ0FBc0M7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0Esa0JBQWtCLGdCQUFnQixJQUFJO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxrQkFBa0IsZ0JBQWdCLElBQUk7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLDREQUErQjtBQUMvRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixZQUFZO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHVDQUF1Qyw0REFBK0I7QUFDdEU7QUFDQSxvQ0FBb0MsOENBQWlCO0FBQ3JEO0FBQ0E7QUFDQSxxRUFBcUUsR0FBRztBQUN4RSxvQ0FBb0MsOENBQWlCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCw4Q0FBaUI7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQSxnQ0FBZ0MsOENBQWlCO0FBQ2pELHVDQUF1Qyw0REFBK0I7QUFDdEU7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELDhDQUFpQjtBQUN2RTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGtCQUFrQixnQkFBZ0IsSUFBSTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHNCQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixlQUFlO0FBQ3ZDLDRCQUE0QiwrQ0FBa0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsUUFBUTtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGtCQUFrQixnQkFBZ0IsSUFBSTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQSxrQkFBa0Isa0NBQWtDLElBQUk7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsa0JBQWtCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELDREQUErQjtBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdCQUFnQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLDhDQUFpQjtBQUM5Qyw2QkFBNkIsOENBQWlCO0FBQzlDLDZCQUE2Qiw4Q0FBaUI7QUFDOUMsNkJBQTZCLDhDQUFpQjtBQUM5Qyw2QkFBNkIsOENBQWlCO0FBQzlDLDZCQUE2Qiw4Q0FBaUI7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHVCQUF1QixJQUFJLDRCQUE0QjtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELDRDQUE0QztBQUNqRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUU7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUU7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUU7QUFDbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix3QkFBd0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsOENBQWlCO0FBQzlDLDZCQUE2Qiw4Q0FBaUI7QUFDOUMsNkJBQTZCLDhDQUFpQjtBQUM5Qyw2QkFBNkIsOENBQWlCO0FBQzlDLDZCQUE2Qiw4Q0FBaUI7QUFDOUMsNkJBQTZCLDhDQUFpQjtBQUM5QztBQUNBO0FBQ0EsaUNBQWlDLDhDQUFpQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0Esa0JBQWtCLHdDQUF3QyxJQUFJO0FBQzlEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLDRCQUE0QixJQUFJO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLGtEQUFrRCxJQUFJO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw0REFBK0I7QUFDdkQ7QUFDQSxrQkFBa0Isb0JBQW9CLElBQUk7QUFDMUMsd0JBQXdCLDREQUErQjtBQUN2RDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsNERBQStCO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHVDQUF1QztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDRCQUE0QjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDJCQUEyQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZCQUE2QjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZCQUE2QjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZCQUE2QjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZCQUE2QjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isa0RBQWtEO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixrREFBa0Q7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2QkFBNkI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2QkFBNkI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2QkFBNkI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2QkFBNkI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDZCQUE2QjtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix1QkFBdUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQix3QkFBd0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIseUNBQXlDO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixlQUFlO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isa0NBQWtDO0FBQ3hEO0FBQ0E7QUFDQSw4QkFBOEIsNERBQStCO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWdpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2FzbjFqcy9idWlsZC9pbmRleC5lcy5qcz9kMWI5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qIVxuICogQ29weXJpZ2h0IChjKSAyMDE0LCBHTU8gR2xvYmFsU2lnblxuICogQ29weXJpZ2h0IChjKSAyMDE1LTIwMjIsIFBlY3VsaWFyIFZlbnR1cmVzXG4gKiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogXG4gKiBBdXRob3IgMjAxNC0yMDE5LCBZdXJ5IFN0cm96aGV2c2t5XG4gKiBcbiAqIFJlZGlzdHJpYnV0aW9uIGFuZCB1c2UgaW4gc291cmNlIGFuZCBiaW5hcnkgZm9ybXMsIHdpdGggb3Igd2l0aG91dCBtb2RpZmljYXRpb24sXG4gKiBhcmUgcGVybWl0dGVkIHByb3ZpZGVkIHRoYXQgdGhlIGZvbGxvd2luZyBjb25kaXRpb25zIGFyZSBtZXQ6XG4gKiBcbiAqICogUmVkaXN0cmlidXRpb25zIG9mIHNvdXJjZSBjb2RlIG11c3QgcmV0YWluIHRoZSBhYm92ZSBjb3B5cmlnaHQgbm90aWNlLCB0aGlzXG4gKiAgIGxpc3Qgb2YgY29uZGl0aW9ucyBhbmQgdGhlIGZvbGxvd2luZyBkaXNjbGFpbWVyLlxuICogXG4gKiAqIFJlZGlzdHJpYnV0aW9ucyBpbiBiaW5hcnkgZm9ybSBtdXN0IHJlcHJvZHVjZSB0aGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSwgdGhpc1xuICogICBsaXN0IG9mIGNvbmRpdGlvbnMgYW5kIHRoZSBmb2xsb3dpbmcgZGlzY2xhaW1lciBpbiB0aGUgZG9jdW1lbnRhdGlvbiBhbmQvb3JcbiAqICAgb3RoZXIgbWF0ZXJpYWxzIHByb3ZpZGVkIHdpdGggdGhlIGRpc3RyaWJ1dGlvbi5cbiAqIFxuICogKiBOZWl0aGVyIHRoZSBuYW1lIG9mIHRoZSBjb3B5cmlnaHQgaG9sZGVyIG5vciB0aGUgbmFtZXMgb2YgaXRzXG4gKiAgIGNvbnRyaWJ1dG9ycyBtYXkgYmUgdXNlZCB0byBlbmRvcnNlIG9yIHByb21vdGUgcHJvZHVjdHMgZGVyaXZlZCBmcm9tXG4gKiAgIHRoaXMgc29mdHdhcmUgd2l0aG91dCBzcGVjaWZpYyBwcmlvciB3cml0dGVuIHBlcm1pc3Npb24uXG4gKiBcbiAqIFRISVMgU09GVFdBUkUgSVMgUFJPVklERUQgQlkgVEhFIENPUFlSSUdIVCBIT0xERVJTIEFORCBDT05UUklCVVRPUlMgXCJBUyBJU1wiIEFORFxuICogQU5ZIEVYUFJFU1MgT1IgSU1QTElFRCBXQVJSQU5USUVTLCBJTkNMVURJTkcsIEJVVCBOT1QgTElNSVRFRCBUTywgVEhFIElNUExJRURcbiAqIFdBUlJBTlRJRVMgT0YgTUVSQ0hBTlRBQklMSVRZIEFORCBGSVRORVNTIEZPUiBBIFBBUlRJQ1VMQVIgUFVSUE9TRSBBUkVcbiAqIERJU0NMQUlNRUQuIElOIE5PIEVWRU5UIFNIQUxMIFRIRSBDT1BZUklHSFQgSE9MREVSIE9SIENPTlRSSUJVVE9SUyBCRSBMSUFCTEUgRk9SXG4gKiBBTlkgRElSRUNULCBJTkRJUkVDVCwgSU5DSURFTlRBTCwgU1BFQ0lBTCwgRVhFTVBMQVJZLCBPUiBDT05TRVFVRU5USUFMIERBTUFHRVNcbiAqIChJTkNMVURJTkcsIEJVVCBOT1QgTElNSVRFRCBUTywgUFJPQ1VSRU1FTlQgT0YgU1VCU1RJVFVURSBHT09EUyBPUiBTRVJWSUNFUztcbiAqIExPU1MgT0YgVVNFLCBEQVRBLCBPUiBQUk9GSVRTOyBPUiBCVVNJTkVTUyBJTlRFUlJVUFRJT04pIEhPV0VWRVIgQ0FVU0VEIEFORCBPTlxuICogQU5ZIFRIRU9SWSBPRiBMSUFCSUxJVFksIFdIRVRIRVIgSU4gQ09OVFJBQ1QsIFNUUklDVCBMSUFCSUxJVFksIE9SIFRPUlRcbiAqIChJTkNMVURJTkcgTkVHTElHRU5DRSBPUiBPVEhFUldJU0UpIEFSSVNJTkcgSU4gQU5ZIFdBWSBPVVQgT0YgVEhFIFVTRSBPRiBUSElTXG4gKiBTT0ZUV0FSRSwgRVZFTiBJRiBBRFZJU0VEIE9GIFRIRSBQT1NTSUJJTElUWSBPRiBTVUNIIERBTUFHRS5cbiAqIFxuICovXG5cbmltcG9ydCAqIGFzIHB2dHN1dGlscyBmcm9tICdwdnRzdXRpbHMnO1xuaW1wb3J0ICogYXMgcHZ1dGlscyBmcm9tICdwdnV0aWxzJztcblxuZnVuY3Rpb24gYXNzZXJ0QmlnSW50KCkge1xuICAgIGlmICh0eXBlb2YgQmlnSW50ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkJpZ0ludCBpcyBub3QgZGVmaW5lZC4gWW91ciBlbnZpcm9ubWVudCBkb2Vzbid0IGltcGxlbWVudCBCaWdJbnQuXCIpO1xuICAgIH1cbn1cbmZ1bmN0aW9uIGNvbmNhdChidWZmZXJzKSB7XG4gICAgbGV0IG91dHB1dExlbmd0aCA9IDA7XG4gICAgbGV0IHByZXZMZW5ndGggPSAwO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYnVmZmVycy5sZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBidWZmZXIgPSBidWZmZXJzW2ldO1xuICAgICAgICBvdXRwdXRMZW5ndGggKz0gYnVmZmVyLmJ5dGVMZW5ndGg7XG4gICAgfVxuICAgIGNvbnN0IHJldFZpZXcgPSBuZXcgVWludDhBcnJheShvdXRwdXRMZW5ndGgpO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYnVmZmVycy5sZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBidWZmZXIgPSBidWZmZXJzW2ldO1xuICAgICAgICByZXRWaWV3LnNldChuZXcgVWludDhBcnJheShidWZmZXIpLCBwcmV2TGVuZ3RoKTtcbiAgICAgICAgcHJldkxlbmd0aCArPSBidWZmZXIuYnl0ZUxlbmd0aDtcbiAgICB9XG4gICAgcmV0dXJuIHJldFZpZXcuYnVmZmVyO1xufVxuZnVuY3Rpb24gY2hlY2tCdWZmZXJQYXJhbXMoYmFzZUJsb2NrLCBpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKSB7XG4gICAgaWYgKCEoaW5wdXRCdWZmZXIgaW5zdGFuY2VvZiBVaW50OEFycmF5KSkge1xuICAgICAgICBiYXNlQmxvY2suZXJyb3IgPSBcIldyb25nIHBhcmFtZXRlcjogaW5wdXRCdWZmZXIgbXVzdCBiZSAnVWludDhBcnJheSdcIjtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoIWlucHV0QnVmZmVyLmJ5dGVMZW5ndGgpIHtcbiAgICAgICAgYmFzZUJsb2NrLmVycm9yID0gXCJXcm9uZyBwYXJhbWV0ZXI6IGlucHV0QnVmZmVyIGhhcyB6ZXJvIGxlbmd0aFwiO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChpbnB1dE9mZnNldCA8IDApIHtcbiAgICAgICAgYmFzZUJsb2NrLmVycm9yID0gXCJXcm9uZyBwYXJhbWV0ZXI6IGlucHV0T2Zmc2V0IGxlc3MgdGhhbiB6ZXJvXCI7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKGlucHV0TGVuZ3RoIDwgMCkge1xuICAgICAgICBiYXNlQmxvY2suZXJyb3IgPSBcIldyb25nIHBhcmFtZXRlcjogaW5wdXRMZW5ndGggbGVzcyB0aGFuIHplcm9cIjtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoKGlucHV0QnVmZmVyLmJ5dGVMZW5ndGggLSBpbnB1dE9mZnNldCAtIGlucHV0TGVuZ3RoKSA8IDApIHtcbiAgICAgICAgYmFzZUJsb2NrLmVycm9yID0gXCJFbmQgb2YgaW5wdXQgcmVhY2hlZCBiZWZvcmUgbWVzc2FnZSB3YXMgZnVsbHkgZGVjb2RlZCAoaW5jb25zaXN0ZW50IG9mZnNldCBhbmQgbGVuZ3RoIHZhbHVlcylcIjtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cblxuY2xhc3MgVmlld1dyaXRlciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuaXRlbXMgPSBbXTtcbiAgICB9XG4gICAgd3JpdGUoYnVmKSB7XG4gICAgICAgIHRoaXMuaXRlbXMucHVzaChidWYpO1xuICAgIH1cbiAgICBmaW5hbCgpIHtcbiAgICAgICAgcmV0dXJuIGNvbmNhdCh0aGlzLml0ZW1zKTtcbiAgICB9XG59XG5cbmNvbnN0IHBvd2VyczIgPSBbbmV3IFVpbnQ4QXJyYXkoWzFdKV07XG5jb25zdCBkaWdpdHNTdHJpbmcgPSBcIjAxMjM0NTY3ODlcIjtcbmNvbnN0IE5BTUUgPSBcIm5hbWVcIjtcbmNvbnN0IFZBTFVFX0hFWF9WSUVXID0gXCJ2YWx1ZUhleFZpZXdcIjtcbmNvbnN0IElTX0hFWF9PTkxZID0gXCJpc0hleE9ubHlcIjtcbmNvbnN0IElEX0JMT0NLID0gXCJpZEJsb2NrXCI7XG5jb25zdCBUQUdfQ0xBU1MgPSBcInRhZ0NsYXNzXCI7XG5jb25zdCBUQUdfTlVNQkVSID0gXCJ0YWdOdW1iZXJcIjtcbmNvbnN0IElTX0NPTlNUUlVDVEVEID0gXCJpc0NvbnN0cnVjdGVkXCI7XG5jb25zdCBGUk9NX0JFUiA9IFwiZnJvbUJFUlwiO1xuY29uc3QgVE9fQkVSID0gXCJ0b0JFUlwiO1xuY29uc3QgTE9DQUwgPSBcImxvY2FsXCI7XG5jb25zdCBFTVBUWV9TVFJJTkcgPSBcIlwiO1xuY29uc3QgRU1QVFlfQlVGRkVSID0gbmV3IEFycmF5QnVmZmVyKDApO1xuY29uc3QgRU1QVFlfVklFVyA9IG5ldyBVaW50OEFycmF5KDApO1xuY29uc3QgRU5EX09GX0NPTlRFTlRfTkFNRSA9IFwiRW5kT2ZDb250ZW50XCI7XG5jb25zdCBPQ1RFVF9TVFJJTkdfTkFNRSA9IFwiT0NURVQgU1RSSU5HXCI7XG5jb25zdCBCSVRfU1RSSU5HX05BTUUgPSBcIkJJVCBTVFJJTkdcIjtcblxuZnVuY3Rpb24gSGV4QmxvY2soQmFzZUNsYXNzKSB7XG4gICAgdmFyIF9hO1xuICAgIHJldHVybiBfYSA9IGNsYXNzIFNvbWUgZXh0ZW5kcyBCYXNlQ2xhc3Mge1xuICAgICAgICAgICAgZ2V0IHZhbHVlSGV4KCkge1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnZhbHVlSGV4Vmlldy5zbGljZSgpLmJ1ZmZlcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHNldCB2YWx1ZUhleCh2YWx1ZSkge1xuICAgICAgICAgICAgICAgIHRoaXMudmFsdWVIZXhWaWV3ID0gbmV3IFVpbnQ4QXJyYXkodmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3RydWN0b3IoLi4uYXJncykge1xuICAgICAgICAgICAgICAgIHZhciBfYjtcbiAgICAgICAgICAgICAgICBzdXBlciguLi5hcmdzKTtcbiAgICAgICAgICAgICAgICBjb25zdCBwYXJhbXMgPSBhcmdzWzBdIHx8IHt9O1xuICAgICAgICAgICAgICAgIHRoaXMuaXNIZXhPbmx5ID0gKF9iID0gcGFyYW1zLmlzSGV4T25seSkgIT09IG51bGwgJiYgX2IgIT09IHZvaWQgMCA/IF9iIDogZmFsc2U7XG4gICAgICAgICAgICAgICAgdGhpcy52YWx1ZUhleFZpZXcgPSBwYXJhbXMudmFsdWVIZXggPyBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheShwYXJhbXMudmFsdWVIZXgpIDogRU1QVFlfVklFVztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHZpZXcgPSBpbnB1dEJ1ZmZlciBpbnN0YW5jZW9mIEFycmF5QnVmZmVyID8gbmV3IFVpbnQ4QXJyYXkoaW5wdXRCdWZmZXIpIDogaW5wdXRCdWZmZXI7XG4gICAgICAgICAgICAgICAgaWYgKCFjaGVja0J1ZmZlclBhcmFtcyh0aGlzLCB2aWV3LCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgZW5kTGVuZ3RoID0gaW5wdXRPZmZzZXQgKyBpbnB1dExlbmd0aDtcbiAgICAgICAgICAgICAgICB0aGlzLnZhbHVlSGV4VmlldyA9IHZpZXcuc3ViYXJyYXkoaW5wdXRPZmZzZXQsIGVuZExlbmd0aCk7XG4gICAgICAgICAgICAgICAgaWYgKCF0aGlzLnZhbHVlSGV4Vmlldy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy53YXJuaW5ncy5wdXNoKFwiWmVybyBidWZmZXIgbGVuZ3RoXCIpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gaW5wdXRPZmZzZXQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuYmxvY2tMZW5ndGggPSBpbnB1dExlbmd0aDtcbiAgICAgICAgICAgICAgICByZXR1cm4gZW5kTGVuZ3RoO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdG9CRVIoc2l6ZU9ubHkgPSBmYWxzZSkge1xuICAgICAgICAgICAgICAgIGlmICghdGhpcy5pc0hleE9ubHkpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvciA9IFwiRmxhZyAnaXNIZXhPbmx5JyBpcyBub3Qgc2V0LCBhYm9ydFwiO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gRU1QVFlfQlVGRkVSO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoc2l6ZU9ubHkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBBcnJheUJ1ZmZlcih0aGlzLnZhbHVlSGV4Vmlldy5ieXRlTGVuZ3RoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuICh0aGlzLnZhbHVlSGV4Vmlldy5ieXRlTGVuZ3RoID09PSB0aGlzLnZhbHVlSGV4Vmlldy5idWZmZXIuYnl0ZUxlbmd0aClcbiAgICAgICAgICAgICAgICAgICAgPyB0aGlzLnZhbHVlSGV4Vmlldy5idWZmZXJcbiAgICAgICAgICAgICAgICAgICAgOiB0aGlzLnZhbHVlSGV4Vmlldy5zbGljZSgpLmJ1ZmZlcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRvSlNPTigpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAuLi5zdXBlci50b0pTT04oKSxcbiAgICAgICAgICAgICAgICAgICAgaXNIZXhPbmx5OiB0aGlzLmlzSGV4T25seSxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVIZXg6IHB2dHN1dGlscy5Db252ZXJ0LlRvSGV4KHRoaXMudmFsdWVIZXhWaWV3KSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBfYS5OQU1FID0gXCJoZXhCbG9ja1wiLFxuICAgICAgICBfYTtcbn1cblxuY2xhc3MgTG9jYWxCYXNlQmxvY2sge1xuICAgIHN0YXRpYyBibG9ja05hbWUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLk5BTUU7XG4gICAgfVxuICAgIGdldCB2YWx1ZUJlZm9yZURlY29kZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVCZWZvcmVEZWNvZGVWaWV3LnNsaWNlKCkuYnVmZmVyO1xuICAgIH1cbiAgICBzZXQgdmFsdWVCZWZvcmVEZWNvZGUodmFsdWUpIHtcbiAgICAgICAgdGhpcy52YWx1ZUJlZm9yZURlY29kZVZpZXcgPSBuZXcgVWludDhBcnJheSh2YWx1ZSk7XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKHsgYmxvY2tMZW5ndGggPSAwLCBlcnJvciA9IEVNUFRZX1NUUklORywgd2FybmluZ3MgPSBbXSwgdmFsdWVCZWZvcmVEZWNvZGUgPSBFTVBUWV9WSUVXLCB9ID0ge30pIHtcbiAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCA9IGJsb2NrTGVuZ3RoO1xuICAgICAgICB0aGlzLmVycm9yID0gZXJyb3I7XG4gICAgICAgIHRoaXMud2FybmluZ3MgPSB3YXJuaW5ncztcbiAgICAgICAgdGhpcy52YWx1ZUJlZm9yZURlY29kZVZpZXcgPSBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheSh2YWx1ZUJlZm9yZURlY29kZSk7XG4gICAgfVxuICAgIHRvSlNPTigpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGJsb2NrTmFtZTogdGhpcy5jb25zdHJ1Y3Rvci5OQU1FLFxuICAgICAgICAgICAgYmxvY2tMZW5ndGg6IHRoaXMuYmxvY2tMZW5ndGgsXG4gICAgICAgICAgICBlcnJvcjogdGhpcy5lcnJvcixcbiAgICAgICAgICAgIHdhcm5pbmdzOiB0aGlzLndhcm5pbmdzLFxuICAgICAgICAgICAgdmFsdWVCZWZvcmVEZWNvZGU6IHB2dHN1dGlscy5Db252ZXJ0LlRvSGV4KHRoaXMudmFsdWVCZWZvcmVEZWNvZGVWaWV3KSxcbiAgICAgICAgfTtcbiAgICB9XG59XG5Mb2NhbEJhc2VCbG9jay5OQU1FID0gXCJiYXNlQmxvY2tcIjtcblxuY2xhc3MgVmFsdWVCbG9jayBleHRlbmRzIExvY2FsQmFzZUJsb2NrIHtcbiAgICBmcm9tQkVSKF9pbnB1dEJ1ZmZlciwgX2lucHV0T2Zmc2V0LCBfaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgdGhyb3cgVHlwZUVycm9yKFwiVXNlciBuZWVkIHRvIG1ha2UgYSBzcGVjaWZpYyBmdW5jdGlvbiBpbiBhIGNsYXNzIHdoaWNoIGV4dGVuZHMgJ1ZhbHVlQmxvY2snXCIpO1xuICAgIH1cbiAgICB0b0JFUihfc2l6ZU9ubHksIF93cml0ZXIpIHtcbiAgICAgICAgdGhyb3cgVHlwZUVycm9yKFwiVXNlciBuZWVkIHRvIG1ha2UgYSBzcGVjaWZpYyBmdW5jdGlvbiBpbiBhIGNsYXNzIHdoaWNoIGV4dGVuZHMgJ1ZhbHVlQmxvY2snXCIpO1xuICAgIH1cbn1cblZhbHVlQmxvY2suTkFNRSA9IFwidmFsdWVCbG9ja1wiO1xuXG5jbGFzcyBMb2NhbElkZW50aWZpY2F0aW9uQmxvY2sgZXh0ZW5kcyBIZXhCbG9jayhMb2NhbEJhc2VCbG9jaykge1xuICAgIGNvbnN0cnVjdG9yKHsgaWRCbG9jayA9IHt9IH0gPSB7fSkge1xuICAgICAgICB2YXIgX2EsIF9iLCBfYywgX2Q7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIGlmIChpZEJsb2NrKSB7XG4gICAgICAgICAgICB0aGlzLmlzSGV4T25seSA9IChfYSA9IGlkQmxvY2suaXNIZXhPbmx5KSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBmYWxzZTtcbiAgICAgICAgICAgIHRoaXMudmFsdWVIZXhWaWV3ID0gaWRCbG9jay52YWx1ZUhleFxuICAgICAgICAgICAgICAgID8gcHZ0c3V0aWxzLkJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b1VpbnQ4QXJyYXkoaWRCbG9jay52YWx1ZUhleClcbiAgICAgICAgICAgICAgICA6IEVNUFRZX1ZJRVc7XG4gICAgICAgICAgICB0aGlzLnRhZ0NsYXNzID0gKF9iID0gaWRCbG9jay50YWdDbGFzcykgIT09IG51bGwgJiYgX2IgIT09IHZvaWQgMCA/IF9iIDogLTE7XG4gICAgICAgICAgICB0aGlzLnRhZ051bWJlciA9IChfYyA9IGlkQmxvY2sudGFnTnVtYmVyKSAhPT0gbnVsbCAmJiBfYyAhPT0gdm9pZCAwID8gX2MgOiAtMTtcbiAgICAgICAgICAgIHRoaXMuaXNDb25zdHJ1Y3RlZCA9IChfZCA9IGlkQmxvY2suaXNDb25zdHJ1Y3RlZCkgIT09IG51bGwgJiYgX2QgIT09IHZvaWQgMCA/IF9kIDogZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnRhZ0NsYXNzID0gLTE7XG4gICAgICAgICAgICB0aGlzLnRhZ051bWJlciA9IC0xO1xuICAgICAgICAgICAgdGhpcy5pc0NvbnN0cnVjdGVkID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgdG9CRVIoc2l6ZU9ubHkgPSBmYWxzZSkge1xuICAgICAgICBsZXQgZmlyc3RPY3RldCA9IDA7XG4gICAgICAgIHN3aXRjaCAodGhpcy50YWdDbGFzcykge1xuICAgICAgICAgICAgY2FzZSAxOlxuICAgICAgICAgICAgICAgIGZpcnN0T2N0ZXQgfD0gMHgwMDtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgMjpcbiAgICAgICAgICAgICAgICBmaXJzdE9jdGV0IHw9IDB4NDA7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIDM6XG4gICAgICAgICAgICAgICAgZmlyc3RPY3RldCB8PSAweDgwO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSA0OlxuICAgICAgICAgICAgICAgIGZpcnN0T2N0ZXQgfD0gMHhDMDtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgdGhpcy5lcnJvciA9IFwiVW5rbm93biB0YWcgY2xhc3NcIjtcbiAgICAgICAgICAgICAgICByZXR1cm4gRU1QVFlfQlVGRkVSO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLmlzQ29uc3RydWN0ZWQpXG4gICAgICAgICAgICBmaXJzdE9jdGV0IHw9IDB4MjA7XG4gICAgICAgIGlmICh0aGlzLnRhZ051bWJlciA8IDMxICYmICF0aGlzLmlzSGV4T25seSkge1xuICAgICAgICAgICAgY29uc3QgcmV0VmlldyA9IG5ldyBVaW50OEFycmF5KDEpO1xuICAgICAgICAgICAgaWYgKCFzaXplT25seSkge1xuICAgICAgICAgICAgICAgIGxldCBudW1iZXIgPSB0aGlzLnRhZ051bWJlcjtcbiAgICAgICAgICAgICAgICBudW1iZXIgJj0gMHgxRjtcbiAgICAgICAgICAgICAgICBmaXJzdE9jdGV0IHw9IG51bWJlcjtcbiAgICAgICAgICAgICAgICByZXRWaWV3WzBdID0gZmlyc3RPY3RldDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiByZXRWaWV3LmJ1ZmZlcjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXRoaXMuaXNIZXhPbmx5KSB7XG4gICAgICAgICAgICBjb25zdCBlbmNvZGVkQnVmID0gcHZ1dGlscy51dGlsVG9CYXNlKHRoaXMudGFnTnVtYmVyLCA3KTtcbiAgICAgICAgICAgIGNvbnN0IGVuY29kZWRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoZW5jb2RlZEJ1Zik7XG4gICAgICAgICAgICBjb25zdCBzaXplID0gZW5jb2RlZEJ1Zi5ieXRlTGVuZ3RoO1xuICAgICAgICAgICAgY29uc3QgcmV0VmlldyA9IG5ldyBVaW50OEFycmF5KHNpemUgKyAxKTtcbiAgICAgICAgICAgIHJldFZpZXdbMF0gPSAoZmlyc3RPY3RldCB8IDB4MUYpO1xuICAgICAgICAgICAgaWYgKCFzaXplT25seSkge1xuICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgKHNpemUgLSAxKTsgaSsrKVxuICAgICAgICAgICAgICAgICAgICByZXRWaWV3W2kgKyAxXSA9IGVuY29kZWRWaWV3W2ldIHwgMHg4MDtcbiAgICAgICAgICAgICAgICByZXRWaWV3W3NpemVdID0gZW5jb2RlZFZpZXdbc2l6ZSAtIDFdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHJldFZpZXcuYnVmZmVyO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJldFZpZXcgPSBuZXcgVWludDhBcnJheSh0aGlzLnZhbHVlSGV4Vmlldy5ieXRlTGVuZ3RoICsgMSk7XG4gICAgICAgIHJldFZpZXdbMF0gPSAoZmlyc3RPY3RldCB8IDB4MUYpO1xuICAgICAgICBpZiAoIXNpemVPbmx5KSB7XG4gICAgICAgICAgICBjb25zdCBjdXJWaWV3ID0gdGhpcy52YWx1ZUhleFZpZXc7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IChjdXJWaWV3Lmxlbmd0aCAtIDEpOyBpKyspXG4gICAgICAgICAgICAgICAgcmV0Vmlld1tpICsgMV0gPSBjdXJWaWV3W2ldIHwgMHg4MDtcbiAgICAgICAgICAgIHJldFZpZXdbdGhpcy52YWx1ZUhleFZpZXcuYnl0ZUxlbmd0aF0gPSBjdXJWaWV3W2N1clZpZXcubGVuZ3RoIC0gMV07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJldFZpZXcuYnVmZmVyO1xuICAgIH1cbiAgICBmcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgY29uc3QgaW5wdXRWaWV3ID0gcHZ0c3V0aWxzLkJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b1VpbnQ4QXJyYXkoaW5wdXRCdWZmZXIpO1xuICAgICAgICBpZiAoIWNoZWNrQnVmZmVyUGFyYW1zKHRoaXMsIGlucHV0VmlldywgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKSkge1xuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGludEJ1ZmZlciA9IGlucHV0Vmlldy5zdWJhcnJheShpbnB1dE9mZnNldCwgaW5wdXRPZmZzZXQgKyBpbnB1dExlbmd0aCk7XG4gICAgICAgIGlmIChpbnRCdWZmZXIubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICB0aGlzLmVycm9yID0gXCJaZXJvIGJ1ZmZlciBsZW5ndGhcIjtcbiAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB0YWdDbGFzc01hc2sgPSBpbnRCdWZmZXJbMF0gJiAweEMwO1xuICAgICAgICBzd2l0Y2ggKHRhZ0NsYXNzTWFzaykge1xuICAgICAgICAgICAgY2FzZSAweDAwOlxuICAgICAgICAgICAgICAgIHRoaXMudGFnQ2xhc3MgPSAoMSk7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIDB4NDA6XG4gICAgICAgICAgICAgICAgdGhpcy50YWdDbGFzcyA9ICgyKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgMHg4MDpcbiAgICAgICAgICAgICAgICB0aGlzLnRhZ0NsYXNzID0gKDMpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAweEMwOlxuICAgICAgICAgICAgICAgIHRoaXMudGFnQ2xhc3MgPSAoNCk7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIlVua25vd24gdGFnIGNsYXNzXCI7XG4gICAgICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuaXNDb25zdHJ1Y3RlZCA9IChpbnRCdWZmZXJbMF0gJiAweDIwKSA9PT0gMHgyMDtcbiAgICAgICAgdGhpcy5pc0hleE9ubHkgPSBmYWxzZTtcbiAgICAgICAgY29uc3QgdGFnTnVtYmVyTWFzayA9IGludEJ1ZmZlclswXSAmIDB4MUY7XG4gICAgICAgIGlmICh0YWdOdW1iZXJNYXNrICE9PSAweDFGKSB7XG4gICAgICAgICAgICB0aGlzLnRhZ051bWJlciA9ICh0YWdOdW1iZXJNYXNrKTtcbiAgICAgICAgICAgIHRoaXMuYmxvY2tMZW5ndGggPSAxO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgbGV0IGNvdW50ID0gMTtcbiAgICAgICAgICAgIGxldCBpbnRUYWdOdW1iZXJCdWZmZXIgPSB0aGlzLnZhbHVlSGV4VmlldyA9IG5ldyBVaW50OEFycmF5KDI1NSk7XG4gICAgICAgICAgICBsZXQgdGFnTnVtYmVyQnVmZmVyTWF4TGVuZ3RoID0gMjU1O1xuICAgICAgICAgICAgd2hpbGUgKGludEJ1ZmZlcltjb3VudF0gJiAweDgwKSB7XG4gICAgICAgICAgICAgICAgaW50VGFnTnVtYmVyQnVmZmVyW2NvdW50IC0gMV0gPSBpbnRCdWZmZXJbY291bnRdICYgMHg3RjtcbiAgICAgICAgICAgICAgICBjb3VudCsrO1xuICAgICAgICAgICAgICAgIGlmIChjb3VudCA+PSBpbnRCdWZmZXIubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIkVuZCBvZiBpbnB1dCByZWFjaGVkIGJlZm9yZSBtZXNzYWdlIHdhcyBmdWxseSBkZWNvZGVkXCI7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGNvdW50ID09PSB0YWdOdW1iZXJCdWZmZXJNYXhMZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgdGFnTnVtYmVyQnVmZmVyTWF4TGVuZ3RoICs9IDI1NTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdGVtcEJ1ZmZlclZpZXcgPSBuZXcgVWludDhBcnJheSh0YWdOdW1iZXJCdWZmZXJNYXhMZW5ndGgpO1xuICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGludFRhZ051bWJlckJ1ZmZlci5sZW5ndGg7IGkrKylcbiAgICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXJWaWV3W2ldID0gaW50VGFnTnVtYmVyQnVmZmVyW2ldO1xuICAgICAgICAgICAgICAgICAgICBpbnRUYWdOdW1iZXJCdWZmZXIgPSB0aGlzLnZhbHVlSGV4VmlldyA9IG5ldyBVaW50OEFycmF5KHRhZ051bWJlckJ1ZmZlck1heExlbmd0aCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCA9IChjb3VudCArIDEpO1xuICAgICAgICAgICAgaW50VGFnTnVtYmVyQnVmZmVyW2NvdW50IC0gMV0gPSBpbnRCdWZmZXJbY291bnRdICYgMHg3RjtcbiAgICAgICAgICAgIGNvbnN0IHRlbXBCdWZmZXJWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoY291bnQpO1xuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgaSsrKVxuICAgICAgICAgICAgICAgIHRlbXBCdWZmZXJWaWV3W2ldID0gaW50VGFnTnVtYmVyQnVmZmVyW2ldO1xuICAgICAgICAgICAgaW50VGFnTnVtYmVyQnVmZmVyID0gdGhpcy52YWx1ZUhleFZpZXcgPSBuZXcgVWludDhBcnJheShjb3VudCk7XG4gICAgICAgICAgICBpbnRUYWdOdW1iZXJCdWZmZXIuc2V0KHRlbXBCdWZmZXJWaWV3KTtcbiAgICAgICAgICAgIGlmICh0aGlzLmJsb2NrTGVuZ3RoIDw9IDkpXG4gICAgICAgICAgICAgICAgdGhpcy50YWdOdW1iZXIgPSBwdnV0aWxzLnV0aWxGcm9tQmFzZShpbnRUYWdOdW1iZXJCdWZmZXIsIDcpO1xuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy5pc0hleE9ubHkgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHRoaXMud2FybmluZ3MucHVzaChcIlRhZyB0b28gbG9uZywgcmVwcmVzZW50ZWQgYXMgaGV4LWNvZGVkXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmICgoKHRoaXMudGFnQ2xhc3MgPT09IDEpKVxuICAgICAgICAgICAgJiYgKHRoaXMuaXNDb25zdHJ1Y3RlZCkpIHtcbiAgICAgICAgICAgIHN3aXRjaCAodGhpcy50YWdOdW1iZXIpIHtcbiAgICAgICAgICAgICAgICBjYXNlIDE6XG4gICAgICAgICAgICAgICAgY2FzZSAyOlxuICAgICAgICAgICAgICAgIGNhc2UgNTpcbiAgICAgICAgICAgICAgICBjYXNlIDY6XG4gICAgICAgICAgICAgICAgY2FzZSA5OlxuICAgICAgICAgICAgICAgIGNhc2UgMTM6XG4gICAgICAgICAgICAgICAgY2FzZSAxNDpcbiAgICAgICAgICAgICAgICBjYXNlIDIzOlxuICAgICAgICAgICAgICAgIGNhc2UgMjQ6XG4gICAgICAgICAgICAgICAgY2FzZSAzMTpcbiAgICAgICAgICAgICAgICBjYXNlIDMyOlxuICAgICAgICAgICAgICAgIGNhc2UgMzM6XG4gICAgICAgICAgICAgICAgY2FzZSAzNDpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvciA9IFwiQ29uc3RydWN0ZWQgZW5jb2RpbmcgdXNlZCBmb3IgcHJpbWl0aXZlIHR5cGVcIjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiAoaW5wdXRPZmZzZXQgKyB0aGlzLmJsb2NrTGVuZ3RoKTtcbiAgICB9XG4gICAgdG9KU09OKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3VwZXIudG9KU09OKCksXG4gICAgICAgICAgICB0YWdDbGFzczogdGhpcy50YWdDbGFzcyxcbiAgICAgICAgICAgIHRhZ051bWJlcjogdGhpcy50YWdOdW1iZXIsXG4gICAgICAgICAgICBpc0NvbnN0cnVjdGVkOiB0aGlzLmlzQ29uc3RydWN0ZWQsXG4gICAgICAgIH07XG4gICAgfVxufVxuTG9jYWxJZGVudGlmaWNhdGlvbkJsb2NrLk5BTUUgPSBcImlkZW50aWZpY2F0aW9uQmxvY2tcIjtcblxuY2xhc3MgTG9jYWxMZW5ndGhCbG9jayBleHRlbmRzIExvY2FsQmFzZUJsb2NrIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGxlbkJsb2NrID0ge30gfSA9IHt9KSB7XG4gICAgICAgIHZhciBfYSwgX2IsIF9jO1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICB0aGlzLmlzSW5kZWZpbml0ZUZvcm0gPSAoX2EgPSBsZW5CbG9jay5pc0luZGVmaW5pdGVGb3JtKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBmYWxzZTtcbiAgICAgICAgdGhpcy5sb25nRm9ybVVzZWQgPSAoX2IgPSBsZW5CbG9jay5sb25nRm9ybVVzZWQpICE9PSBudWxsICYmIF9iICE9PSB2b2lkIDAgPyBfYiA6IGZhbHNlO1xuICAgICAgICB0aGlzLmxlbmd0aCA9IChfYyA9IGxlbkJsb2NrLmxlbmd0aCkgIT09IG51bGwgJiYgX2MgIT09IHZvaWQgMCA/IF9jIDogMDtcbiAgICB9XG4gICAgZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IHZpZXcgPSBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheShpbnB1dEJ1ZmZlcik7XG4gICAgICAgIGlmICghY2hlY2tCdWZmZXJQYXJhbXModGhpcywgdmlldywgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKSkge1xuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGludEJ1ZmZlciA9IHZpZXcuc3ViYXJyYXkoaW5wdXRPZmZzZXQsIGlucHV0T2Zmc2V0ICsgaW5wdXRMZW5ndGgpO1xuICAgICAgICBpZiAoaW50QnVmZmVyLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgdGhpcy5lcnJvciA9IFwiWmVybyBidWZmZXIgbGVuZ3RoXCI7XG4gICAgICAgICAgICByZXR1cm4gLTE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGludEJ1ZmZlclswXSA9PT0gMHhGRikge1xuICAgICAgICAgICAgdGhpcy5lcnJvciA9IFwiTGVuZ3RoIGJsb2NrIDB4RkYgaXMgcmVzZXJ2ZWQgYnkgc3RhbmRhcmRcIjtcbiAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmlzSW5kZWZpbml0ZUZvcm0gPSBpbnRCdWZmZXJbMF0gPT09IDB4ODA7XG4gICAgICAgIGlmICh0aGlzLmlzSW5kZWZpbml0ZUZvcm0pIHtcbiAgICAgICAgICAgIHRoaXMuYmxvY2tMZW5ndGggPSAxO1xuICAgICAgICAgICAgcmV0dXJuIChpbnB1dE9mZnNldCArIHRoaXMuYmxvY2tMZW5ndGgpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMubG9uZ0Zvcm1Vc2VkID0gISEoaW50QnVmZmVyWzBdICYgMHg4MCk7XG4gICAgICAgIGlmICh0aGlzLmxvbmdGb3JtVXNlZCA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgIHRoaXMubGVuZ3RoID0gKGludEJ1ZmZlclswXSk7XG4gICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoID0gMTtcbiAgICAgICAgICAgIHJldHVybiAoaW5wdXRPZmZzZXQgKyB0aGlzLmJsb2NrTGVuZ3RoKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBjb3VudCA9IGludEJ1ZmZlclswXSAmIDB4N0Y7XG4gICAgICAgIGlmIChjb3VudCA+IDgpIHtcbiAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIlRvbyBiaWcgaW50ZWdlclwiO1xuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICAgIGlmICgoY291bnQgKyAxKSA+IGludEJ1ZmZlci5sZW5ndGgpIHtcbiAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIkVuZCBvZiBpbnB1dCByZWFjaGVkIGJlZm9yZSBtZXNzYWdlIHdhcyBmdWxseSBkZWNvZGVkXCI7XG4gICAgICAgICAgICByZXR1cm4gLTE7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgbGVuT2Zmc2V0ID0gaW5wdXRPZmZzZXQgKyAxO1xuICAgICAgICBjb25zdCBsZW5ndGhCdWZmZXJWaWV3ID0gdmlldy5zdWJhcnJheShsZW5PZmZzZXQsIGxlbk9mZnNldCArIGNvdW50KTtcbiAgICAgICAgaWYgKGxlbmd0aEJ1ZmZlclZpZXdbY291bnQgLSAxXSA9PT0gMHgwMClcbiAgICAgICAgICAgIHRoaXMud2FybmluZ3MucHVzaChcIk5lZWRsZXNzbHkgbG9uZyBlbmNvZGVkIGxlbmd0aFwiKTtcbiAgICAgICAgdGhpcy5sZW5ndGggPSBwdnV0aWxzLnV0aWxGcm9tQmFzZShsZW5ndGhCdWZmZXJWaWV3LCA4KTtcbiAgICAgICAgaWYgKHRoaXMubG9uZ0Zvcm1Vc2VkICYmICh0aGlzLmxlbmd0aCA8PSAxMjcpKVxuICAgICAgICAgICAgdGhpcy53YXJuaW5ncy5wdXNoKFwiVW5uZWNlc3NhcnkgdXNhZ2Ugb2YgbG9uZyBsZW5ndGggZm9ybVwiKTtcbiAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCA9IGNvdW50ICsgMTtcbiAgICAgICAgcmV0dXJuIChpbnB1dE9mZnNldCArIHRoaXMuYmxvY2tMZW5ndGgpO1xuICAgIH1cbiAgICB0b0JFUihzaXplT25seSA9IGZhbHNlKSB7XG4gICAgICAgIGxldCByZXRCdWY7XG4gICAgICAgIGxldCByZXRWaWV3O1xuICAgICAgICBpZiAodGhpcy5sZW5ndGggPiAxMjcpXG4gICAgICAgICAgICB0aGlzLmxvbmdGb3JtVXNlZCA9IHRydWU7XG4gICAgICAgIGlmICh0aGlzLmlzSW5kZWZpbml0ZUZvcm0pIHtcbiAgICAgICAgICAgIHJldEJ1ZiA9IG5ldyBBcnJheUJ1ZmZlcigxKTtcbiAgICAgICAgICAgIGlmIChzaXplT25seSA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICByZXRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkocmV0QnVmKTtcbiAgICAgICAgICAgICAgICByZXRWaWV3WzBdID0gMHg4MDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiByZXRCdWY7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMubG9uZ0Zvcm1Vc2VkKSB7XG4gICAgICAgICAgICBjb25zdCBlbmNvZGVkQnVmID0gcHZ1dGlscy51dGlsVG9CYXNlKHRoaXMubGVuZ3RoLCA4KTtcbiAgICAgICAgICAgIGlmIChlbmNvZGVkQnVmLmJ5dGVMZW5ndGggPiAxMjcpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmVycm9yID0gXCJUb28gYmlnIGxlbmd0aFwiO1xuICAgICAgICAgICAgICAgIHJldHVybiAoRU1QVFlfQlVGRkVSKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldEJ1ZiA9IG5ldyBBcnJheUJ1ZmZlcihlbmNvZGVkQnVmLmJ5dGVMZW5ndGggKyAxKTtcbiAgICAgICAgICAgIGlmIChzaXplT25seSlcbiAgICAgICAgICAgICAgICByZXR1cm4gcmV0QnVmO1xuICAgICAgICAgICAgY29uc3QgZW5jb2RlZFZpZXcgPSBuZXcgVWludDhBcnJheShlbmNvZGVkQnVmKTtcbiAgICAgICAgICAgIHJldFZpZXcgPSBuZXcgVWludDhBcnJheShyZXRCdWYpO1xuICAgICAgICAgICAgcmV0Vmlld1swXSA9IGVuY29kZWRCdWYuYnl0ZUxlbmd0aCB8IDB4ODA7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGVuY29kZWRCdWYuYnl0ZUxlbmd0aDsgaSsrKVxuICAgICAgICAgICAgICAgIHJldFZpZXdbaSArIDFdID0gZW5jb2RlZFZpZXdbaV07XG4gICAgICAgICAgICByZXR1cm4gcmV0QnVmO1xuICAgICAgICB9XG4gICAgICAgIHJldEJ1ZiA9IG5ldyBBcnJheUJ1ZmZlcigxKTtcbiAgICAgICAgaWYgKHNpemVPbmx5ID09PSBmYWxzZSkge1xuICAgICAgICAgICAgcmV0VmlldyA9IG5ldyBVaW50OEFycmF5KHJldEJ1Zik7XG4gICAgICAgICAgICByZXRWaWV3WzBdID0gdGhpcy5sZW5ndGg7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJldEJ1ZjtcbiAgICB9XG4gICAgdG9KU09OKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3VwZXIudG9KU09OKCksXG4gICAgICAgICAgICBpc0luZGVmaW5pdGVGb3JtOiB0aGlzLmlzSW5kZWZpbml0ZUZvcm0sXG4gICAgICAgICAgICBsb25nRm9ybVVzZWQ6IHRoaXMubG9uZ0Zvcm1Vc2VkLFxuICAgICAgICAgICAgbGVuZ3RoOiB0aGlzLmxlbmd0aCxcbiAgICAgICAgfTtcbiAgICB9XG59XG5Mb2NhbExlbmd0aEJsb2NrLk5BTUUgPSBcImxlbmd0aEJsb2NrXCI7XG5cbmNvbnN0IHR5cGVTdG9yZSA9IHt9O1xuXG5jbGFzcyBCYXNlQmxvY2sgZXh0ZW5kcyBMb2NhbEJhc2VCbG9jayB7XG4gICAgY29uc3RydWN0b3IoeyBuYW1lID0gRU1QVFlfU1RSSU5HLCBvcHRpb25hbCA9IGZhbHNlLCBwcmltaXRpdmVTY2hlbWEsIC4uLnBhcmFtZXRlcnMgfSA9IHt9LCB2YWx1ZUJsb2NrVHlwZSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5uYW1lID0gbmFtZTtcbiAgICAgICAgdGhpcy5vcHRpb25hbCA9IG9wdGlvbmFsO1xuICAgICAgICBpZiAocHJpbWl0aXZlU2NoZW1hKSB7XG4gICAgICAgICAgICB0aGlzLnByaW1pdGl2ZVNjaGVtYSA9IHByaW1pdGl2ZVNjaGVtYTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmlkQmxvY2sgPSBuZXcgTG9jYWxJZGVudGlmaWNhdGlvbkJsb2NrKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLmxlbkJsb2NrID0gbmV3IExvY2FsTGVuZ3RoQmxvY2socGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMudmFsdWVCbG9jayA9IHZhbHVlQmxvY2tUeXBlID8gbmV3IHZhbHVlQmxvY2tUeXBlKHBhcmFtZXRlcnMpIDogbmV3IFZhbHVlQmxvY2socGFyYW1ldGVycyk7XG4gICAgfVxuICAgIGZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkge1xuICAgICAgICBjb25zdCByZXN1bHRPZmZzZXQgPSB0aGlzLnZhbHVlQmxvY2suZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsICh0aGlzLmxlbkJsb2NrLmlzSW5kZWZpbml0ZUZvcm0pXG4gICAgICAgICAgICA/IGlucHV0TGVuZ3RoXG4gICAgICAgICAgICA6IHRoaXMubGVuQmxvY2subGVuZ3RoKTtcbiAgICAgICAgaWYgKHJlc3VsdE9mZnNldCA9PT0gLTEpIHtcbiAgICAgICAgICAgIHRoaXMuZXJyb3IgPSB0aGlzLnZhbHVlQmxvY2suZXJyb3I7XG4gICAgICAgICAgICByZXR1cm4gcmVzdWx0T2Zmc2V0O1xuICAgICAgICB9XG4gICAgICAgIGlmICghdGhpcy5pZEJsb2NrLmVycm9yLmxlbmd0aClcbiAgICAgICAgICAgIHRoaXMuYmxvY2tMZW5ndGggKz0gdGhpcy5pZEJsb2NrLmJsb2NrTGVuZ3RoO1xuICAgICAgICBpZiAoIXRoaXMubGVuQmxvY2suZXJyb3IubGVuZ3RoKVxuICAgICAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCArPSB0aGlzLmxlbkJsb2NrLmJsb2NrTGVuZ3RoO1xuICAgICAgICBpZiAoIXRoaXMudmFsdWVCbG9jay5lcnJvci5sZW5ndGgpXG4gICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoICs9IHRoaXMudmFsdWVCbG9jay5ibG9ja0xlbmd0aDtcbiAgICAgICAgcmV0dXJuIHJlc3VsdE9mZnNldDtcbiAgICB9XG4gICAgdG9CRVIoc2l6ZU9ubHksIHdyaXRlcikge1xuICAgICAgICBjb25zdCBfd3JpdGVyID0gd3JpdGVyIHx8IG5ldyBWaWV3V3JpdGVyKCk7XG4gICAgICAgIGlmICghd3JpdGVyKSB7XG4gICAgICAgICAgICBwcmVwYXJlSW5kZWZpbml0ZUZvcm0odGhpcyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaWRCbG9ja0J1ZiA9IHRoaXMuaWRCbG9jay50b0JFUihzaXplT25seSk7XG4gICAgICAgIF93cml0ZXIud3JpdGUoaWRCbG9ja0J1Zik7XG4gICAgICAgIGlmICh0aGlzLmxlbkJsb2NrLmlzSW5kZWZpbml0ZUZvcm0pIHtcbiAgICAgICAgICAgIF93cml0ZXIud3JpdGUobmV3IFVpbnQ4QXJyYXkoWzB4ODBdKS5idWZmZXIpO1xuICAgICAgICAgICAgdGhpcy52YWx1ZUJsb2NrLnRvQkVSKHNpemVPbmx5LCBfd3JpdGVyKTtcbiAgICAgICAgICAgIF93cml0ZXIud3JpdGUobmV3IEFycmF5QnVmZmVyKDIpKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlQmxvY2tCdWYgPSB0aGlzLnZhbHVlQmxvY2sudG9CRVIoc2l6ZU9ubHkpO1xuICAgICAgICAgICAgdGhpcy5sZW5CbG9jay5sZW5ndGggPSB2YWx1ZUJsb2NrQnVmLmJ5dGVMZW5ndGg7XG4gICAgICAgICAgICBjb25zdCBsZW5CbG9ja0J1ZiA9IHRoaXMubGVuQmxvY2sudG9CRVIoc2l6ZU9ubHkpO1xuICAgICAgICAgICAgX3dyaXRlci53cml0ZShsZW5CbG9ja0J1Zik7XG4gICAgICAgICAgICBfd3JpdGVyLndyaXRlKHZhbHVlQmxvY2tCdWYpO1xuICAgICAgICB9XG4gICAgICAgIGlmICghd3JpdGVyKSB7XG4gICAgICAgICAgICByZXR1cm4gX3dyaXRlci5maW5hbCgpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBFTVBUWV9CVUZGRVI7XG4gICAgfVxuICAgIHRvSlNPTigpIHtcbiAgICAgICAgY29uc3Qgb2JqZWN0ID0ge1xuICAgICAgICAgICAgLi4uc3VwZXIudG9KU09OKCksXG4gICAgICAgICAgICBpZEJsb2NrOiB0aGlzLmlkQmxvY2sudG9KU09OKCksXG4gICAgICAgICAgICBsZW5CbG9jazogdGhpcy5sZW5CbG9jay50b0pTT04oKSxcbiAgICAgICAgICAgIHZhbHVlQmxvY2s6IHRoaXMudmFsdWVCbG9jay50b0pTT04oKSxcbiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSxcbiAgICAgICAgICAgIG9wdGlvbmFsOiB0aGlzLm9wdGlvbmFsLFxuICAgICAgICB9O1xuICAgICAgICBpZiAodGhpcy5wcmltaXRpdmVTY2hlbWEpXG4gICAgICAgICAgICBvYmplY3QucHJpbWl0aXZlU2NoZW1hID0gdGhpcy5wcmltaXRpdmVTY2hlbWEudG9KU09OKCk7XG4gICAgICAgIHJldHVybiBvYmplY3Q7XG4gICAgfVxuICAgIHRvU3RyaW5nKGVuY29kaW5nID0gXCJhc2NpaVwiKSB7XG4gICAgICAgIGlmIChlbmNvZGluZyA9PT0gXCJhc2NpaVwiKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5vbkFzY2lpRW5jb2RpbmcoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcHZ0c3V0aWxzLkNvbnZlcnQuVG9IZXgodGhpcy50b0JFUigpKTtcbiAgICB9XG4gICAgb25Bc2NpaUVuY29kaW5nKCkge1xuICAgICAgICBjb25zdCBuYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5OQU1FO1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHB2dHN1dGlscy5Db252ZXJ0LlRvSGV4KHRoaXMudmFsdWVCbG9jay52YWx1ZUJlZm9yZURlY29kZVZpZXcpO1xuICAgICAgICByZXR1cm4gYCR7bmFtZX0gOiAke3ZhbHVlfWA7XG4gICAgfVxuICAgIGlzRXF1YWwob3RoZXIpIHtcbiAgICAgICAgaWYgKHRoaXMgPT09IG90aGVyKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIShvdGhlciBpbnN0YW5jZW9mIHRoaXMuY29uc3RydWN0b3IpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdGhpc1JhdyA9IHRoaXMudG9CRVIoKTtcbiAgICAgICAgY29uc3Qgb3RoZXJSYXcgPSBvdGhlci50b0JFUigpO1xuICAgICAgICByZXR1cm4gcHZ1dGlscy5pc0VxdWFsQnVmZmVyKHRoaXNSYXcsIG90aGVyUmF3KTtcbiAgICB9XG59XG5CYXNlQmxvY2suTkFNRSA9IFwiQmFzZUJsb2NrXCI7XG5mdW5jdGlvbiBwcmVwYXJlSW5kZWZpbml0ZUZvcm0oYmFzZUJsb2NrKSB7XG4gICAgdmFyIF9hO1xuICAgIGlmIChiYXNlQmxvY2sgaW5zdGFuY2VvZiB0eXBlU3RvcmUuQ29uc3RydWN0ZWQpIHtcbiAgICAgICAgZm9yIChjb25zdCB2YWx1ZSBvZiBiYXNlQmxvY2sudmFsdWVCbG9jay52YWx1ZSkge1xuICAgICAgICAgICAgaWYgKHByZXBhcmVJbmRlZmluaXRlRm9ybSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICBiYXNlQmxvY2subGVuQmxvY2suaXNJbmRlZmluaXRlRm9ybSA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuICEhKChfYSA9IGJhc2VCbG9jay5sZW5CbG9jaykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmlzSW5kZWZpbml0ZUZvcm0pO1xufVxuXG5jbGFzcyBCYXNlU3RyaW5nQmxvY2sgZXh0ZW5kcyBCYXNlQmxvY2sge1xuICAgIGdldFZhbHVlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy52YWx1ZUJsb2NrLnZhbHVlO1xuICAgIH1cbiAgICBzZXRWYWx1ZSh2YWx1ZSkge1xuICAgICAgICB0aGlzLnZhbHVlQmxvY2sudmFsdWUgPSB2YWx1ZTtcbiAgICB9XG4gICAgY29uc3RydWN0b3IoeyB2YWx1ZSA9IEVNUFRZX1NUUklORywgLi4ucGFyYW1ldGVycyB9ID0ge30sIHN0cmluZ1ZhbHVlQmxvY2tUeXBlKSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMsIHN0cmluZ1ZhbHVlQmxvY2tUeXBlKTtcbiAgICAgICAgaWYgKHZhbHVlKSB7XG4gICAgICAgICAgICB0aGlzLmZyb21TdHJpbmcodmFsdWUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkge1xuICAgICAgICBjb25zdCByZXN1bHRPZmZzZXQgPSB0aGlzLnZhbHVlQmxvY2suZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsICh0aGlzLmxlbkJsb2NrLmlzSW5kZWZpbml0ZUZvcm0pXG4gICAgICAgICAgICA/IGlucHV0TGVuZ3RoXG4gICAgICAgICAgICA6IHRoaXMubGVuQmxvY2subGVuZ3RoKTtcbiAgICAgICAgaWYgKHJlc3VsdE9mZnNldCA9PT0gLTEpIHtcbiAgICAgICAgICAgIHRoaXMuZXJyb3IgPSB0aGlzLnZhbHVlQmxvY2suZXJyb3I7XG4gICAgICAgICAgICByZXR1cm4gcmVzdWx0T2Zmc2V0O1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuZnJvbUJ1ZmZlcih0aGlzLnZhbHVlQmxvY2sudmFsdWVIZXhWaWV3KTtcbiAgICAgICAgaWYgKCF0aGlzLmlkQmxvY2suZXJyb3IubGVuZ3RoKVxuICAgICAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCArPSB0aGlzLmlkQmxvY2suYmxvY2tMZW5ndGg7XG4gICAgICAgIGlmICghdGhpcy5sZW5CbG9jay5lcnJvci5sZW5ndGgpXG4gICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoICs9IHRoaXMubGVuQmxvY2suYmxvY2tMZW5ndGg7XG4gICAgICAgIGlmICghdGhpcy52YWx1ZUJsb2NrLmVycm9yLmxlbmd0aClcbiAgICAgICAgICAgIHRoaXMuYmxvY2tMZW5ndGggKz0gdGhpcy52YWx1ZUJsb2NrLmJsb2NrTGVuZ3RoO1xuICAgICAgICByZXR1cm4gcmVzdWx0T2Zmc2V0O1xuICAgIH1cbiAgICBvbkFzY2lpRW5jb2RpbmcoKSB7XG4gICAgICAgIHJldHVybiBgJHt0aGlzLmNvbnN0cnVjdG9yLk5BTUV9IDogJyR7dGhpcy52YWx1ZUJsb2NrLnZhbHVlfSdgO1xuICAgIH1cbn1cbkJhc2VTdHJpbmdCbG9jay5OQU1FID0gXCJCYXNlU3RyaW5nQmxvY2tcIjtcblxuY2xhc3MgTG9jYWxQcmltaXRpdmVWYWx1ZUJsb2NrIGV4dGVuZHMgSGV4QmxvY2soVmFsdWVCbG9jaykge1xuICAgIGNvbnN0cnVjdG9yKHsgaXNIZXhPbmx5ID0gdHJ1ZSwgLi4ucGFyYW1ldGVycyB9ID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMuaXNIZXhPbmx5ID0gaXNIZXhPbmx5O1xuICAgIH1cbn1cbkxvY2FsUHJpbWl0aXZlVmFsdWVCbG9jay5OQU1FID0gXCJQcmltaXRpdmVWYWx1ZUJsb2NrXCI7XG5cbnZhciBfYSR3O1xuY2xhc3MgUHJpbWl0aXZlIGV4dGVuZHMgQmFzZUJsb2NrIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycywgTG9jYWxQcmltaXRpdmVWYWx1ZUJsb2NrKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLmlzQ29uc3RydWN0ZWQgPSBmYWxzZTtcbiAgICB9XG59XG5fYSR3ID0gUHJpbWl0aXZlO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuUHJpbWl0aXZlID0gX2Ekdztcbn0pKCk7XG5QcmltaXRpdmUuTkFNRSA9IFwiUFJJTUlUSVZFXCI7XG5cbmZ1bmN0aW9uIGxvY2FsQ2hhbmdlVHlwZShpbnB1dE9iamVjdCwgbmV3VHlwZSkge1xuICAgIGlmIChpbnB1dE9iamVjdCBpbnN0YW5jZW9mIG5ld1R5cGUpIHtcbiAgICAgICAgcmV0dXJuIGlucHV0T2JqZWN0O1xuICAgIH1cbiAgICBjb25zdCBuZXdPYmplY3QgPSBuZXcgbmV3VHlwZSgpO1xuICAgIG5ld09iamVjdC5pZEJsb2NrID0gaW5wdXRPYmplY3QuaWRCbG9jaztcbiAgICBuZXdPYmplY3QubGVuQmxvY2sgPSBpbnB1dE9iamVjdC5sZW5CbG9jaztcbiAgICBuZXdPYmplY3Qud2FybmluZ3MgPSBpbnB1dE9iamVjdC53YXJuaW5ncztcbiAgICBuZXdPYmplY3QudmFsdWVCZWZvcmVEZWNvZGVWaWV3ID0gaW5wdXRPYmplY3QudmFsdWVCZWZvcmVEZWNvZGVWaWV3O1xuICAgIHJldHVybiBuZXdPYmplY3Q7XG59XG5mdW5jdGlvbiBsb2NhbEZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0ID0gMCwgaW5wdXRMZW5ndGggPSBpbnB1dEJ1ZmZlci5sZW5ndGgpIHtcbiAgICBjb25zdCBpbmNvbWluZ09mZnNldCA9IGlucHV0T2Zmc2V0O1xuICAgIGxldCByZXR1cm5PYmplY3QgPSBuZXcgQmFzZUJsb2NrKHt9LCBWYWx1ZUJsb2NrKTtcbiAgICBjb25zdCBiYXNlQmxvY2sgPSBuZXcgTG9jYWxCYXNlQmxvY2soKTtcbiAgICBpZiAoIWNoZWNrQnVmZmVyUGFyYW1zKGJhc2VCbG9jaywgaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkpIHtcbiAgICAgICAgcmV0dXJuT2JqZWN0LmVycm9yID0gYmFzZUJsb2NrLmVycm9yO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgb2Zmc2V0OiAtMSxcbiAgICAgICAgICAgIHJlc3VsdDogcmV0dXJuT2JqZWN0LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBjb25zdCBpbnRCdWZmZXIgPSBpbnB1dEJ1ZmZlci5zdWJhcnJheShpbnB1dE9mZnNldCwgaW5wdXRPZmZzZXQgKyBpbnB1dExlbmd0aCk7XG4gICAgaWYgKCFpbnRCdWZmZXIubGVuZ3RoKSB7XG4gICAgICAgIHJldHVybk9iamVjdC5lcnJvciA9IFwiWmVybyBidWZmZXIgbGVuZ3RoXCI7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBvZmZzZXQ6IC0xLFxuICAgICAgICAgICAgcmVzdWx0OiByZXR1cm5PYmplY3QsXG4gICAgICAgIH07XG4gICAgfVxuICAgIGxldCByZXN1bHRPZmZzZXQgPSByZXR1cm5PYmplY3QuaWRCbG9jay5mcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpO1xuICAgIGlmIChyZXR1cm5PYmplY3QuaWRCbG9jay53YXJuaW5ncy5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuT2JqZWN0Lndhcm5pbmdzLmNvbmNhdChyZXR1cm5PYmplY3QuaWRCbG9jay53YXJuaW5ncyk7XG4gICAgfVxuICAgIGlmIChyZXN1bHRPZmZzZXQgPT09IC0xKSB7XG4gICAgICAgIHJldHVybk9iamVjdC5lcnJvciA9IHJldHVybk9iamVjdC5pZEJsb2NrLmVycm9yO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgb2Zmc2V0OiAtMSxcbiAgICAgICAgICAgIHJlc3VsdDogcmV0dXJuT2JqZWN0LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpbnB1dE9mZnNldCA9IHJlc3VsdE9mZnNldDtcbiAgICBpbnB1dExlbmd0aCAtPSByZXR1cm5PYmplY3QuaWRCbG9jay5ibG9ja0xlbmd0aDtcbiAgICByZXN1bHRPZmZzZXQgPSByZXR1cm5PYmplY3QubGVuQmxvY2suZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKTtcbiAgICBpZiAocmV0dXJuT2JqZWN0LmxlbkJsb2NrLndhcm5pbmdzLmxlbmd0aCkge1xuICAgICAgICByZXR1cm5PYmplY3Qud2FybmluZ3MuY29uY2F0KHJldHVybk9iamVjdC5sZW5CbG9jay53YXJuaW5ncyk7XG4gICAgfVxuICAgIGlmIChyZXN1bHRPZmZzZXQgPT09IC0xKSB7XG4gICAgICAgIHJldHVybk9iamVjdC5lcnJvciA9IHJldHVybk9iamVjdC5sZW5CbG9jay5lcnJvcjtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIG9mZnNldDogLTEsXG4gICAgICAgICAgICByZXN1bHQ6IHJldHVybk9iamVjdCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgaW5wdXRPZmZzZXQgPSByZXN1bHRPZmZzZXQ7XG4gICAgaW5wdXRMZW5ndGggLT0gcmV0dXJuT2JqZWN0LmxlbkJsb2NrLmJsb2NrTGVuZ3RoO1xuICAgIGlmICghcmV0dXJuT2JqZWN0LmlkQmxvY2suaXNDb25zdHJ1Y3RlZFxuICAgICAgICAmJiByZXR1cm5PYmplY3QubGVuQmxvY2suaXNJbmRlZmluaXRlRm9ybSkge1xuICAgICAgICByZXR1cm5PYmplY3QuZXJyb3IgPSBcIkluZGVmaW5pdGUgbGVuZ3RoIGZvcm0gdXNlZCBmb3IgcHJpbWl0aXZlIGVuY29kaW5nIGZvcm1cIjtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIG9mZnNldDogLTEsXG4gICAgICAgICAgICByZXN1bHQ6IHJldHVybk9iamVjdCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgbGV0IG5ld0FTTjFUeXBlID0gQmFzZUJsb2NrO1xuICAgIHN3aXRjaCAocmV0dXJuT2JqZWN0LmlkQmxvY2sudGFnQ2xhc3MpIHtcbiAgICAgICAgY2FzZSAxOlxuICAgICAgICAgICAgaWYgKChyZXR1cm5PYmplY3QuaWRCbG9jay50YWdOdW1iZXIgPj0gMzcpXG4gICAgICAgICAgICAgICAgJiYgKHJldHVybk9iamVjdC5pZEJsb2NrLmlzSGV4T25seSA9PT0gZmFsc2UpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuT2JqZWN0LmVycm9yID0gXCJVTklWRVJTQUwgMzcgYW5kIHVwcGVyIHRhZ3MgYXJlIHJlc2VydmVkIGJ5IEFTTi4xIHN0YW5kYXJkXCI7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAtMSxcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiByZXR1cm5PYmplY3QsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHN3aXRjaCAocmV0dXJuT2JqZWN0LmlkQmxvY2sudGFnTnVtYmVyKSB7XG4gICAgICAgICAgICAgICAgY2FzZSAwOlxuICAgICAgICAgICAgICAgICAgICBpZiAoKHJldHVybk9iamVjdC5pZEJsb2NrLmlzQ29uc3RydWN0ZWQpXG4gICAgICAgICAgICAgICAgICAgICAgICAmJiAocmV0dXJuT2JqZWN0LmxlbkJsb2NrLmxlbmd0aCA+IDApKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm5PYmplY3QuZXJyb3IgPSBcIlR5cGUgW1VOSVZFUlNBTCAwXSBpcyByZXNlcnZlZFwiO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvZmZzZXQ6IC0xLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdDogcmV0dXJuT2JqZWN0LFxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5FbmRPZkNvbnRlbnQ7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMTpcbiAgICAgICAgICAgICAgICAgICAgbmV3QVNOMVR5cGUgPSB0eXBlU3RvcmUuQm9vbGVhbjtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAyOlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5JbnRlZ2VyO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDM6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLkJpdFN0cmluZztcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSA0OlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5PY3RldFN0cmluZztcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSA1OlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5OdWxsO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDY6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLk9iamVjdElkZW50aWZpZXI7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMTA6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLkVudW1lcmF0ZWQ7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMTI6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLlV0ZjhTdHJpbmc7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMTM6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLlJlbGF0aXZlT2JqZWN0SWRlbnRpZmllcjtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAxNDpcbiAgICAgICAgICAgICAgICAgICAgbmV3QVNOMVR5cGUgPSB0eXBlU3RvcmUuVElNRTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAxNTpcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuT2JqZWN0LmVycm9yID0gXCJbVU5JVkVSU0FMIDE1XSBpcyByZXNlcnZlZCBieSBBU04uMSBzdGFuZGFyZFwiO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAtMSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdDogcmV0dXJuT2JqZWN0LFxuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGNhc2UgMTY6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLlNlcXVlbmNlO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDE3OlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5TZXQ7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMTg6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLk51bWVyaWNTdHJpbmc7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMTk6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLlByaW50YWJsZVN0cmluZztcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAyMDpcbiAgICAgICAgICAgICAgICAgICAgbmV3QVNOMVR5cGUgPSB0eXBlU3RvcmUuVGVsZXRleFN0cmluZztcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAyMTpcbiAgICAgICAgICAgICAgICAgICAgbmV3QVNOMVR5cGUgPSB0eXBlU3RvcmUuVmlkZW90ZXhTdHJpbmc7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMjI6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLklBNVN0cmluZztcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAyMzpcbiAgICAgICAgICAgICAgICAgICAgbmV3QVNOMVR5cGUgPSB0eXBlU3RvcmUuVVRDVGltZTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAyNDpcbiAgICAgICAgICAgICAgICAgICAgbmV3QVNOMVR5cGUgPSB0eXBlU3RvcmUuR2VuZXJhbGl6ZWRUaW1lO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDI1OlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5HcmFwaGljU3RyaW5nO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDI2OlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5WaXNpYmxlU3RyaW5nO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDI3OlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5HZW5lcmFsU3RyaW5nO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDI4OlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5Vbml2ZXJzYWxTdHJpbmc7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMjk6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLkNoYXJhY3RlclN0cmluZztcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAzMDpcbiAgICAgICAgICAgICAgICAgICAgbmV3QVNOMVR5cGUgPSB0eXBlU3RvcmUuQm1wU3RyaW5nO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDMxOlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5EQVRFO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDMyOlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5UaW1lT2ZEYXk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMzM6XG4gICAgICAgICAgICAgICAgICAgIG5ld0FTTjFUeXBlID0gdHlwZVN0b3JlLkRhdGVUaW1lO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDM0OlxuICAgICAgICAgICAgICAgICAgICBuZXdBU04xVHlwZSA9IHR5cGVTdG9yZS5EdXJhdGlvbjtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdPYmplY3QgPSByZXR1cm5PYmplY3QuaWRCbG9jay5pc0NvbnN0cnVjdGVkXG4gICAgICAgICAgICAgICAgICAgICAgICA/IG5ldyB0eXBlU3RvcmUuQ29uc3RydWN0ZWQoKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBuZXcgdHlwZVN0b3JlLlByaW1pdGl2ZSgpO1xuICAgICAgICAgICAgICAgICAgICBuZXdPYmplY3QuaWRCbG9jayA9IHJldHVybk9iamVjdC5pZEJsb2NrO1xuICAgICAgICAgICAgICAgICAgICBuZXdPYmplY3QubGVuQmxvY2sgPSByZXR1cm5PYmplY3QubGVuQmxvY2s7XG4gICAgICAgICAgICAgICAgICAgIG5ld09iamVjdC53YXJuaW5ncyA9IHJldHVybk9iamVjdC53YXJuaW5ncztcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuT2JqZWN0ID0gbmV3T2JqZWN0O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDI6XG4gICAgICAgIGNhc2UgMzpcbiAgICAgICAgY2FzZSA0OlxuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICBuZXdBU04xVHlwZSA9IHJldHVybk9iamVjdC5pZEJsb2NrLmlzQ29uc3RydWN0ZWRcbiAgICAgICAgICAgICAgICA/IHR5cGVTdG9yZS5Db25zdHJ1Y3RlZFxuICAgICAgICAgICAgICAgIDogdHlwZVN0b3JlLlByaW1pdGl2ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm5PYmplY3QgPSBsb2NhbENoYW5nZVR5cGUocmV0dXJuT2JqZWN0LCBuZXdBU04xVHlwZSk7XG4gICAgcmVzdWx0T2Zmc2V0ID0gcmV0dXJuT2JqZWN0LmZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCByZXR1cm5PYmplY3QubGVuQmxvY2suaXNJbmRlZmluaXRlRm9ybSA/IGlucHV0TGVuZ3RoIDogcmV0dXJuT2JqZWN0LmxlbkJsb2NrLmxlbmd0aCk7XG4gICAgcmV0dXJuT2JqZWN0LnZhbHVlQmVmb3JlRGVjb2RlVmlldyA9IGlucHV0QnVmZmVyLnN1YmFycmF5KGluY29taW5nT2Zmc2V0LCBpbmNvbWluZ09mZnNldCArIHJldHVybk9iamVjdC5ibG9ja0xlbmd0aCk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgb2Zmc2V0OiByZXN1bHRPZmZzZXQsXG4gICAgICAgIHJlc3VsdDogcmV0dXJuT2JqZWN0LFxuICAgIH07XG59XG5mdW5jdGlvbiBmcm9tQkVSKGlucHV0QnVmZmVyKSB7XG4gICAgaWYgKCFpbnB1dEJ1ZmZlci5ieXRlTGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IG5ldyBCYXNlQmxvY2soe30sIFZhbHVlQmxvY2spO1xuICAgICAgICByZXN1bHQuZXJyb3IgPSBcIklucHV0IGJ1ZmZlciBoYXMgemVybyBsZW5ndGhcIjtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIG9mZnNldDogLTEsXG4gICAgICAgICAgICByZXN1bHQsXG4gICAgICAgIH07XG4gICAgfVxuICAgIHJldHVybiBsb2NhbEZyb21CRVIocHZ0c3V0aWxzLkJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b1VpbnQ4QXJyYXkoaW5wdXRCdWZmZXIpLnNsaWNlKCksIDAsIGlucHV0QnVmZmVyLmJ5dGVMZW5ndGgpO1xufVxuXG5mdW5jdGlvbiBjaGVja0xlbihpbmRlZmluaXRlTGVuZ3RoLCBsZW5ndGgpIHtcbiAgICBpZiAoaW5kZWZpbml0ZUxlbmd0aCkge1xuICAgICAgICByZXR1cm4gMTtcbiAgICB9XG4gICAgcmV0dXJuIGxlbmd0aDtcbn1cbmNsYXNzIExvY2FsQ29uc3RydWN0ZWRWYWx1ZUJsb2NrIGV4dGVuZHMgVmFsdWVCbG9jayB7XG4gICAgY29uc3RydWN0b3IoeyB2YWx1ZSA9IFtdLCBpc0luZGVmaW5pdGVGb3JtID0gZmFsc2UsIC4uLnBhcmFtZXRlcnMgfSA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLnZhbHVlID0gdmFsdWU7XG4gICAgICAgIHRoaXMuaXNJbmRlZmluaXRlRm9ybSA9IGlzSW5kZWZpbml0ZUZvcm07XG4gICAgfVxuICAgIGZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkge1xuICAgICAgICBjb25zdCB2aWV3ID0gcHZ0c3V0aWxzLkJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b1VpbnQ4QXJyYXkoaW5wdXRCdWZmZXIpO1xuICAgICAgICBpZiAoIWNoZWNrQnVmZmVyUGFyYW1zKHRoaXMsIHZpZXcsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkpIHtcbiAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnZhbHVlQmVmb3JlRGVjb2RlVmlldyA9IHZpZXcuc3ViYXJyYXkoaW5wdXRPZmZzZXQsIGlucHV0T2Zmc2V0ICsgaW5wdXRMZW5ndGgpO1xuICAgICAgICBpZiAodGhpcy52YWx1ZUJlZm9yZURlY29kZVZpZXcubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICB0aGlzLndhcm5pbmdzLnB1c2goXCJaZXJvIGJ1ZmZlciBsZW5ndGhcIik7XG4gICAgICAgICAgICByZXR1cm4gaW5wdXRPZmZzZXQ7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IGN1cnJlbnRPZmZzZXQgPSBpbnB1dE9mZnNldDtcbiAgICAgICAgd2hpbGUgKGNoZWNrTGVuKHRoaXMuaXNJbmRlZmluaXRlRm9ybSwgaW5wdXRMZW5ndGgpID4gMCkge1xuICAgICAgICAgICAgY29uc3QgcmV0dXJuT2JqZWN0ID0gbG9jYWxGcm9tQkVSKHZpZXcsIGN1cnJlbnRPZmZzZXQsIGlucHV0TGVuZ3RoKTtcbiAgICAgICAgICAgIGlmIChyZXR1cm5PYmplY3Qub2Zmc2V0ID09PSAtMSkge1xuICAgICAgICAgICAgICAgIHRoaXMuZXJyb3IgPSByZXR1cm5PYmplY3QucmVzdWx0LmVycm9yO1xuICAgICAgICAgICAgICAgIHRoaXMud2FybmluZ3MuY29uY2F0KHJldHVybk9iamVjdC5yZXN1bHQud2FybmluZ3MpO1xuICAgICAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGN1cnJlbnRPZmZzZXQgPSByZXR1cm5PYmplY3Qub2Zmc2V0O1xuICAgICAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCArPSByZXR1cm5PYmplY3QucmVzdWx0LmJsb2NrTGVuZ3RoO1xuICAgICAgICAgICAgaW5wdXRMZW5ndGggLT0gcmV0dXJuT2JqZWN0LnJlc3VsdC5ibG9ja0xlbmd0aDtcbiAgICAgICAgICAgIHRoaXMudmFsdWUucHVzaChyZXR1cm5PYmplY3QucmVzdWx0KTtcbiAgICAgICAgICAgIGlmICh0aGlzLmlzSW5kZWZpbml0ZUZvcm0gJiYgcmV0dXJuT2JqZWN0LnJlc3VsdC5jb25zdHJ1Y3Rvci5OQU1FID09PSBFTkRfT0ZfQ09OVEVOVF9OQU1FKSB7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuaXNJbmRlZmluaXRlRm9ybSkge1xuICAgICAgICAgICAgaWYgKHRoaXMudmFsdWVbdGhpcy52YWx1ZS5sZW5ndGggLSAxXS5jb25zdHJ1Y3Rvci5OQU1FID09PSBFTkRfT0ZfQ09OVEVOVF9OQU1FKSB7XG4gICAgICAgICAgICAgICAgdGhpcy52YWx1ZS5wb3AoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMud2FybmluZ3MucHVzaChcIk5vIEVuZE9mQ29udGVudCBibG9jayBlbmNvZGVkXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBjdXJyZW50T2Zmc2V0O1xuICAgIH1cbiAgICB0b0JFUihzaXplT25seSwgd3JpdGVyKSB7XG4gICAgICAgIGNvbnN0IF93cml0ZXIgPSB3cml0ZXIgfHwgbmV3IFZpZXdXcml0ZXIoKTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnZhbHVlLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICB0aGlzLnZhbHVlW2ldLnRvQkVSKHNpemVPbmx5LCBfd3JpdGVyKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXdyaXRlcikge1xuICAgICAgICAgICAgcmV0dXJuIF93cml0ZXIuZmluYWwoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gRU1QVFlfQlVGRkVSO1xuICAgIH1cbiAgICB0b0pTT04oKSB7XG4gICAgICAgIGNvbnN0IG9iamVjdCA9IHtcbiAgICAgICAgICAgIC4uLnN1cGVyLnRvSlNPTigpLFxuICAgICAgICAgICAgaXNJbmRlZmluaXRlRm9ybTogdGhpcy5pc0luZGVmaW5pdGVGb3JtLFxuICAgICAgICAgICAgdmFsdWU6IFtdLFxuICAgICAgICB9O1xuICAgICAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHRoaXMudmFsdWUpIHtcbiAgICAgICAgICAgIG9iamVjdC52YWx1ZS5wdXNoKHZhbHVlLnRvSlNPTigpKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gb2JqZWN0O1xuICAgIH1cbn1cbkxvY2FsQ29uc3RydWN0ZWRWYWx1ZUJsb2NrLk5BTUUgPSBcIkNvbnN0cnVjdGVkVmFsdWVCbG9ja1wiO1xuXG52YXIgX2EkdjtcbmNsYXNzIENvbnN0cnVjdGVkIGV4dGVuZHMgQmFzZUJsb2NrIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycywgTG9jYWxDb25zdHJ1Y3RlZFZhbHVlQmxvY2spO1xuICAgICAgICB0aGlzLmlkQmxvY2suaXNDb25zdHJ1Y3RlZCA9IHRydWU7XG4gICAgfVxuICAgIGZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkge1xuICAgICAgICB0aGlzLnZhbHVlQmxvY2suaXNJbmRlZmluaXRlRm9ybSA9IHRoaXMubGVuQmxvY2suaXNJbmRlZmluaXRlRm9ybTtcbiAgICAgICAgY29uc3QgcmVzdWx0T2Zmc2V0ID0gdGhpcy52YWx1ZUJsb2NrLmZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCAodGhpcy5sZW5CbG9jay5pc0luZGVmaW5pdGVGb3JtKSA/IGlucHV0TGVuZ3RoIDogdGhpcy5sZW5CbG9jay5sZW5ndGgpO1xuICAgICAgICBpZiAocmVzdWx0T2Zmc2V0ID09PSAtMSkge1xuICAgICAgICAgICAgdGhpcy5lcnJvciA9IHRoaXMudmFsdWVCbG9jay5lcnJvcjtcbiAgICAgICAgICAgIHJldHVybiByZXN1bHRPZmZzZXQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCF0aGlzLmlkQmxvY2suZXJyb3IubGVuZ3RoKVxuICAgICAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCArPSB0aGlzLmlkQmxvY2suYmxvY2tMZW5ndGg7XG4gICAgICAgIGlmICghdGhpcy5sZW5CbG9jay5lcnJvci5sZW5ndGgpXG4gICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoICs9IHRoaXMubGVuQmxvY2suYmxvY2tMZW5ndGg7XG4gICAgICAgIGlmICghdGhpcy52YWx1ZUJsb2NrLmVycm9yLmxlbmd0aClcbiAgICAgICAgICAgIHRoaXMuYmxvY2tMZW5ndGggKz0gdGhpcy52YWx1ZUJsb2NrLmJsb2NrTGVuZ3RoO1xuICAgICAgICByZXR1cm4gcmVzdWx0T2Zmc2V0O1xuICAgIH1cbiAgICBvbkFzY2lpRW5jb2RpbmcoKSB7XG4gICAgICAgIGNvbnN0IHZhbHVlcyA9IFtdO1xuICAgICAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHRoaXMudmFsdWVCbG9jay52YWx1ZSkge1xuICAgICAgICAgICAgdmFsdWVzLnB1c2godmFsdWUudG9TdHJpbmcoXCJhc2NpaVwiKS5zcGxpdChcIlxcblwiKS5tYXAoKG8pID0+IGAgICR7b31gKS5qb2luKFwiXFxuXCIpKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBibG9ja05hbWUgPSB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPT09IDNcbiAgICAgICAgICAgID8gYFske3RoaXMuaWRCbG9jay50YWdOdW1iZXJ9XWBcbiAgICAgICAgICAgIDogdGhpcy5jb25zdHJ1Y3Rvci5OQU1FO1xuICAgICAgICByZXR1cm4gdmFsdWVzLmxlbmd0aFxuICAgICAgICAgICAgPyBgJHtibG9ja05hbWV9IDpcXG4ke3ZhbHVlcy5qb2luKFwiXFxuXCIpfWBcbiAgICAgICAgICAgIDogYCR7YmxvY2tOYW1lfSA6YDtcbiAgICB9XG59XG5fYSR2ID0gQ29uc3RydWN0ZWQ7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5Db25zdHJ1Y3RlZCA9IF9hJHY7XG59KSgpO1xuQ29uc3RydWN0ZWQuTkFNRSA9IFwiQ09OU1RSVUNURURcIjtcblxuY2xhc3MgTG9jYWxFbmRPZkNvbnRlbnRWYWx1ZUJsb2NrIGV4dGVuZHMgVmFsdWVCbG9jayB7XG4gICAgZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsIF9pbnB1dExlbmd0aCkge1xuICAgICAgICByZXR1cm4gaW5wdXRPZmZzZXQ7XG4gICAgfVxuICAgIHRvQkVSKF9zaXplT25seSkge1xuICAgICAgICByZXR1cm4gRU1QVFlfQlVGRkVSO1xuICAgIH1cbn1cbkxvY2FsRW5kT2ZDb250ZW50VmFsdWVCbG9jay5vdmVycmlkZSA9IFwiRW5kT2ZDb250ZW50VmFsdWVCbG9ja1wiO1xuXG52YXIgX2EkdTtcbmNsYXNzIEVuZE9mQ29udGVudCBleHRlbmRzIEJhc2VCbG9jayB7XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMsIExvY2FsRW5kT2ZDb250ZW50VmFsdWVCbG9jayk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAwO1xuICAgIH1cbn1cbl9hJHUgPSBFbmRPZkNvbnRlbnQ7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5FbmRPZkNvbnRlbnQgPSBfYSR1O1xufSkoKTtcbkVuZE9mQ29udGVudC5OQU1FID0gRU5EX09GX0NPTlRFTlRfTkFNRTtcblxudmFyIF9hJHQ7XG5jbGFzcyBOdWxsIGV4dGVuZHMgQmFzZUJsb2NrIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycywgVmFsdWVCbG9jayk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSA1O1xuICAgIH1cbiAgICBmcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgaWYgKHRoaXMubGVuQmxvY2subGVuZ3RoID4gMClcbiAgICAgICAgICAgIHRoaXMud2FybmluZ3MucHVzaChcIk5vbi16ZXJvIGxlbmd0aCBvZiB2YWx1ZSBibG9jayBmb3IgTnVsbCB0eXBlXCIpO1xuICAgICAgICBpZiAoIXRoaXMuaWRCbG9jay5lcnJvci5sZW5ndGgpXG4gICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoICs9IHRoaXMuaWRCbG9jay5ibG9ja0xlbmd0aDtcbiAgICAgICAgaWYgKCF0aGlzLmxlbkJsb2NrLmVycm9yLmxlbmd0aClcbiAgICAgICAgICAgIHRoaXMuYmxvY2tMZW5ndGggKz0gdGhpcy5sZW5CbG9jay5ibG9ja0xlbmd0aDtcbiAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCArPSBpbnB1dExlbmd0aDtcbiAgICAgICAgaWYgKChpbnB1dE9mZnNldCArIGlucHV0TGVuZ3RoKSA+IGlucHV0QnVmZmVyLmJ5dGVMZW5ndGgpIHtcbiAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIkVuZCBvZiBpbnB1dCByZWFjaGVkIGJlZm9yZSBtZXNzYWdlIHdhcyBmdWxseSBkZWNvZGVkIChpbmNvbnNpc3RlbnQgb2Zmc2V0IGFuZCBsZW5ndGggdmFsdWVzKVwiO1xuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAoaW5wdXRPZmZzZXQgKyBpbnB1dExlbmd0aCk7XG4gICAgfVxuICAgIHRvQkVSKHNpemVPbmx5LCB3cml0ZXIpIHtcbiAgICAgICAgY29uc3QgcmV0QnVmID0gbmV3IEFycmF5QnVmZmVyKDIpO1xuICAgICAgICBpZiAoIXNpemVPbmx5KSB7XG4gICAgICAgICAgICBjb25zdCByZXRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkocmV0QnVmKTtcbiAgICAgICAgICAgIHJldFZpZXdbMF0gPSAweDA1O1xuICAgICAgICAgICAgcmV0Vmlld1sxXSA9IDB4MDA7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHdyaXRlcikge1xuICAgICAgICAgICAgd3JpdGVyLndyaXRlKHJldEJ1Zik7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJldEJ1ZjtcbiAgICB9XG4gICAgb25Bc2NpaUVuY29kaW5nKCkge1xuICAgICAgICByZXR1cm4gYCR7dGhpcy5jb25zdHJ1Y3Rvci5OQU1FfWA7XG4gICAgfVxufVxuX2EkdCA9IE51bGw7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5OdWxsID0gX2EkdDtcbn0pKCk7XG5OdWxsLk5BTUUgPSBcIk5VTExcIjtcblxuY2xhc3MgTG9jYWxCb29sZWFuVmFsdWVCbG9jayBleHRlbmRzIEhleEJsb2NrKFZhbHVlQmxvY2spIHtcbiAgICBnZXQgdmFsdWUoKSB7XG4gICAgICAgIGZvciAoY29uc3Qgb2N0ZXQgb2YgdGhpcy52YWx1ZUhleFZpZXcpIHtcbiAgICAgICAgICAgIGlmIChvY3RldCA+IDApIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHNldCB2YWx1ZSh2YWx1ZSkge1xuICAgICAgICB0aGlzLnZhbHVlSGV4Vmlld1swXSA9IHZhbHVlID8gMHhGRiA6IDB4MDA7XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKHsgdmFsdWUsIC4uLnBhcmFtZXRlcnMgfSA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICBpZiAocGFyYW1ldGVycy52YWx1ZUhleCkge1xuICAgICAgICAgICAgdGhpcy52YWx1ZUhleFZpZXcgPSBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheShwYXJhbWV0ZXJzLnZhbHVlSGV4KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMudmFsdWVIZXhWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoMSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHZhbHVlKSB7XG4gICAgICAgICAgICB0aGlzLnZhbHVlID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IGlucHV0VmlldyA9IHB2dHN1dGlscy5CdWZmZXJTb3VyY2VDb252ZXJ0ZXIudG9VaW50OEFycmF5KGlucHV0QnVmZmVyKTtcbiAgICAgICAgaWYgKCFjaGVja0J1ZmZlclBhcmFtcyh0aGlzLCBpbnB1dFZpZXcsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkpIHtcbiAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnZhbHVlSGV4VmlldyA9IGlucHV0Vmlldy5zdWJhcnJheShpbnB1dE9mZnNldCwgaW5wdXRPZmZzZXQgKyBpbnB1dExlbmd0aCk7XG4gICAgICAgIGlmIChpbnB1dExlbmd0aCA+IDEpXG4gICAgICAgICAgICB0aGlzLndhcm5pbmdzLnB1c2goXCJCb29sZWFuIHZhbHVlIGVuY29kZWQgaW4gbW9yZSB0aGVuIDEgb2N0ZXRcIik7XG4gICAgICAgIHRoaXMuaXNIZXhPbmx5ID0gdHJ1ZTtcbiAgICAgICAgcHZ1dGlscy51dGlsRGVjb2RlVEMuY2FsbCh0aGlzKTtcbiAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCA9IGlucHV0TGVuZ3RoO1xuICAgICAgICByZXR1cm4gKGlucHV0T2Zmc2V0ICsgaW5wdXRMZW5ndGgpO1xuICAgIH1cbiAgICB0b0JFUigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVIZXhWaWV3LnNsaWNlKCk7XG4gICAgfVxuICAgIHRvSlNPTigpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN1cGVyLnRvSlNPTigpLFxuICAgICAgICAgICAgdmFsdWU6IHRoaXMudmFsdWUsXG4gICAgICAgIH07XG4gICAgfVxufVxuTG9jYWxCb29sZWFuVmFsdWVCbG9jay5OQU1FID0gXCJCb29sZWFuVmFsdWVCbG9ja1wiO1xuXG52YXIgX2EkcztcbmNsYXNzIEJvb2xlYW4gZXh0ZW5kcyBCYXNlQmxvY2sge1xuICAgIGdldFZhbHVlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy52YWx1ZUJsb2NrLnZhbHVlO1xuICAgIH1cbiAgICBzZXRWYWx1ZSh2YWx1ZSkge1xuICAgICAgICB0aGlzLnZhbHVlQmxvY2sudmFsdWUgPSB2YWx1ZTtcbiAgICB9XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMsIExvY2FsQm9vbGVhblZhbHVlQmxvY2spO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gMTtcbiAgICB9XG4gICAgb25Bc2NpaUVuY29kaW5nKCkge1xuICAgICAgICByZXR1cm4gYCR7dGhpcy5jb25zdHJ1Y3Rvci5OQU1FfSA6ICR7dGhpcy5nZXRWYWx1ZX1gO1xuICAgIH1cbn1cbl9hJHMgPSBCb29sZWFuO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuQm9vbGVhbiA9IF9hJHM7XG59KSgpO1xuQm9vbGVhbi5OQU1FID0gXCJCT09MRUFOXCI7XG5cbmNsYXNzIExvY2FsT2N0ZXRTdHJpbmdWYWx1ZUJsb2NrIGV4dGVuZHMgSGV4QmxvY2soTG9jYWxDb25zdHJ1Y3RlZFZhbHVlQmxvY2spIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGlzQ29uc3RydWN0ZWQgPSBmYWxzZSwgLi4ucGFyYW1ldGVycyB9ID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMuaXNDb25zdHJ1Y3RlZCA9IGlzQ29uc3RydWN0ZWQ7XG4gICAgfVxuICAgIGZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkge1xuICAgICAgICBsZXQgcmVzdWx0T2Zmc2V0ID0gMDtcbiAgICAgICAgaWYgKHRoaXMuaXNDb25zdHJ1Y3RlZCkge1xuICAgICAgICAgICAgdGhpcy5pc0hleE9ubHkgPSBmYWxzZTtcbiAgICAgICAgICAgIHJlc3VsdE9mZnNldCA9IExvY2FsQ29uc3RydWN0ZWRWYWx1ZUJsb2NrLnByb3RvdHlwZS5mcm9tQkVSLmNhbGwodGhpcywgaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCk7XG4gICAgICAgICAgICBpZiAocmVzdWx0T2Zmc2V0ID09PSAtMSlcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0T2Zmc2V0O1xuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnZhbHVlLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgY3VycmVudEJsb2NrTmFtZSA9IHRoaXMudmFsdWVbaV0uY29uc3RydWN0b3IuTkFNRTtcbiAgICAgICAgICAgICAgICBpZiAoY3VycmVudEJsb2NrTmFtZSA9PT0gRU5EX09GX0NPTlRFTlRfTkFNRSkge1xuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5pc0luZGVmaW5pdGVGb3JtKVxuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvciA9IFwiRW5kT2ZDb250ZW50IGlzIHVuZXhwZWN0ZWQsIE9DVEVUIFNUUklORyBtYXkgY29uc2lzdHMgb2YgT0NURVQgU1RSSU5HcyBvbmx5XCI7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gLTE7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRCbG9ja05hbWUgIT09IE9DVEVUX1NUUklOR19OQU1FKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIk9DVEVUIFNUUklORyBtYXkgY29uc2lzdHMgb2YgT0NURVQgU1RSSU5HcyBvbmx5XCI7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmlzSGV4T25seSA9IHRydWU7XG4gICAgICAgICAgICByZXN1bHRPZmZzZXQgPSBzdXBlci5mcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpO1xuICAgICAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCA9IGlucHV0TGVuZ3RoO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHRPZmZzZXQ7XG4gICAgfVxuICAgIHRvQkVSKHNpemVPbmx5LCB3cml0ZXIpIHtcbiAgICAgICAgaWYgKHRoaXMuaXNDb25zdHJ1Y3RlZClcbiAgICAgICAgICAgIHJldHVybiBMb2NhbENvbnN0cnVjdGVkVmFsdWVCbG9jay5wcm90b3R5cGUudG9CRVIuY2FsbCh0aGlzLCBzaXplT25seSwgd3JpdGVyKTtcbiAgICAgICAgcmV0dXJuIHNpemVPbmx5XG4gICAgICAgICAgICA/IG5ldyBBcnJheUJ1ZmZlcih0aGlzLnZhbHVlSGV4Vmlldy5ieXRlTGVuZ3RoKVxuICAgICAgICAgICAgOiB0aGlzLnZhbHVlSGV4Vmlldy5zbGljZSgpLmJ1ZmZlcjtcbiAgICB9XG4gICAgdG9KU09OKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3VwZXIudG9KU09OKCksXG4gICAgICAgICAgICBpc0NvbnN0cnVjdGVkOiB0aGlzLmlzQ29uc3RydWN0ZWQsXG4gICAgICAgIH07XG4gICAgfVxufVxuTG9jYWxPY3RldFN0cmluZ1ZhbHVlQmxvY2suTkFNRSA9IFwiT2N0ZXRTdHJpbmdWYWx1ZUJsb2NrXCI7XG5cbnZhciBfYSRyO1xuY2xhc3MgT2N0ZXRTdHJpbmcgZXh0ZW5kcyBCYXNlQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHsgaWRCbG9jayA9IHt9LCBsZW5CbG9jayA9IHt9LCAuLi5wYXJhbWV0ZXJzIH0gPSB7fSkge1xuICAgICAgICB2YXIgX2IsIF9jO1xuICAgICAgICAoX2IgPSBwYXJhbWV0ZXJzLmlzQ29uc3RydWN0ZWQpICE9PSBudWxsICYmIF9iICE9PSB2b2lkIDAgPyBfYiA6IChwYXJhbWV0ZXJzLmlzQ29uc3RydWN0ZWQgPSAhISgoX2MgPSBwYXJhbWV0ZXJzLnZhbHVlKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MubGVuZ3RoKSk7XG4gICAgICAgIHN1cGVyKHtcbiAgICAgICAgICAgIGlkQmxvY2s6IHtcbiAgICAgICAgICAgICAgICBpc0NvbnN0cnVjdGVkOiBwYXJhbWV0ZXJzLmlzQ29uc3RydWN0ZWQsXG4gICAgICAgICAgICAgICAgLi4uaWRCbG9jayxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBsZW5CbG9jazoge1xuICAgICAgICAgICAgICAgIC4uLmxlbkJsb2NrLFxuICAgICAgICAgICAgICAgIGlzSW5kZWZpbml0ZUZvcm06ICEhcGFyYW1ldGVycy5pc0luZGVmaW5pdGVGb3JtLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIC4uLnBhcmFtZXRlcnMsXG4gICAgICAgIH0sIExvY2FsT2N0ZXRTdHJpbmdWYWx1ZUJsb2NrKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDQ7XG4gICAgfVxuICAgIGZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkge1xuICAgICAgICB0aGlzLnZhbHVlQmxvY2suaXNDb25zdHJ1Y3RlZCA9IHRoaXMuaWRCbG9jay5pc0NvbnN0cnVjdGVkO1xuICAgICAgICB0aGlzLnZhbHVlQmxvY2suaXNJbmRlZmluaXRlRm9ybSA9IHRoaXMubGVuQmxvY2suaXNJbmRlZmluaXRlRm9ybTtcbiAgICAgICAgaWYgKGlucHV0TGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5pZEJsb2NrLmVycm9yLmxlbmd0aCA9PT0gMClcbiAgICAgICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoICs9IHRoaXMuaWRCbG9jay5ibG9ja0xlbmd0aDtcbiAgICAgICAgICAgIGlmICh0aGlzLmxlbkJsb2NrLmVycm9yLmxlbmd0aCA9PT0gMClcbiAgICAgICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoICs9IHRoaXMubGVuQmxvY2suYmxvY2tMZW5ndGg7XG4gICAgICAgICAgICByZXR1cm4gaW5wdXRPZmZzZXQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCF0aGlzLnZhbHVlQmxvY2suaXNDb25zdHJ1Y3RlZCkge1xuICAgICAgICAgICAgY29uc3QgdmlldyA9IGlucHV0QnVmZmVyIGluc3RhbmNlb2YgQXJyYXlCdWZmZXIgPyBuZXcgVWludDhBcnJheShpbnB1dEJ1ZmZlcikgOiBpbnB1dEJ1ZmZlcjtcbiAgICAgICAgICAgIGNvbnN0IGJ1ZiA9IHZpZXcuc3ViYXJyYXkoaW5wdXRPZmZzZXQsIGlucHV0T2Zmc2V0ICsgaW5wdXRMZW5ndGgpO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBpZiAoYnVmLmJ5dGVMZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYXNuID0gbG9jYWxGcm9tQkVSKGJ1ZiwgMCwgYnVmLmJ5dGVMZW5ndGgpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoYXNuLm9mZnNldCAhPT0gLTEgJiYgYXNuLm9mZnNldCA9PT0gaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsdWVCbG9jay52YWx1ZSA9IFthc24ucmVzdWx0XTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIHtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc3VwZXIuZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKTtcbiAgICB9XG4gICAgb25Bc2NpaUVuY29kaW5nKCkge1xuICAgICAgICBpZiAodGhpcy52YWx1ZUJsb2NrLmlzQ29uc3RydWN0ZWQgfHwgKHRoaXMudmFsdWVCbG9jay52YWx1ZSAmJiB0aGlzLnZhbHVlQmxvY2sudmFsdWUubGVuZ3RoKSkge1xuICAgICAgICAgICAgcmV0dXJuIENvbnN0cnVjdGVkLnByb3RvdHlwZS5vbkFzY2lpRW5jb2RpbmcuY2FsbCh0aGlzKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBuYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5OQU1FO1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHB2dHN1dGlscy5Db252ZXJ0LlRvSGV4KHRoaXMudmFsdWVCbG9jay52YWx1ZUhleFZpZXcpO1xuICAgICAgICByZXR1cm4gYCR7bmFtZX0gOiAke3ZhbHVlfWA7XG4gICAgfVxuICAgIGdldFZhbHVlKCkge1xuICAgICAgICBpZiAoIXRoaXMuaWRCbG9jay5pc0NvbnN0cnVjdGVkKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy52YWx1ZUJsb2NrLnZhbHVlSGV4Vmlldy5zbGljZSgpLmJ1ZmZlcjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcnJheSA9IFtdO1xuICAgICAgICBmb3IgKGNvbnN0IGNvbnRlbnQgb2YgdGhpcy52YWx1ZUJsb2NrLnZhbHVlKSB7XG4gICAgICAgICAgICBpZiAoY29udGVudCBpbnN0YW5jZW9mIF9hJHIpIHtcbiAgICAgICAgICAgICAgICBhcnJheS5wdXNoKGNvbnRlbnQudmFsdWVCbG9jay52YWx1ZUhleFZpZXcpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLmNvbmNhdChhcnJheSk7XG4gICAgfVxufVxuX2EkciA9IE9jdGV0U3RyaW5nO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuT2N0ZXRTdHJpbmcgPSBfYSRyO1xufSkoKTtcbk9jdGV0U3RyaW5nLk5BTUUgPSBPQ1RFVF9TVFJJTkdfTkFNRTtcblxuY2xhc3MgTG9jYWxCaXRTdHJpbmdWYWx1ZUJsb2NrIGV4dGVuZHMgSGV4QmxvY2soTG9jYWxDb25zdHJ1Y3RlZFZhbHVlQmxvY2spIHtcbiAgICBjb25zdHJ1Y3Rvcih7IHVudXNlZEJpdHMgPSAwLCBpc0NvbnN0cnVjdGVkID0gZmFsc2UsIC4uLnBhcmFtZXRlcnMgfSA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLnVudXNlZEJpdHMgPSB1bnVzZWRCaXRzO1xuICAgICAgICB0aGlzLmlzQ29uc3RydWN0ZWQgPSBpc0NvbnN0cnVjdGVkO1xuICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoID0gdGhpcy52YWx1ZUhleFZpZXcuYnl0ZUxlbmd0aDtcbiAgICB9XG4gICAgZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKSB7XG4gICAgICAgIGlmICghaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbnB1dE9mZnNldDtcbiAgICAgICAgfVxuICAgICAgICBsZXQgcmVzdWx0T2Zmc2V0ID0gLTE7XG4gICAgICAgIGlmICh0aGlzLmlzQ29uc3RydWN0ZWQpIHtcbiAgICAgICAgICAgIHJlc3VsdE9mZnNldCA9IExvY2FsQ29uc3RydWN0ZWRWYWx1ZUJsb2NrLnByb3RvdHlwZS5mcm9tQkVSLmNhbGwodGhpcywgaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCk7XG4gICAgICAgICAgICBpZiAocmVzdWx0T2Zmc2V0ID09PSAtMSlcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0T2Zmc2V0O1xuICAgICAgICAgICAgZm9yIChjb25zdCB2YWx1ZSBvZiB0aGlzLnZhbHVlKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgY3VycmVudEJsb2NrTmFtZSA9IHZhbHVlLmNvbnN0cnVjdG9yLk5BTUU7XG4gICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRCbG9ja05hbWUgPT09IEVORF9PRl9DT05URU5UX05BTUUpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNJbmRlZmluaXRlRm9ybSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIkVuZE9mQ29udGVudCBpcyB1bmV4cGVjdGVkLCBCSVQgU1RSSU5HIG1heSBjb25zaXN0cyBvZiBCSVQgU1RSSU5HcyBvbmx5XCI7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gLTE7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRCbG9ja05hbWUgIT09IEJJVF9TVFJJTkdfTkFNRSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yID0gXCJCSVQgU1RSSU5HIG1heSBjb25zaXN0cyBvZiBCSVQgU1RSSU5HcyBvbmx5XCI7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCbG9jayA9IHZhbHVlLnZhbHVlQmxvY2s7XG4gICAgICAgICAgICAgICAgaWYgKCh0aGlzLnVudXNlZEJpdHMgPiAwKSAmJiAodmFsdWVCbG9jay51bnVzZWRCaXRzID4gMCkpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvciA9IFwiVXNpbmcgb2YgXFxcInVudXNlZCBiaXRzXFxcIiBpbnNpZGUgY29uc3RydWN0aXZlIEJJVCBTVFJJTkcgYWxsb3dlZCBmb3IgbGVhc3Qgb25lIG9ubHlcIjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLnVudXNlZEJpdHMgPSB2YWx1ZUJsb2NrLnVudXNlZEJpdHM7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcmVzdWx0T2Zmc2V0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGlucHV0VmlldyA9IHB2dHN1dGlscy5CdWZmZXJTb3VyY2VDb252ZXJ0ZXIudG9VaW50OEFycmF5KGlucHV0QnVmZmVyKTtcbiAgICAgICAgaWYgKCFjaGVja0J1ZmZlclBhcmFtcyh0aGlzLCBpbnB1dFZpZXcsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkpIHtcbiAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBpbnRCdWZmZXIgPSBpbnB1dFZpZXcuc3ViYXJyYXkoaW5wdXRPZmZzZXQsIGlucHV0T2Zmc2V0ICsgaW5wdXRMZW5ndGgpO1xuICAgICAgICB0aGlzLnVudXNlZEJpdHMgPSBpbnRCdWZmZXJbMF07XG4gICAgICAgIGlmICh0aGlzLnVudXNlZEJpdHMgPiA3KSB7XG4gICAgICAgICAgICB0aGlzLmVycm9yID0gXCJVbnVzZWQgYml0cyBmb3IgQml0U3RyaW5nIG11c3QgYmUgaW4gcmFuZ2UgMC03XCI7XG4gICAgICAgICAgICByZXR1cm4gLTE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCF0aGlzLnVudXNlZEJpdHMpIHtcbiAgICAgICAgICAgIGNvbnN0IGJ1ZiA9IGludEJ1ZmZlci5zdWJhcnJheSgxKTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgaWYgKGJ1Zi5ieXRlTGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFzbiA9IGxvY2FsRnJvbUJFUihidWYsIDAsIGJ1Zi5ieXRlTGVuZ3RoKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGFzbi5vZmZzZXQgIT09IC0xICYmIGFzbi5vZmZzZXQgPT09IChpbnB1dExlbmd0aCAtIDEpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbHVlID0gW2Fzbi5yZXN1bHRdO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2gge1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMudmFsdWVIZXhWaWV3ID0gaW50QnVmZmVyLnN1YmFycmF5KDEpO1xuICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoID0gaW50QnVmZmVyLmxlbmd0aDtcbiAgICAgICAgcmV0dXJuIChpbnB1dE9mZnNldCArIGlucHV0TGVuZ3RoKTtcbiAgICB9XG4gICAgdG9CRVIoc2l6ZU9ubHksIHdyaXRlcikge1xuICAgICAgICBpZiAodGhpcy5pc0NvbnN0cnVjdGVkKSB7XG4gICAgICAgICAgICByZXR1cm4gTG9jYWxDb25zdHJ1Y3RlZFZhbHVlQmxvY2sucHJvdG90eXBlLnRvQkVSLmNhbGwodGhpcywgc2l6ZU9ubHksIHdyaXRlcik7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHNpemVPbmx5KSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IEFycmF5QnVmZmVyKHRoaXMudmFsdWVIZXhWaWV3LmJ5dGVMZW5ndGggKyAxKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXRoaXMudmFsdWVIZXhWaWV3LmJ5dGVMZW5ndGgpIHtcbiAgICAgICAgICAgIHJldHVybiBFTVBUWV9CVUZGRVI7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmV0VmlldyA9IG5ldyBVaW50OEFycmF5KHRoaXMudmFsdWVIZXhWaWV3Lmxlbmd0aCArIDEpO1xuICAgICAgICByZXRWaWV3WzBdID0gdGhpcy51bnVzZWRCaXRzO1xuICAgICAgICByZXRWaWV3LnNldCh0aGlzLnZhbHVlSGV4VmlldywgMSk7XG4gICAgICAgIHJldHVybiByZXRWaWV3LmJ1ZmZlcjtcbiAgICB9XG4gICAgdG9KU09OKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3VwZXIudG9KU09OKCksXG4gICAgICAgICAgICB1bnVzZWRCaXRzOiB0aGlzLnVudXNlZEJpdHMsXG4gICAgICAgICAgICBpc0NvbnN0cnVjdGVkOiB0aGlzLmlzQ29uc3RydWN0ZWQsXG4gICAgICAgIH07XG4gICAgfVxufVxuTG9jYWxCaXRTdHJpbmdWYWx1ZUJsb2NrLk5BTUUgPSBcIkJpdFN0cmluZ1ZhbHVlQmxvY2tcIjtcblxudmFyIF9hJHE7XG5jbGFzcyBCaXRTdHJpbmcgZXh0ZW5kcyBCYXNlQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHsgaWRCbG9jayA9IHt9LCBsZW5CbG9jayA9IHt9LCAuLi5wYXJhbWV0ZXJzIH0gPSB7fSkge1xuICAgICAgICB2YXIgX2IsIF9jO1xuICAgICAgICAoX2IgPSBwYXJhbWV0ZXJzLmlzQ29uc3RydWN0ZWQpICE9PSBudWxsICYmIF9iICE9PSB2b2lkIDAgPyBfYiA6IChwYXJhbWV0ZXJzLmlzQ29uc3RydWN0ZWQgPSAhISgoX2MgPSBwYXJhbWV0ZXJzLnZhbHVlKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MubGVuZ3RoKSk7XG4gICAgICAgIHN1cGVyKHtcbiAgICAgICAgICAgIGlkQmxvY2s6IHtcbiAgICAgICAgICAgICAgICBpc0NvbnN0cnVjdGVkOiBwYXJhbWV0ZXJzLmlzQ29uc3RydWN0ZWQsXG4gICAgICAgICAgICAgICAgLi4uaWRCbG9jayxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBsZW5CbG9jazoge1xuICAgICAgICAgICAgICAgIC4uLmxlbkJsb2NrLFxuICAgICAgICAgICAgICAgIGlzSW5kZWZpbml0ZUZvcm06ICEhcGFyYW1ldGVycy5pc0luZGVmaW5pdGVGb3JtLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIC4uLnBhcmFtZXRlcnMsXG4gICAgICAgIH0sIExvY2FsQml0U3RyaW5nVmFsdWVCbG9jayk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAzO1xuICAgIH1cbiAgICBmcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgdGhpcy52YWx1ZUJsb2NrLmlzQ29uc3RydWN0ZWQgPSB0aGlzLmlkQmxvY2suaXNDb25zdHJ1Y3RlZDtcbiAgICAgICAgdGhpcy52YWx1ZUJsb2NrLmlzSW5kZWZpbml0ZUZvcm0gPSB0aGlzLmxlbkJsb2NrLmlzSW5kZWZpbml0ZUZvcm07XG4gICAgICAgIHJldHVybiBzdXBlci5mcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpO1xuICAgIH1cbiAgICBvbkFzY2lpRW5jb2RpbmcoKSB7XG4gICAgICAgIGlmICh0aGlzLnZhbHVlQmxvY2suaXNDb25zdHJ1Y3RlZCB8fCAodGhpcy52YWx1ZUJsb2NrLnZhbHVlICYmIHRoaXMudmFsdWVCbG9jay52YWx1ZS5sZW5ndGgpKSB7XG4gICAgICAgICAgICByZXR1cm4gQ29uc3RydWN0ZWQucHJvdG90eXBlLm9uQXNjaWlFbmNvZGluZy5jYWxsKHRoaXMpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29uc3QgYml0cyA9IFtdO1xuICAgICAgICAgICAgY29uc3QgdmFsdWVIZXggPSB0aGlzLnZhbHVlQmxvY2sudmFsdWVIZXhWaWV3O1xuICAgICAgICAgICAgZm9yIChjb25zdCBieXRlIG9mIHZhbHVlSGV4KSB7XG4gICAgICAgICAgICAgICAgYml0cy5wdXNoKGJ5dGUudG9TdHJpbmcoMikucGFkU3RhcnQoOCwgXCIwXCIpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGJpdHNTdHIgPSBiaXRzLmpvaW4oXCJcIik7XG4gICAgICAgICAgICBjb25zdCBuYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5OQU1FO1xuICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBiaXRzU3RyLnN1YnN0cmluZygwLCBiaXRzU3RyLmxlbmd0aCAtIHRoaXMudmFsdWVCbG9jay51bnVzZWRCaXRzKTtcbiAgICAgICAgICAgIHJldHVybiBgJHtuYW1lfSA6ICR7dmFsdWV9YDtcbiAgICAgICAgfVxuICAgIH1cbn1cbl9hJHEgPSBCaXRTdHJpbmc7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5CaXRTdHJpbmcgPSBfYSRxO1xufSkoKTtcbkJpdFN0cmluZy5OQU1FID0gQklUX1NUUklOR19OQU1FO1xuXG52YXIgX2EkcDtcbmZ1bmN0aW9uIHZpZXdBZGQoZmlyc3QsIHNlY29uZCkge1xuICAgIGNvbnN0IGMgPSBuZXcgVWludDhBcnJheShbMF0pO1xuICAgIGNvbnN0IGZpcnN0VmlldyA9IG5ldyBVaW50OEFycmF5KGZpcnN0KTtcbiAgICBjb25zdCBzZWNvbmRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoc2Vjb25kKTtcbiAgICBsZXQgZmlyc3RWaWV3Q29weSA9IGZpcnN0Vmlldy5zbGljZSgwKTtcbiAgICBjb25zdCBmaXJzdFZpZXdDb3B5TGVuZ3RoID0gZmlyc3RWaWV3Q29weS5sZW5ndGggLSAxO1xuICAgIGNvbnN0IHNlY29uZFZpZXdDb3B5ID0gc2Vjb25kVmlldy5zbGljZSgwKTtcbiAgICBjb25zdCBzZWNvbmRWaWV3Q29weUxlbmd0aCA9IHNlY29uZFZpZXdDb3B5Lmxlbmd0aCAtIDE7XG4gICAgbGV0IHZhbHVlID0gMDtcbiAgICBjb25zdCBtYXggPSAoc2Vjb25kVmlld0NvcHlMZW5ndGggPCBmaXJzdFZpZXdDb3B5TGVuZ3RoKSA/IGZpcnN0Vmlld0NvcHlMZW5ndGggOiBzZWNvbmRWaWV3Q29weUxlbmd0aDtcbiAgICBsZXQgY291bnRlciA9IDA7XG4gICAgZm9yIChsZXQgaSA9IG1heDsgaSA+PSAwOyBpLS0sIGNvdW50ZXIrKykge1xuICAgICAgICBzd2l0Y2ggKHRydWUpIHtcbiAgICAgICAgICAgIGNhc2UgKGNvdW50ZXIgPCBzZWNvbmRWaWV3Q29weS5sZW5ndGgpOlxuICAgICAgICAgICAgICAgIHZhbHVlID0gZmlyc3RWaWV3Q29weVtmaXJzdFZpZXdDb3B5TGVuZ3RoIC0gY291bnRlcl0gKyBzZWNvbmRWaWV3Q29weVtzZWNvbmRWaWV3Q29weUxlbmd0aCAtIGNvdW50ZXJdICsgY1swXTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgdmFsdWUgPSBmaXJzdFZpZXdDb3B5W2ZpcnN0Vmlld0NvcHlMZW5ndGggLSBjb3VudGVyXSArIGNbMF07XG4gICAgICAgIH1cbiAgICAgICAgY1swXSA9IHZhbHVlIC8gMTA7XG4gICAgICAgIHN3aXRjaCAodHJ1ZSkge1xuICAgICAgICAgICAgY2FzZSAoY291bnRlciA+PSBmaXJzdFZpZXdDb3B5Lmxlbmd0aCk6XG4gICAgICAgICAgICAgICAgZmlyc3RWaWV3Q29weSA9IHB2dXRpbHMudXRpbENvbmNhdFZpZXcobmV3IFVpbnQ4QXJyYXkoW3ZhbHVlICUgMTBdKSwgZmlyc3RWaWV3Q29weSk7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIGZpcnN0Vmlld0NvcHlbZmlyc3RWaWV3Q29weUxlbmd0aCAtIGNvdW50ZXJdID0gdmFsdWUgJSAxMDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoY1swXSA+IDApXG4gICAgICAgIGZpcnN0Vmlld0NvcHkgPSBwdnV0aWxzLnV0aWxDb25jYXRWaWV3KGMsIGZpcnN0Vmlld0NvcHkpO1xuICAgIHJldHVybiBmaXJzdFZpZXdDb3B5O1xufVxuZnVuY3Rpb24gcG93ZXIyKG4pIHtcbiAgICBpZiAobiA+PSBwb3dlcnMyLmxlbmd0aCkge1xuICAgICAgICBmb3IgKGxldCBwID0gcG93ZXJzMi5sZW5ndGg7IHAgPD0gbjsgcCsrKSB7XG4gICAgICAgICAgICBjb25zdCBjID0gbmV3IFVpbnQ4QXJyYXkoWzBdKTtcbiAgICAgICAgICAgIGxldCBkaWdpdHMgPSAocG93ZXJzMltwIC0gMV0pLnNsaWNlKDApO1xuICAgICAgICAgICAgZm9yIChsZXQgaSA9IChkaWdpdHMubGVuZ3RoIC0gMSk7IGkgPj0gMDsgaS0tKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV3VmFsdWUgPSBuZXcgVWludDhBcnJheShbKGRpZ2l0c1tpXSA8PCAxKSArIGNbMF1dKTtcbiAgICAgICAgICAgICAgICBjWzBdID0gbmV3VmFsdWVbMF0gLyAxMDtcbiAgICAgICAgICAgICAgICBkaWdpdHNbaV0gPSBuZXdWYWx1ZVswXSAlIDEwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGNbMF0gPiAwKVxuICAgICAgICAgICAgICAgIGRpZ2l0cyA9IHB2dXRpbHMudXRpbENvbmNhdFZpZXcoYywgZGlnaXRzKTtcbiAgICAgICAgICAgIHBvd2VyczIucHVzaChkaWdpdHMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBwb3dlcnMyW25dO1xufVxuZnVuY3Rpb24gdmlld1N1YihmaXJzdCwgc2Vjb25kKSB7XG4gICAgbGV0IGIgPSAwO1xuICAgIGNvbnN0IGZpcnN0VmlldyA9IG5ldyBVaW50OEFycmF5KGZpcnN0KTtcbiAgICBjb25zdCBzZWNvbmRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoc2Vjb25kKTtcbiAgICBjb25zdCBmaXJzdFZpZXdDb3B5ID0gZmlyc3RWaWV3LnNsaWNlKDApO1xuICAgIGNvbnN0IGZpcnN0Vmlld0NvcHlMZW5ndGggPSBmaXJzdFZpZXdDb3B5Lmxlbmd0aCAtIDE7XG4gICAgY29uc3Qgc2Vjb25kVmlld0NvcHkgPSBzZWNvbmRWaWV3LnNsaWNlKDApO1xuICAgIGNvbnN0IHNlY29uZFZpZXdDb3B5TGVuZ3RoID0gc2Vjb25kVmlld0NvcHkubGVuZ3RoIC0gMTtcbiAgICBsZXQgdmFsdWU7XG4gICAgbGV0IGNvdW50ZXIgPSAwO1xuICAgIGZvciAobGV0IGkgPSBzZWNvbmRWaWV3Q29weUxlbmd0aDsgaSA+PSAwOyBpLS0sIGNvdW50ZXIrKykge1xuICAgICAgICB2YWx1ZSA9IGZpcnN0Vmlld0NvcHlbZmlyc3RWaWV3Q29weUxlbmd0aCAtIGNvdW50ZXJdIC0gc2Vjb25kVmlld0NvcHlbc2Vjb25kVmlld0NvcHlMZW5ndGggLSBjb3VudGVyXSAtIGI7XG4gICAgICAgIHN3aXRjaCAodHJ1ZSkge1xuICAgICAgICAgICAgY2FzZSAodmFsdWUgPCAwKTpcbiAgICAgICAgICAgICAgICBiID0gMTtcbiAgICAgICAgICAgICAgICBmaXJzdFZpZXdDb3B5W2ZpcnN0Vmlld0NvcHlMZW5ndGggLSBjb3VudGVyXSA9IHZhbHVlICsgMTA7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIGIgPSAwO1xuICAgICAgICAgICAgICAgIGZpcnN0Vmlld0NvcHlbZmlyc3RWaWV3Q29weUxlbmd0aCAtIGNvdW50ZXJdID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKGIgPiAwKSB7XG4gICAgICAgIGZvciAobGV0IGkgPSAoZmlyc3RWaWV3Q29weUxlbmd0aCAtIHNlY29uZFZpZXdDb3B5TGVuZ3RoICsgMSk7IGkgPj0gMDsgaS0tLCBjb3VudGVyKyspIHtcbiAgICAgICAgICAgIHZhbHVlID0gZmlyc3RWaWV3Q29weVtmaXJzdFZpZXdDb3B5TGVuZ3RoIC0gY291bnRlcl0gLSBiO1xuICAgICAgICAgICAgaWYgKHZhbHVlIDwgMCkge1xuICAgICAgICAgICAgICAgIGIgPSAxO1xuICAgICAgICAgICAgICAgIGZpcnN0Vmlld0NvcHlbZmlyc3RWaWV3Q29weUxlbmd0aCAtIGNvdW50ZXJdID0gdmFsdWUgKyAxMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGIgPSAwO1xuICAgICAgICAgICAgICAgIGZpcnN0Vmlld0NvcHlbZmlyc3RWaWV3Q29weUxlbmd0aCAtIGNvdW50ZXJdID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGZpcnN0Vmlld0NvcHkuc2xpY2UoKTtcbn1cbmNsYXNzIExvY2FsSW50ZWdlclZhbHVlQmxvY2sgZXh0ZW5kcyBIZXhCbG9jayhWYWx1ZUJsb2NrKSB7XG4gICAgc2V0VmFsdWVIZXgoKSB7XG4gICAgICAgIGlmICh0aGlzLnZhbHVlSGV4Vmlldy5sZW5ndGggPj0gNCkge1xuICAgICAgICAgICAgdGhpcy53YXJuaW5ncy5wdXNoKFwiVG9vIGJpZyBJbnRlZ2VyIGZvciBkZWNvZGluZywgaGV4IG9ubHlcIik7XG4gICAgICAgICAgICB0aGlzLmlzSGV4T25seSA9IHRydWU7XG4gICAgICAgICAgICB0aGlzLl92YWx1ZURlYyA9IDA7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmlzSGV4T25seSA9IGZhbHNlO1xuICAgICAgICAgICAgaWYgKHRoaXMudmFsdWVIZXhWaWV3Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICB0aGlzLl92YWx1ZURlYyA9IHB2dXRpbHMudXRpbERlY29kZVRDLmNhbGwodGhpcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3RydWN0b3IoeyB2YWx1ZSwgLi4ucGFyYW1ldGVycyB9ID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMuX3ZhbHVlRGVjID0gMDtcbiAgICAgICAgaWYgKHBhcmFtZXRlcnMudmFsdWVIZXgpIHtcbiAgICAgICAgICAgIHRoaXMuc2V0VmFsdWVIZXgoKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy52YWx1ZURlYyA9IHZhbHVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNldCB2YWx1ZURlYyh2KSB7XG4gICAgICAgIHRoaXMuX3ZhbHVlRGVjID0gdjtcbiAgICAgICAgdGhpcy5pc0hleE9ubHkgPSBmYWxzZTtcbiAgICAgICAgdGhpcy52YWx1ZUhleFZpZXcgPSBuZXcgVWludDhBcnJheShwdnV0aWxzLnV0aWxFbmNvZGVUQyh2KSk7XG4gICAgfVxuICAgIGdldCB2YWx1ZURlYygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3ZhbHVlRGVjO1xuICAgIH1cbiAgICBmcm9tREVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgsIGV4cGVjdGVkTGVuZ3RoID0gMCkge1xuICAgICAgICBjb25zdCBvZmZzZXQgPSB0aGlzLmZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCk7XG4gICAgICAgIGlmIChvZmZzZXQgPT09IC0xKVxuICAgICAgICAgICAgcmV0dXJuIG9mZnNldDtcbiAgICAgICAgY29uc3QgdmlldyA9IHRoaXMudmFsdWVIZXhWaWV3O1xuICAgICAgICBpZiAoKHZpZXdbMF0gPT09IDB4MDApICYmICgodmlld1sxXSAmIDB4ODApICE9PSAwKSkge1xuICAgICAgICAgICAgdGhpcy52YWx1ZUhleFZpZXcgPSB2aWV3LnN1YmFycmF5KDEpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaWYgKGV4cGVjdGVkTGVuZ3RoICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgaWYgKHZpZXcubGVuZ3RoIDwgZXhwZWN0ZWRMZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKChleHBlY3RlZExlbmd0aCAtIHZpZXcubGVuZ3RoKSA+IDEpXG4gICAgICAgICAgICAgICAgICAgICAgICBleHBlY3RlZExlbmd0aCA9IHZpZXcubGVuZ3RoICsgMTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy52YWx1ZUhleFZpZXcgPSB2aWV3LnN1YmFycmF5KGV4cGVjdGVkTGVuZ3RoIC0gdmlldy5sZW5ndGgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gb2Zmc2V0O1xuICAgIH1cbiAgICB0b0RFUihzaXplT25seSA9IGZhbHNlKSB7XG4gICAgICAgIGNvbnN0IHZpZXcgPSB0aGlzLnZhbHVlSGV4VmlldztcbiAgICAgICAgc3dpdGNoICh0cnVlKSB7XG4gICAgICAgICAgICBjYXNlICgodmlld1swXSAmIDB4ODApICE9PSAwKTpcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkodGhpcy52YWx1ZUhleFZpZXcubGVuZ3RoICsgMSk7XG4gICAgICAgICAgICAgICAgICAgIHVwZGF0ZWRWaWV3WzBdID0gMHgwMDtcbiAgICAgICAgICAgICAgICAgICAgdXBkYXRlZFZpZXcuc2V0KHZpZXcsIDEpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbHVlSGV4VmlldyA9IHVwZGF0ZWRWaWV3O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgKCh2aWV3WzBdID09PSAweDAwKSAmJiAoKHZpZXdbMV0gJiAweDgwKSA9PT0gMCkpOlxuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy52YWx1ZUhleFZpZXcgPSB0aGlzLnZhbHVlSGV4Vmlldy5zdWJhcnJheSgxKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMudG9CRVIoc2l6ZU9ubHkpO1xuICAgIH1cbiAgICBmcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgY29uc3QgcmVzdWx0T2Zmc2V0ID0gc3VwZXIuZnJvbUJFUihpbnB1dEJ1ZmZlciwgaW5wdXRPZmZzZXQsIGlucHV0TGVuZ3RoKTtcbiAgICAgICAgaWYgKHJlc3VsdE9mZnNldCA9PT0gLTEpIHtcbiAgICAgICAgICAgIHJldHVybiByZXN1bHRPZmZzZXQ7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zZXRWYWx1ZUhleCgpO1xuICAgICAgICByZXR1cm4gcmVzdWx0T2Zmc2V0O1xuICAgIH1cbiAgICB0b0JFUihzaXplT25seSkge1xuICAgICAgICByZXR1cm4gc2l6ZU9ubHlcbiAgICAgICAgICAgID8gbmV3IEFycmF5QnVmZmVyKHRoaXMudmFsdWVIZXhWaWV3Lmxlbmd0aClcbiAgICAgICAgICAgIDogdGhpcy52YWx1ZUhleFZpZXcuc2xpY2UoKS5idWZmZXI7XG4gICAgfVxuICAgIHRvSlNPTigpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN1cGVyLnRvSlNPTigpLFxuICAgICAgICAgICAgdmFsdWVEZWM6IHRoaXMudmFsdWVEZWMsXG4gICAgICAgIH07XG4gICAgfVxuICAgIHRvU3RyaW5nKCkge1xuICAgICAgICBjb25zdCBmaXJzdEJpdCA9ICh0aGlzLnZhbHVlSGV4Vmlldy5sZW5ndGggKiA4KSAtIDE7XG4gICAgICAgIGxldCBkaWdpdHMgPSBuZXcgVWludDhBcnJheSgodGhpcy52YWx1ZUhleFZpZXcubGVuZ3RoICogOCkgLyAzKTtcbiAgICAgICAgbGV0IGJpdE51bWJlciA9IDA7XG4gICAgICAgIGxldCBjdXJyZW50Qnl0ZTtcbiAgICAgICAgY29uc3QgYXNuMVZpZXcgPSB0aGlzLnZhbHVlSGV4VmlldztcbiAgICAgICAgbGV0IHJlc3VsdCA9IFwiXCI7XG4gICAgICAgIGxldCBmbGFnID0gZmFsc2U7XG4gICAgICAgIGZvciAobGV0IGJ5dGVOdW1iZXIgPSAoYXNuMVZpZXcuYnl0ZUxlbmd0aCAtIDEpOyBieXRlTnVtYmVyID49IDA7IGJ5dGVOdW1iZXItLSkge1xuICAgICAgICAgICAgY3VycmVudEJ5dGUgPSBhc24xVmlld1tieXRlTnVtYmVyXTtcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgODsgaSsrKSB7XG4gICAgICAgICAgICAgICAgaWYgKChjdXJyZW50Qnl0ZSAmIDEpID09PSAxKSB7XG4gICAgICAgICAgICAgICAgICAgIHN3aXRjaCAoYml0TnVtYmVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYXNlIGZpcnN0Qml0OlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpZ2l0cyA9IHZpZXdTdWIocG93ZXIyKGJpdE51bWJlciksIGRpZ2l0cyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0ID0gXCItXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpZ2l0cyA9IHZpZXdBZGQoZGlnaXRzLCBwb3dlcjIoYml0TnVtYmVyKSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYml0TnVtYmVyKys7XG4gICAgICAgICAgICAgICAgY3VycmVudEJ5dGUgPj49IDE7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkaWdpdHMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGlmIChkaWdpdHNbaV0pXG4gICAgICAgICAgICAgICAgZmxhZyA9IHRydWU7XG4gICAgICAgICAgICBpZiAoZmxhZylcbiAgICAgICAgICAgICAgICByZXN1bHQgKz0gZGlnaXRzU3RyaW5nLmNoYXJBdChkaWdpdHNbaV0pO1xuICAgICAgICB9XG4gICAgICAgIGlmIChmbGFnID09PSBmYWxzZSlcbiAgICAgICAgICAgIHJlc3VsdCArPSBkaWdpdHNTdHJpbmcuY2hhckF0KDApO1xuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH1cbn1cbl9hJHAgPSBMb2NhbEludGVnZXJWYWx1ZUJsb2NrO1xuTG9jYWxJbnRlZ2VyVmFsdWVCbG9jay5OQU1FID0gXCJJbnRlZ2VyVmFsdWVCbG9ja1wiO1xuKCgpID0+IHtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoX2EkcC5wcm90b3R5cGUsIFwidmFsdWVIZXhcIiwge1xuICAgICAgICBzZXQ6IGZ1bmN0aW9uICh2KSB7XG4gICAgICAgICAgICB0aGlzLnZhbHVlSGV4VmlldyA9IG5ldyBVaW50OEFycmF5KHYpO1xuICAgICAgICAgICAgdGhpcy5zZXRWYWx1ZUhleCgpO1xuICAgICAgICB9LFxuICAgICAgICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnZhbHVlSGV4Vmlldy5zbGljZSgpLmJ1ZmZlcjtcbiAgICAgICAgfSxcbiAgICB9KTtcbn0pKCk7XG5cbnZhciBfYSRvO1xuY2xhc3MgSW50ZWdlciBleHRlbmRzIEJhc2VCbG9jayB7XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMsIExvY2FsSW50ZWdlclZhbHVlQmxvY2spO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gMjtcbiAgICB9XG4gICAgdG9CaWdJbnQoKSB7XG4gICAgICAgIGFzc2VydEJpZ0ludCgpO1xuICAgICAgICByZXR1cm4gQmlnSW50KHRoaXMudmFsdWVCbG9jay50b1N0cmluZygpKTtcbiAgICB9XG4gICAgc3RhdGljIGZyb21CaWdJbnQodmFsdWUpIHtcbiAgICAgICAgYXNzZXJ0QmlnSW50KCk7XG4gICAgICAgIGNvbnN0IGJpZ0ludFZhbHVlID0gQmlnSW50KHZhbHVlKTtcbiAgICAgICAgY29uc3Qgd3JpdGVyID0gbmV3IFZpZXdXcml0ZXIoKTtcbiAgICAgICAgY29uc3QgaGV4ID0gYmlnSW50VmFsdWUudG9TdHJpbmcoMTYpLnJlcGxhY2UoL14tLywgXCJcIik7XG4gICAgICAgIGNvbnN0IHZpZXcgPSBuZXcgVWludDhBcnJheShwdnRzdXRpbHMuQ29udmVydC5Gcm9tSGV4KGhleCkpO1xuICAgICAgICBpZiAoYmlnSW50VmFsdWUgPCAwKSB7XG4gICAgICAgICAgICBjb25zdCBmaXJzdCA9IG5ldyBVaW50OEFycmF5KHZpZXcubGVuZ3RoICsgKHZpZXdbMF0gJiAweDgwID8gMSA6IDApKTtcbiAgICAgICAgICAgIGZpcnN0WzBdIHw9IDB4ODA7XG4gICAgICAgICAgICBjb25zdCBmaXJzdEludCA9IEJpZ0ludChgMHgke3B2dHN1dGlscy5Db252ZXJ0LlRvSGV4KGZpcnN0KX1gKTtcbiAgICAgICAgICAgIGNvbnN0IHNlY29uZEludCA9IGZpcnN0SW50ICsgYmlnSW50VmFsdWU7XG4gICAgICAgICAgICBjb25zdCBzZWNvbmQgPSBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheShwdnRzdXRpbHMuQ29udmVydC5Gcm9tSGV4KHNlY29uZEludC50b1N0cmluZygxNikpKTtcbiAgICAgICAgICAgIHNlY29uZFswXSB8PSAweDgwO1xuICAgICAgICAgICAgd3JpdGVyLndyaXRlKHNlY29uZCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpZiAodmlld1swXSAmIDB4ODApIHtcbiAgICAgICAgICAgICAgICB3cml0ZXIud3JpdGUobmV3IFVpbnQ4QXJyYXkoWzBdKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB3cml0ZXIud3JpdGUodmlldyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmVzID0gbmV3IF9hJG8oeyB2YWx1ZUhleDogd3JpdGVyLmZpbmFsKCkgfSk7XG4gICAgICAgIHJldHVybiByZXM7XG4gICAgfVxuICAgIGNvbnZlcnRUb0RFUigpIHtcbiAgICAgICAgY29uc3QgaW50ZWdlciA9IG5ldyBfYSRvKHsgdmFsdWVIZXg6IHRoaXMudmFsdWVCbG9jay52YWx1ZUhleFZpZXcgfSk7XG4gICAgICAgIGludGVnZXIudmFsdWVCbG9jay50b0RFUigpO1xuICAgICAgICByZXR1cm4gaW50ZWdlcjtcbiAgICB9XG4gICAgY29udmVydEZyb21ERVIoKSB7XG4gICAgICAgIHJldHVybiBuZXcgX2Ekbyh7XG4gICAgICAgICAgICB2YWx1ZUhleDogdGhpcy52YWx1ZUJsb2NrLnZhbHVlSGV4Vmlld1swXSA9PT0gMFxuICAgICAgICAgICAgICAgID8gdGhpcy52YWx1ZUJsb2NrLnZhbHVlSGV4Vmlldy5zdWJhcnJheSgxKVxuICAgICAgICAgICAgICAgIDogdGhpcy52YWx1ZUJsb2NrLnZhbHVlSGV4VmlldyxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIG9uQXNjaWlFbmNvZGluZygpIHtcbiAgICAgICAgcmV0dXJuIGAke3RoaXMuY29uc3RydWN0b3IuTkFNRX0gOiAke3RoaXMudmFsdWVCbG9jay50b1N0cmluZygpfWA7XG4gICAgfVxufVxuX2EkbyA9IEludGVnZXI7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5JbnRlZ2VyID0gX2Ekbztcbn0pKCk7XG5JbnRlZ2VyLk5BTUUgPSBcIklOVEVHRVJcIjtcblxudmFyIF9hJG47XG5jbGFzcyBFbnVtZXJhdGVkIGV4dGVuZHMgSW50ZWdlciB7XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gMTA7XG4gICAgfVxufVxuX2EkbiA9IEVudW1lcmF0ZWQ7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5FbnVtZXJhdGVkID0gX2Ekbjtcbn0pKCk7XG5FbnVtZXJhdGVkLk5BTUUgPSBcIkVOVU1FUkFURURcIjtcblxuY2xhc3MgTG9jYWxTaWRWYWx1ZUJsb2NrIGV4dGVuZHMgSGV4QmxvY2soVmFsdWVCbG9jaykge1xuICAgIGNvbnN0cnVjdG9yKHsgdmFsdWVEZWMgPSAtMSwgaXNGaXJzdFNpZCA9IGZhbHNlLCAuLi5wYXJhbWV0ZXJzIH0gPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy52YWx1ZURlYyA9IHZhbHVlRGVjO1xuICAgICAgICB0aGlzLmlzRmlyc3RTaWQgPSBpc0ZpcnN0U2lkO1xuICAgIH1cbiAgICBmcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgaWYgKCFpbnB1dExlbmd0aCkge1xuICAgICAgICAgICAgcmV0dXJuIGlucHV0T2Zmc2V0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGlucHV0VmlldyA9IHB2dHN1dGlscy5CdWZmZXJTb3VyY2VDb252ZXJ0ZXIudG9VaW50OEFycmF5KGlucHV0QnVmZmVyKTtcbiAgICAgICAgaWYgKCFjaGVja0J1ZmZlclBhcmFtcyh0aGlzLCBpbnB1dFZpZXcsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkpIHtcbiAgICAgICAgICAgIHJldHVybiAtMTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBpbnRCdWZmZXIgPSBpbnB1dFZpZXcuc3ViYXJyYXkoaW5wdXRPZmZzZXQsIGlucHV0T2Zmc2V0ICsgaW5wdXRMZW5ndGgpO1xuICAgICAgICB0aGlzLnZhbHVlSGV4VmlldyA9IG5ldyBVaW50OEFycmF5KGlucHV0TGVuZ3RoKTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBpbnB1dExlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICB0aGlzLnZhbHVlSGV4Vmlld1tpXSA9IGludEJ1ZmZlcltpXSAmIDB4N0Y7XG4gICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoKys7XG4gICAgICAgICAgICBpZiAoKGludEJ1ZmZlcltpXSAmIDB4ODApID09PSAweDAwKVxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHRlbXBWaWV3ID0gbmV3IFVpbnQ4QXJyYXkodGhpcy5ibG9ja0xlbmd0aCk7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5ibG9ja0xlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICB0ZW1wVmlld1tpXSA9IHRoaXMudmFsdWVIZXhWaWV3W2ldO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMudmFsdWVIZXhWaWV3ID0gdGVtcFZpZXc7XG4gICAgICAgIGlmICgoaW50QnVmZmVyW3RoaXMuYmxvY2tMZW5ndGggLSAxXSAmIDB4ODApICE9PSAweDAwKSB7XG4gICAgICAgICAgICB0aGlzLmVycm9yID0gXCJFbmQgb2YgaW5wdXQgcmVhY2hlZCBiZWZvcmUgbWVzc2FnZSB3YXMgZnVsbHkgZGVjb2RlZFwiO1xuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLnZhbHVlSGV4Vmlld1swXSA9PT0gMHgwMClcbiAgICAgICAgICAgIHRoaXMud2FybmluZ3MucHVzaChcIk5lZWRsZXNzbHkgbG9uZyBmb3JtYXQgb2YgU0lEIGVuY29kaW5nXCIpO1xuICAgICAgICBpZiAodGhpcy5ibG9ja0xlbmd0aCA8PSA4KVxuICAgICAgICAgICAgdGhpcy52YWx1ZURlYyA9IHB2dXRpbHMudXRpbEZyb21CYXNlKHRoaXMudmFsdWVIZXhWaWV3LCA3KTtcbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmlzSGV4T25seSA9IHRydWU7XG4gICAgICAgICAgICB0aGlzLndhcm5pbmdzLnB1c2goXCJUb28gYmlnIFNJRCBmb3IgZGVjb2RpbmcsIGhleCBvbmx5XCIpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAoaW5wdXRPZmZzZXQgKyB0aGlzLmJsb2NrTGVuZ3RoKTtcbiAgICB9XG4gICAgc2V0IHZhbHVlQmlnSW50KHZhbHVlKSB7XG4gICAgICAgIGFzc2VydEJpZ0ludCgpO1xuICAgICAgICBsZXQgYml0cyA9IEJpZ0ludCh2YWx1ZSkudG9TdHJpbmcoMik7XG4gICAgICAgIHdoaWxlIChiaXRzLmxlbmd0aCAlIDcpIHtcbiAgICAgICAgICAgIGJpdHMgPSBcIjBcIiArIGJpdHM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYnl0ZXMgPSBuZXcgVWludDhBcnJheShiaXRzLmxlbmd0aCAvIDcpO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGJ5dGVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBieXRlc1tpXSA9IHBhcnNlSW50KGJpdHMuc2xpY2UoaSAqIDcsIGkgKiA3ICsgNyksIDIpICsgKGkgKyAxIDwgYnl0ZXMubGVuZ3RoID8gMHg4MCA6IDApO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuZnJvbUJFUihieXRlcy5idWZmZXIsIDAsIGJ5dGVzLmxlbmd0aCk7XG4gICAgfVxuICAgIHRvQkVSKHNpemVPbmx5KSB7XG4gICAgICAgIGlmICh0aGlzLmlzSGV4T25seSkge1xuICAgICAgICAgICAgaWYgKHNpemVPbmx5KVxuICAgICAgICAgICAgICAgIHJldHVybiAobmV3IEFycmF5QnVmZmVyKHRoaXMudmFsdWVIZXhWaWV3LmJ5dGVMZW5ndGgpKTtcbiAgICAgICAgICAgIGNvbnN0IGN1clZpZXcgPSB0aGlzLnZhbHVlSGV4VmlldztcbiAgICAgICAgICAgIGNvbnN0IHJldFZpZXcgPSBuZXcgVWludDhBcnJheSh0aGlzLmJsb2NrTGVuZ3RoKTtcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgKHRoaXMuYmxvY2tMZW5ndGggLSAxKTsgaSsrKVxuICAgICAgICAgICAgICAgIHJldFZpZXdbaV0gPSBjdXJWaWV3W2ldIHwgMHg4MDtcbiAgICAgICAgICAgIHJldFZpZXdbdGhpcy5ibG9ja0xlbmd0aCAtIDFdID0gY3VyVmlld1t0aGlzLmJsb2NrTGVuZ3RoIC0gMV07XG4gICAgICAgICAgICByZXR1cm4gcmV0Vmlldy5idWZmZXI7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZW5jb2RlZEJ1ZiA9IHB2dXRpbHMudXRpbFRvQmFzZSh0aGlzLnZhbHVlRGVjLCA3KTtcbiAgICAgICAgaWYgKGVuY29kZWRCdWYuYnl0ZUxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgdGhpcy5lcnJvciA9IFwiRXJyb3IgZHVyaW5nIGVuY29kaW5nIFNJRCB2YWx1ZVwiO1xuICAgICAgICAgICAgcmV0dXJuIEVNUFRZX0JVRkZFUjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZXRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoZW5jb2RlZEJ1Zi5ieXRlTGVuZ3RoKTtcbiAgICAgICAgaWYgKCFzaXplT25seSkge1xuICAgICAgICAgICAgY29uc3QgZW5jb2RlZFZpZXcgPSBuZXcgVWludDhBcnJheShlbmNvZGVkQnVmKTtcbiAgICAgICAgICAgIGNvbnN0IGxlbiA9IGVuY29kZWRCdWYuYnl0ZUxlbmd0aCAtIDE7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbjsgaSsrKVxuICAgICAgICAgICAgICAgIHJldFZpZXdbaV0gPSBlbmNvZGVkVmlld1tpXSB8IDB4ODA7XG4gICAgICAgICAgICByZXRWaWV3W2xlbl0gPSBlbmNvZGVkVmlld1tsZW5dO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXRWaWV3O1xuICAgIH1cbiAgICB0b1N0cmluZygpIHtcbiAgICAgICAgbGV0IHJlc3VsdCA9IFwiXCI7XG4gICAgICAgIGlmICh0aGlzLmlzSGV4T25seSlcbiAgICAgICAgICAgIHJlc3VsdCA9IHB2dHN1dGlscy5Db252ZXJ0LlRvSGV4KHRoaXMudmFsdWVIZXhWaWV3KTtcbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpZiAodGhpcy5pc0ZpcnN0U2lkKSB7XG4gICAgICAgICAgICAgICAgbGV0IHNpZFZhbHVlID0gdGhpcy52YWx1ZURlYztcbiAgICAgICAgICAgICAgICBpZiAodGhpcy52YWx1ZURlYyA8PSAzOSlcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0ID0gXCIwLlwiO1xuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy52YWx1ZURlYyA8PSA3OSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0ID0gXCIxLlwiO1xuICAgICAgICAgICAgICAgICAgICAgICAgc2lkVmFsdWUgLT0gNDA7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXN1bHQgPSBcIjIuXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICBzaWRWYWx1ZSAtPSA4MDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXN1bHQgKz0gc2lkVmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICByZXN1bHQgPSB0aGlzLnZhbHVlRGVjLnRvU3RyaW5nKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG4gICAgdG9KU09OKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3VwZXIudG9KU09OKCksXG4gICAgICAgICAgICB2YWx1ZURlYzogdGhpcy52YWx1ZURlYyxcbiAgICAgICAgICAgIGlzRmlyc3RTaWQ6IHRoaXMuaXNGaXJzdFNpZCxcbiAgICAgICAgfTtcbiAgICB9XG59XG5Mb2NhbFNpZFZhbHVlQmxvY2suTkFNRSA9IFwic2lkQmxvY2tcIjtcblxuY2xhc3MgTG9jYWxPYmplY3RJZGVudGlmaWVyVmFsdWVCbG9jayBleHRlbmRzIFZhbHVlQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHsgdmFsdWUgPSBFTVBUWV9TVFJJTkcsIC4uLnBhcmFtZXRlcnMgfSA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLnZhbHVlID0gW107XG4gICAgICAgIGlmICh2YWx1ZSkge1xuICAgICAgICAgICAgdGhpcy5mcm9tU3RyaW5nKHZhbHVlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBmcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgbGV0IHJlc3VsdE9mZnNldCA9IGlucHV0T2Zmc2V0O1xuICAgICAgICB3aGlsZSAoaW5wdXRMZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBjb25zdCBzaWRCbG9jayA9IG5ldyBMb2NhbFNpZFZhbHVlQmxvY2soKTtcbiAgICAgICAgICAgIHJlc3VsdE9mZnNldCA9IHNpZEJsb2NrLmZyb21CRVIoaW5wdXRCdWZmZXIsIHJlc3VsdE9mZnNldCwgaW5wdXRMZW5ndGgpO1xuICAgICAgICAgICAgaWYgKHJlc3VsdE9mZnNldCA9PT0gLTEpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoID0gMDtcbiAgICAgICAgICAgICAgICB0aGlzLmVycm9yID0gc2lkQmxvY2suZXJyb3I7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdE9mZnNldDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLnZhbHVlLmxlbmd0aCA9PT0gMClcbiAgICAgICAgICAgICAgICBzaWRCbG9jay5pc0ZpcnN0U2lkID0gdHJ1ZTtcbiAgICAgICAgICAgIHRoaXMuYmxvY2tMZW5ndGggKz0gc2lkQmxvY2suYmxvY2tMZW5ndGg7XG4gICAgICAgICAgICBpbnB1dExlbmd0aCAtPSBzaWRCbG9jay5ibG9ja0xlbmd0aDtcbiAgICAgICAgICAgIHRoaXMudmFsdWUucHVzaChzaWRCbG9jayk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdE9mZnNldDtcbiAgICB9XG4gICAgdG9CRVIoc2l6ZU9ubHkpIHtcbiAgICAgICAgY29uc3QgcmV0QnVmZmVycyA9IFtdO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMudmFsdWUubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlQnVmID0gdGhpcy52YWx1ZVtpXS50b0JFUihzaXplT25seSk7XG4gICAgICAgICAgICBpZiAodmFsdWVCdWYuYnl0ZUxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZXJyb3IgPSB0aGlzLnZhbHVlW2ldLmVycm9yO1xuICAgICAgICAgICAgICAgIHJldHVybiBFTVBUWV9CVUZGRVI7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXRCdWZmZXJzLnB1c2godmFsdWVCdWYpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBjb25jYXQocmV0QnVmZmVycyk7XG4gICAgfVxuICAgIGZyb21TdHJpbmcoc3RyaW5nKSB7XG4gICAgICAgIHRoaXMudmFsdWUgPSBbXTtcbiAgICAgICAgbGV0IHBvczEgPSAwO1xuICAgICAgICBsZXQgcG9zMiA9IDA7XG4gICAgICAgIGxldCBzaWQgPSBcIlwiO1xuICAgICAgICBsZXQgZmxhZyA9IGZhbHNlO1xuICAgICAgICBkbyB7XG4gICAgICAgICAgICBwb3MyID0gc3RyaW5nLmluZGV4T2YoXCIuXCIsIHBvczEpO1xuICAgICAgICAgICAgaWYgKHBvczIgPT09IC0xKVxuICAgICAgICAgICAgICAgIHNpZCA9IHN0cmluZy5zdWJzdHJpbmcocG9zMSk7XG4gICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgc2lkID0gc3RyaW5nLnN1YnN0cmluZyhwb3MxLCBwb3MyKTtcbiAgICAgICAgICAgIHBvczEgPSBwb3MyICsgMTtcbiAgICAgICAgICAgIGlmIChmbGFnKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc2lkQmxvY2sgPSB0aGlzLnZhbHVlWzBdO1xuICAgICAgICAgICAgICAgIGxldCBwbHVzID0gMDtcbiAgICAgICAgICAgICAgICBzd2l0Y2ggKHNpZEJsb2NrLnZhbHVlRGVjKSB7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgMDpcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICBjYXNlIDE6XG4gICAgICAgICAgICAgICAgICAgICAgICBwbHVzID0gNDA7XG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgY2FzZSAyOlxuICAgICAgICAgICAgICAgICAgICAgICAgcGx1cyA9IDgwO1xuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbHVlID0gW107XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHBhcnNlZFNJRCA9IHBhcnNlSW50KHNpZCwgMTApO1xuICAgICAgICAgICAgICAgIGlmIChpc05hTihwYXJzZWRTSUQpKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgc2lkQmxvY2sudmFsdWVEZWMgPSBwYXJzZWRTSUQgKyBwbHVzO1xuICAgICAgICAgICAgICAgIGZsYWcgPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IHNpZEJsb2NrID0gbmV3IExvY2FsU2lkVmFsdWVCbG9jaygpO1xuICAgICAgICAgICAgICAgIGlmIChzaWQgPiBOdW1iZXIuTUFYX1NBRkVfSU5URUdFUikge1xuICAgICAgICAgICAgICAgICAgICBhc3NlcnRCaWdJbnQoKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2lkVmFsdWUgPSBCaWdJbnQoc2lkKTtcbiAgICAgICAgICAgICAgICAgICAgc2lkQmxvY2sudmFsdWVCaWdJbnQgPSBzaWRWYWx1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHNpZEJsb2NrLnZhbHVlRGVjID0gcGFyc2VJbnQoc2lkLCAxMCk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc05hTihzaWRCbG9jay52YWx1ZURlYykpXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICghdGhpcy52YWx1ZS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgc2lkQmxvY2suaXNGaXJzdFNpZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIGZsYWcgPSB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLnZhbHVlLnB1c2goc2lkQmxvY2spO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IHdoaWxlIChwb3MyICE9PSAtMSk7XG4gICAgfVxuICAgIHRvU3RyaW5nKCkge1xuICAgICAgICBsZXQgcmVzdWx0ID0gXCJcIjtcbiAgICAgICAgbGV0IGlzSGV4T25seSA9IGZhbHNlO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMudmFsdWUubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGlzSGV4T25seSA9IHRoaXMudmFsdWVbaV0uaXNIZXhPbmx5O1xuICAgICAgICAgICAgbGV0IHNpZFN0ciA9IHRoaXMudmFsdWVbaV0udG9TdHJpbmcoKTtcbiAgICAgICAgICAgIGlmIChpICE9PSAwKVxuICAgICAgICAgICAgICAgIHJlc3VsdCA9IGAke3Jlc3VsdH0uYDtcbiAgICAgICAgICAgIGlmIChpc0hleE9ubHkpIHtcbiAgICAgICAgICAgICAgICBzaWRTdHIgPSBgeyR7c2lkU3RyfX1gO1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLnZhbHVlW2ldLmlzRmlyc3RTaWQpXG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdCA9IGAyLnske3NpZFN0cn0gLSA4MH1gO1xuICAgICAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0ICs9IHNpZFN0cjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICByZXN1bHQgKz0gc2lkU3RyO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfVxuICAgIHRvSlNPTigpIHtcbiAgICAgICAgY29uc3Qgb2JqZWN0ID0ge1xuICAgICAgICAgICAgLi4uc3VwZXIudG9KU09OKCksXG4gICAgICAgICAgICB2YWx1ZTogdGhpcy50b1N0cmluZygpLFxuICAgICAgICAgICAgc2lkQXJyYXk6IFtdLFxuICAgICAgICB9O1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMudmFsdWUubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIG9iamVjdC5zaWRBcnJheS5wdXNoKHRoaXMudmFsdWVbaV0udG9KU09OKCkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBvYmplY3Q7XG4gICAgfVxufVxuTG9jYWxPYmplY3RJZGVudGlmaWVyVmFsdWVCbG9jay5OQU1FID0gXCJPYmplY3RJZGVudGlmaWVyVmFsdWVCbG9ja1wiO1xuXG52YXIgX2EkbTtcbmNsYXNzIE9iamVjdElkZW50aWZpZXIgZXh0ZW5kcyBCYXNlQmxvY2sge1xuICAgIGdldFZhbHVlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy52YWx1ZUJsb2NrLnRvU3RyaW5nKCk7XG4gICAgfVxuICAgIHNldFZhbHVlKHZhbHVlKSB7XG4gICAgICAgIHRoaXMudmFsdWVCbG9jay5mcm9tU3RyaW5nKHZhbHVlKTtcbiAgICB9XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMsIExvY2FsT2JqZWN0SWRlbnRpZmllclZhbHVlQmxvY2spO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gNjtcbiAgICB9XG4gICAgb25Bc2NpaUVuY29kaW5nKCkge1xuICAgICAgICByZXR1cm4gYCR7dGhpcy5jb25zdHJ1Y3Rvci5OQU1FfSA6ICR7dGhpcy52YWx1ZUJsb2NrLnRvU3RyaW5nKCkgfHwgXCJlbXB0eVwifWA7XG4gICAgfVxuICAgIHRvSlNPTigpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN1cGVyLnRvSlNPTigpLFxuICAgICAgICAgICAgdmFsdWU6IHRoaXMuZ2V0VmFsdWUoKSxcbiAgICAgICAgfTtcbiAgICB9XG59XG5fYSRtID0gT2JqZWN0SWRlbnRpZmllcjtcbigoKSA9PiB7XG4gICAgdHlwZVN0b3JlLk9iamVjdElkZW50aWZpZXIgPSBfYSRtO1xufSkoKTtcbk9iamVjdElkZW50aWZpZXIuTkFNRSA9IFwiT0JKRUNUIElERU5USUZJRVJcIjtcblxuY2xhc3MgTG9jYWxSZWxhdGl2ZVNpZFZhbHVlQmxvY2sgZXh0ZW5kcyBIZXhCbG9jayhMb2NhbEJhc2VCbG9jaykge1xuICAgIGNvbnN0cnVjdG9yKHsgdmFsdWVEZWMgPSAwLCAuLi5wYXJhbWV0ZXJzIH0gPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy52YWx1ZURlYyA9IHZhbHVlRGVjO1xuICAgIH1cbiAgICBmcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgaWYgKGlucHV0TGVuZ3RoID09PSAwKVxuICAgICAgICAgICAgcmV0dXJuIGlucHV0T2Zmc2V0O1xuICAgICAgICBjb25zdCBpbnB1dFZpZXcgPSBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheShpbnB1dEJ1ZmZlcik7XG4gICAgICAgIGlmICghY2hlY2tCdWZmZXJQYXJhbXModGhpcywgaW5wdXRWaWV3LCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpKVxuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICBjb25zdCBpbnRCdWZmZXIgPSBpbnB1dFZpZXcuc3ViYXJyYXkoaW5wdXRPZmZzZXQsIGlucHV0T2Zmc2V0ICsgaW5wdXRMZW5ndGgpO1xuICAgICAgICB0aGlzLnZhbHVlSGV4VmlldyA9IG5ldyBVaW50OEFycmF5KGlucHV0TGVuZ3RoKTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBpbnB1dExlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICB0aGlzLnZhbHVlSGV4Vmlld1tpXSA9IGludEJ1ZmZlcltpXSAmIDB4N0Y7XG4gICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoKys7XG4gICAgICAgICAgICBpZiAoKGludEJ1ZmZlcltpXSAmIDB4ODApID09PSAweDAwKVxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHRlbXBWaWV3ID0gbmV3IFVpbnQ4QXJyYXkodGhpcy5ibG9ja0xlbmd0aCk7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5ibG9ja0xlbmd0aDsgaSsrKVxuICAgICAgICAgICAgdGVtcFZpZXdbaV0gPSB0aGlzLnZhbHVlSGV4Vmlld1tpXTtcbiAgICAgICAgdGhpcy52YWx1ZUhleFZpZXcgPSB0ZW1wVmlldztcbiAgICAgICAgaWYgKChpbnRCdWZmZXJbdGhpcy5ibG9ja0xlbmd0aCAtIDFdICYgMHg4MCkgIT09IDB4MDApIHtcbiAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIkVuZCBvZiBpbnB1dCByZWFjaGVkIGJlZm9yZSBtZXNzYWdlIHdhcyBmdWxseSBkZWNvZGVkXCI7XG4gICAgICAgICAgICByZXR1cm4gLTE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMudmFsdWVIZXhWaWV3WzBdID09PSAweDAwKVxuICAgICAgICAgICAgdGhpcy53YXJuaW5ncy5wdXNoKFwiTmVlZGxlc3NseSBsb25nIGZvcm1hdCBvZiBTSUQgZW5jb2RpbmdcIik7XG4gICAgICAgIGlmICh0aGlzLmJsb2NrTGVuZ3RoIDw9IDgpXG4gICAgICAgICAgICB0aGlzLnZhbHVlRGVjID0gcHZ1dGlscy51dGlsRnJvbUJhc2UodGhpcy52YWx1ZUhleFZpZXcsIDcpO1xuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuaXNIZXhPbmx5ID0gdHJ1ZTtcbiAgICAgICAgICAgIHRoaXMud2FybmluZ3MucHVzaChcIlRvbyBiaWcgU0lEIGZvciBkZWNvZGluZywgaGV4IG9ubHlcIik7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIChpbnB1dE9mZnNldCArIHRoaXMuYmxvY2tMZW5ndGgpO1xuICAgIH1cbiAgICB0b0JFUihzaXplT25seSkge1xuICAgICAgICBpZiAodGhpcy5pc0hleE9ubHkpIHtcbiAgICAgICAgICAgIGlmIChzaXplT25seSlcbiAgICAgICAgICAgICAgICByZXR1cm4gKG5ldyBBcnJheUJ1ZmZlcih0aGlzLnZhbHVlSGV4Vmlldy5ieXRlTGVuZ3RoKSk7XG4gICAgICAgICAgICBjb25zdCBjdXJWaWV3ID0gdGhpcy52YWx1ZUhleFZpZXc7XG4gICAgICAgICAgICBjb25zdCByZXRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkodGhpcy5ibG9ja0xlbmd0aCk7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8ICh0aGlzLmJsb2NrTGVuZ3RoIC0gMSk7IGkrKylcbiAgICAgICAgICAgICAgICByZXRWaWV3W2ldID0gY3VyVmlld1tpXSB8IDB4ODA7XG4gICAgICAgICAgICByZXRWaWV3W3RoaXMuYmxvY2tMZW5ndGggLSAxXSA9IGN1clZpZXdbdGhpcy5ibG9ja0xlbmd0aCAtIDFdO1xuICAgICAgICAgICAgcmV0dXJuIHJldFZpZXcuYnVmZmVyO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGVuY29kZWRCdWYgPSBwdnV0aWxzLnV0aWxUb0Jhc2UodGhpcy52YWx1ZURlYywgNyk7XG4gICAgICAgIGlmIChlbmNvZGVkQnVmLmJ5dGVMZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIkVycm9yIGR1cmluZyBlbmNvZGluZyBTSUQgdmFsdWVcIjtcbiAgICAgICAgICAgIHJldHVybiBFTVBUWV9CVUZGRVI7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmV0VmlldyA9IG5ldyBVaW50OEFycmF5KGVuY29kZWRCdWYuYnl0ZUxlbmd0aCk7XG4gICAgICAgIGlmICghc2l6ZU9ubHkpIHtcbiAgICAgICAgICAgIGNvbnN0IGVuY29kZWRWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoZW5jb2RlZEJ1Zik7XG4gICAgICAgICAgICBjb25zdCBsZW4gPSBlbmNvZGVkQnVmLmJ5dGVMZW5ndGggLSAxO1xuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW47IGkrKylcbiAgICAgICAgICAgICAgICByZXRWaWV3W2ldID0gZW5jb2RlZFZpZXdbaV0gfCAweDgwO1xuICAgICAgICAgICAgcmV0Vmlld1tsZW5dID0gZW5jb2RlZFZpZXdbbGVuXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmV0Vmlldy5idWZmZXI7XG4gICAgfVxuICAgIHRvU3RyaW5nKCkge1xuICAgICAgICBsZXQgcmVzdWx0ID0gXCJcIjtcbiAgICAgICAgaWYgKHRoaXMuaXNIZXhPbmx5KVxuICAgICAgICAgICAgcmVzdWx0ID0gcHZ0c3V0aWxzLkNvbnZlcnQuVG9IZXgodGhpcy52YWx1ZUhleFZpZXcpO1xuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJlc3VsdCA9IHRoaXMudmFsdWVEZWMudG9TdHJpbmcoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH1cbiAgICB0b0pTT04oKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdXBlci50b0pTT04oKSxcbiAgICAgICAgICAgIHZhbHVlRGVjOiB0aGlzLnZhbHVlRGVjLFxuICAgICAgICB9O1xuICAgIH1cbn1cbkxvY2FsUmVsYXRpdmVTaWRWYWx1ZUJsb2NrLk5BTUUgPSBcInJlbGF0aXZlU2lkQmxvY2tcIjtcblxuY2xhc3MgTG9jYWxSZWxhdGl2ZU9iamVjdElkZW50aWZpZXJWYWx1ZUJsb2NrIGV4dGVuZHMgVmFsdWVCbG9jayB7XG4gICAgY29uc3RydWN0b3IoeyB2YWx1ZSA9IEVNUFRZX1NUUklORywgLi4ucGFyYW1ldGVycyB9ID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMudmFsdWUgPSBbXTtcbiAgICAgICAgaWYgKHZhbHVlKSB7XG4gICAgICAgICAgICB0aGlzLmZyb21TdHJpbmcodmFsdWUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGZyb21CRVIoaW5wdXRCdWZmZXIsIGlucHV0T2Zmc2V0LCBpbnB1dExlbmd0aCkge1xuICAgICAgICBsZXQgcmVzdWx0T2Zmc2V0ID0gaW5wdXRPZmZzZXQ7XG4gICAgICAgIHdoaWxlIChpbnB1dExlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGNvbnN0IHNpZEJsb2NrID0gbmV3IExvY2FsUmVsYXRpdmVTaWRWYWx1ZUJsb2NrKCk7XG4gICAgICAgICAgICByZXN1bHRPZmZzZXQgPSBzaWRCbG9jay5mcm9tQkVSKGlucHV0QnVmZmVyLCByZXN1bHRPZmZzZXQsIGlucHV0TGVuZ3RoKTtcbiAgICAgICAgICAgIGlmIChyZXN1bHRPZmZzZXQgPT09IC0xKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5ibG9ja0xlbmd0aCA9IDA7XG4gICAgICAgICAgICAgICAgdGhpcy5lcnJvciA9IHNpZEJsb2NrLmVycm9yO1xuICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHRPZmZzZXQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmJsb2NrTGVuZ3RoICs9IHNpZEJsb2NrLmJsb2NrTGVuZ3RoO1xuICAgICAgICAgICAgaW5wdXRMZW5ndGggLT0gc2lkQmxvY2suYmxvY2tMZW5ndGg7XG4gICAgICAgICAgICB0aGlzLnZhbHVlLnB1c2goc2lkQmxvY2spO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHRPZmZzZXQ7XG4gICAgfVxuICAgIHRvQkVSKHNpemVPbmx5LCBfd3JpdGVyKSB7XG4gICAgICAgIGNvbnN0IHJldEJ1ZmZlcnMgPSBbXTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnZhbHVlLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCB2YWx1ZUJ1ZiA9IHRoaXMudmFsdWVbaV0udG9CRVIoc2l6ZU9ubHkpO1xuICAgICAgICAgICAgaWYgKHZhbHVlQnVmLmJ5dGVMZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgICAgICB0aGlzLmVycm9yID0gdGhpcy52YWx1ZVtpXS5lcnJvcjtcbiAgICAgICAgICAgICAgICByZXR1cm4gRU1QVFlfQlVGRkVSO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0QnVmZmVycy5wdXNoKHZhbHVlQnVmKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gY29uY2F0KHJldEJ1ZmZlcnMpO1xuICAgIH1cbiAgICBmcm9tU3RyaW5nKHN0cmluZykge1xuICAgICAgICB0aGlzLnZhbHVlID0gW107XG4gICAgICAgIGxldCBwb3MxID0gMDtcbiAgICAgICAgbGV0IHBvczIgPSAwO1xuICAgICAgICBsZXQgc2lkID0gXCJcIjtcbiAgICAgICAgZG8ge1xuICAgICAgICAgICAgcG9zMiA9IHN0cmluZy5pbmRleE9mKFwiLlwiLCBwb3MxKTtcbiAgICAgICAgICAgIGlmIChwb3MyID09PSAtMSlcbiAgICAgICAgICAgICAgICBzaWQgPSBzdHJpbmcuc3Vic3RyaW5nKHBvczEpO1xuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIHNpZCA9IHN0cmluZy5zdWJzdHJpbmcocG9zMSwgcG9zMik7XG4gICAgICAgICAgICBwb3MxID0gcG9zMiArIDE7XG4gICAgICAgICAgICBjb25zdCBzaWRCbG9jayA9IG5ldyBMb2NhbFJlbGF0aXZlU2lkVmFsdWVCbG9jaygpO1xuICAgICAgICAgICAgc2lkQmxvY2sudmFsdWVEZWMgPSBwYXJzZUludChzaWQsIDEwKTtcbiAgICAgICAgICAgIGlmIChpc05hTihzaWRCbG9jay52YWx1ZURlYykpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB0aGlzLnZhbHVlLnB1c2goc2lkQmxvY2spO1xuICAgICAgICB9IHdoaWxlIChwb3MyICE9PSAtMSk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICB0b1N0cmluZygpIHtcbiAgICAgICAgbGV0IHJlc3VsdCA9IFwiXCI7XG4gICAgICAgIGxldCBpc0hleE9ubHkgPSBmYWxzZTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnZhbHVlLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBpc0hleE9ubHkgPSB0aGlzLnZhbHVlW2ldLmlzSGV4T25seTtcbiAgICAgICAgICAgIGxldCBzaWRTdHIgPSB0aGlzLnZhbHVlW2ldLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICBpZiAoaSAhPT0gMClcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBgJHtyZXN1bHR9LmA7XG4gICAgICAgICAgICBpZiAoaXNIZXhPbmx5KSB7XG4gICAgICAgICAgICAgICAgc2lkU3RyID0gYHske3NpZFN0cn19YDtcbiAgICAgICAgICAgICAgICByZXN1bHQgKz0gc2lkU3RyO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIHJlc3VsdCArPSBzaWRTdHI7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG4gICAgdG9KU09OKCkge1xuICAgICAgICBjb25zdCBvYmplY3QgPSB7XG4gICAgICAgICAgICAuLi5zdXBlci50b0pTT04oKSxcbiAgICAgICAgICAgIHZhbHVlOiB0aGlzLnRvU3RyaW5nKCksXG4gICAgICAgICAgICBzaWRBcnJheTogW10sXG4gICAgICAgIH07XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy52YWx1ZS5sZW5ndGg7IGkrKylcbiAgICAgICAgICAgIG9iamVjdC5zaWRBcnJheS5wdXNoKHRoaXMudmFsdWVbaV0udG9KU09OKCkpO1xuICAgICAgICByZXR1cm4gb2JqZWN0O1xuICAgIH1cbn1cbkxvY2FsUmVsYXRpdmVPYmplY3RJZGVudGlmaWVyVmFsdWVCbG9jay5OQU1FID0gXCJSZWxhdGl2ZU9iamVjdElkZW50aWZpZXJWYWx1ZUJsb2NrXCI7XG5cbnZhciBfYSRsO1xuY2xhc3MgUmVsYXRpdmVPYmplY3RJZGVudGlmaWVyIGV4dGVuZHMgQmFzZUJsb2NrIHtcbiAgICBnZXRWYWx1ZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVCbG9jay50b1N0cmluZygpO1xuICAgIH1cbiAgICBzZXRWYWx1ZSh2YWx1ZSkge1xuICAgICAgICB0aGlzLnZhbHVlQmxvY2suZnJvbVN0cmluZyh2YWx1ZSk7XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzLCBMb2NhbFJlbGF0aXZlT2JqZWN0SWRlbnRpZmllclZhbHVlQmxvY2spO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gMTM7XG4gICAgfVxuICAgIG9uQXNjaWlFbmNvZGluZygpIHtcbiAgICAgICAgcmV0dXJuIGAke3RoaXMuY29uc3RydWN0b3IuTkFNRX0gOiAke3RoaXMudmFsdWVCbG9jay50b1N0cmluZygpIHx8IFwiZW1wdHlcIn1gO1xuICAgIH1cbiAgICB0b0pTT04oKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdXBlci50b0pTT04oKSxcbiAgICAgICAgICAgIHZhbHVlOiB0aGlzLmdldFZhbHVlKCksXG4gICAgICAgIH07XG4gICAgfVxufVxuX2EkbCA9IFJlbGF0aXZlT2JqZWN0SWRlbnRpZmllcjtcbigoKSA9PiB7XG4gICAgdHlwZVN0b3JlLlJlbGF0aXZlT2JqZWN0SWRlbnRpZmllciA9IF9hJGw7XG59KSgpO1xuUmVsYXRpdmVPYmplY3RJZGVudGlmaWVyLk5BTUUgPSBcIlJlbGF0aXZlT2JqZWN0SWRlbnRpZmllclwiO1xuXG52YXIgX2EkaztcbmNsYXNzIFNlcXVlbmNlIGV4dGVuZHMgQ29uc3RydWN0ZWQge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDE2O1xuICAgIH1cbn1cbl9hJGsgPSBTZXF1ZW5jZTtcbigoKSA9PiB7XG4gICAgdHlwZVN0b3JlLlNlcXVlbmNlID0gX2Ekaztcbn0pKCk7XG5TZXF1ZW5jZS5OQU1FID0gXCJTRVFVRU5DRVwiO1xuXG52YXIgX2EkajtcbmNsYXNzIFNldCBleHRlbmRzIENvbnN0cnVjdGVkIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAxNztcbiAgICB9XG59XG5fYSRqID0gU2V0O1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuU2V0ID0gX2Ekajtcbn0pKCk7XG5TZXQuTkFNRSA9IFwiU0VUXCI7XG5cbmNsYXNzIExvY2FsU3RyaW5nVmFsdWVCbG9jayBleHRlbmRzIEhleEJsb2NrKFZhbHVlQmxvY2spIHtcbiAgICBjb25zdHJ1Y3Rvcih7IC4uLnBhcmFtZXRlcnMgfSA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLmlzSGV4T25seSA9IHRydWU7XG4gICAgICAgIHRoaXMudmFsdWUgPSBFTVBUWV9TVFJJTkc7XG4gICAgfVxuICAgIHRvSlNPTigpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN1cGVyLnRvSlNPTigpLFxuICAgICAgICAgICAgdmFsdWU6IHRoaXMudmFsdWUsXG4gICAgICAgIH07XG4gICAgfVxufVxuTG9jYWxTdHJpbmdWYWx1ZUJsb2NrLk5BTUUgPSBcIlN0cmluZ1ZhbHVlQmxvY2tcIjtcblxuY2xhc3MgTG9jYWxTaW1wbGVTdHJpbmdWYWx1ZUJsb2NrIGV4dGVuZHMgTG9jYWxTdHJpbmdWYWx1ZUJsb2NrIHtcbn1cbkxvY2FsU2ltcGxlU3RyaW5nVmFsdWVCbG9jay5OQU1FID0gXCJTaW1wbGVTdHJpbmdWYWx1ZUJsb2NrXCI7XG5cbmNsYXNzIExvY2FsU2ltcGxlU3RyaW5nQmxvY2sgZXh0ZW5kcyBCYXNlU3RyaW5nQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHsgLi4ucGFyYW1ldGVycyB9ID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycywgTG9jYWxTaW1wbGVTdHJpbmdWYWx1ZUJsb2NrKTtcbiAgICB9XG4gICAgZnJvbUJ1ZmZlcihpbnB1dEJ1ZmZlcikge1xuICAgICAgICB0aGlzLnZhbHVlQmxvY2sudmFsdWUgPSBTdHJpbmcuZnJvbUNoYXJDb2RlLmFwcGx5KG51bGwsIHB2dHN1dGlscy5CdWZmZXJTb3VyY2VDb252ZXJ0ZXIudG9VaW50OEFycmF5KGlucHV0QnVmZmVyKSk7XG4gICAgfVxuICAgIGZyb21TdHJpbmcoaW5wdXRTdHJpbmcpIHtcbiAgICAgICAgY29uc3Qgc3RyTGVuID0gaW5wdXRTdHJpbmcubGVuZ3RoO1xuICAgICAgICBjb25zdCB2aWV3ID0gdGhpcy52YWx1ZUJsb2NrLnZhbHVlSGV4VmlldyA9IG5ldyBVaW50OEFycmF5KHN0ckxlbik7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyTGVuOyBpKyspXG4gICAgICAgICAgICB2aWV3W2ldID0gaW5wdXRTdHJpbmcuY2hhckNvZGVBdChpKTtcbiAgICAgICAgdGhpcy52YWx1ZUJsb2NrLnZhbHVlID0gaW5wdXRTdHJpbmc7XG4gICAgfVxufVxuTG9jYWxTaW1wbGVTdHJpbmdCbG9jay5OQU1FID0gXCJTSU1QTEUgU1RSSU5HXCI7XG5cbmNsYXNzIExvY2FsVXRmOFN0cmluZ1ZhbHVlQmxvY2sgZXh0ZW5kcyBMb2NhbFNpbXBsZVN0cmluZ0Jsb2NrIHtcbiAgICBmcm9tQnVmZmVyKGlucHV0QnVmZmVyKSB7XG4gICAgICAgIHRoaXMudmFsdWVCbG9jay52YWx1ZUhleFZpZXcgPSBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheShpbnB1dEJ1ZmZlcik7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICB0aGlzLnZhbHVlQmxvY2sudmFsdWUgPSBwdnRzdXRpbHMuQ29udmVydC5Ub1V0ZjhTdHJpbmcoaW5wdXRCdWZmZXIpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChleCkge1xuICAgICAgICAgICAgdGhpcy53YXJuaW5ncy5wdXNoKGBFcnJvciBkdXJpbmcgXCJkZWNvZGVVUklDb21wb25lbnRcIjogJHtleH0sIHVzaW5nIHJhdyBzdHJpbmdgKTtcbiAgICAgICAgICAgIHRoaXMudmFsdWVCbG9jay52YWx1ZSA9IHB2dHN1dGlscy5Db252ZXJ0LlRvQmluYXJ5KGlucHV0QnVmZmVyKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBmcm9tU3RyaW5nKGlucHV0U3RyaW5nKSB7XG4gICAgICAgIHRoaXMudmFsdWVCbG9jay52YWx1ZUhleFZpZXcgPSBuZXcgVWludDhBcnJheShwdnRzdXRpbHMuQ29udmVydC5Gcm9tVXRmOFN0cmluZyhpbnB1dFN0cmluZykpO1xuICAgICAgICB0aGlzLnZhbHVlQmxvY2sudmFsdWUgPSBpbnB1dFN0cmluZztcbiAgICB9XG59XG5Mb2NhbFV0ZjhTdHJpbmdWYWx1ZUJsb2NrLk5BTUUgPSBcIlV0ZjhTdHJpbmdWYWx1ZUJsb2NrXCI7XG5cbnZhciBfYSRpO1xuY2xhc3MgVXRmOFN0cmluZyBleHRlbmRzIExvY2FsVXRmOFN0cmluZ1ZhbHVlQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDEyO1xuICAgIH1cbn1cbl9hJGkgPSBVdGY4U3RyaW5nO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuVXRmOFN0cmluZyA9IF9hJGk7XG59KSgpO1xuVXRmOFN0cmluZy5OQU1FID0gXCJVVEY4U3RyaW5nXCI7XG5cbmNsYXNzIExvY2FsQm1wU3RyaW5nVmFsdWVCbG9jayBleHRlbmRzIExvY2FsU2ltcGxlU3RyaW5nQmxvY2sge1xuICAgIGZyb21CdWZmZXIoaW5wdXRCdWZmZXIpIHtcbiAgICAgICAgdGhpcy52YWx1ZUJsb2NrLnZhbHVlID0gcHZ0c3V0aWxzLkNvbnZlcnQuVG9VdGYxNlN0cmluZyhpbnB1dEJ1ZmZlcik7XG4gICAgICAgIHRoaXMudmFsdWVCbG9jay52YWx1ZUhleFZpZXcgPSBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheShpbnB1dEJ1ZmZlcik7XG4gICAgfVxuICAgIGZyb21TdHJpbmcoaW5wdXRTdHJpbmcpIHtcbiAgICAgICAgdGhpcy52YWx1ZUJsb2NrLnZhbHVlID0gaW5wdXRTdHJpbmc7XG4gICAgICAgIHRoaXMudmFsdWVCbG9jay52YWx1ZUhleFZpZXcgPSBuZXcgVWludDhBcnJheShwdnRzdXRpbHMuQ29udmVydC5Gcm9tVXRmMTZTdHJpbmcoaW5wdXRTdHJpbmcpKTtcbiAgICB9XG59XG5Mb2NhbEJtcFN0cmluZ1ZhbHVlQmxvY2suTkFNRSA9IFwiQm1wU3RyaW5nVmFsdWVCbG9ja1wiO1xuXG52YXIgX2EkaDtcbmNsYXNzIEJtcFN0cmluZyBleHRlbmRzIExvY2FsQm1wU3RyaW5nVmFsdWVCbG9jayB7XG4gICAgY29uc3RydWN0b3IoeyAuLi5wYXJhbWV0ZXJzIH0gPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDMwO1xuICAgIH1cbn1cbl9hJGggPSBCbXBTdHJpbmc7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5CbXBTdHJpbmcgPSBfYSRoO1xufSkoKTtcbkJtcFN0cmluZy5OQU1FID0gXCJCTVBTdHJpbmdcIjtcblxuY2xhc3MgTG9jYWxVbml2ZXJzYWxTdHJpbmdWYWx1ZUJsb2NrIGV4dGVuZHMgTG9jYWxTaW1wbGVTdHJpbmdCbG9jayB7XG4gICAgZnJvbUJ1ZmZlcihpbnB1dEJ1ZmZlcikge1xuICAgICAgICBjb25zdCBjb3B5QnVmZmVyID0gQXJyYXlCdWZmZXIuaXNWaWV3KGlucHV0QnVmZmVyKSA/IGlucHV0QnVmZmVyLnNsaWNlKCkuYnVmZmVyIDogaW5wdXRCdWZmZXIuc2xpY2UoMCk7XG4gICAgICAgIGNvbnN0IHZhbHVlVmlldyA9IG5ldyBVaW50OEFycmF5KGNvcHlCdWZmZXIpO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbHVlVmlldy5sZW5ndGg7IGkgKz0gNCkge1xuICAgICAgICAgICAgdmFsdWVWaWV3W2ldID0gdmFsdWVWaWV3W2kgKyAzXTtcbiAgICAgICAgICAgIHZhbHVlVmlld1tpICsgMV0gPSB2YWx1ZVZpZXdbaSArIDJdO1xuICAgICAgICAgICAgdmFsdWVWaWV3W2kgKyAyXSA9IDB4MDA7XG4gICAgICAgICAgICB2YWx1ZVZpZXdbaSArIDNdID0gMHgwMDtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnZhbHVlQmxvY2sudmFsdWUgPSBTdHJpbmcuZnJvbUNoYXJDb2RlLmFwcGx5KG51bGwsIG5ldyBVaW50MzJBcnJheShjb3B5QnVmZmVyKSk7XG4gICAgfVxuICAgIGZyb21TdHJpbmcoaW5wdXRTdHJpbmcpIHtcbiAgICAgICAgY29uc3Qgc3RyTGVuZ3RoID0gaW5wdXRTdHJpbmcubGVuZ3RoO1xuICAgICAgICBjb25zdCB2YWx1ZUhleFZpZXcgPSB0aGlzLnZhbHVlQmxvY2sudmFsdWVIZXhWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoc3RyTGVuZ3RoICogNCk7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyTGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IGNvZGVCdWYgPSBwdnV0aWxzLnV0aWxUb0Jhc2UoaW5wdXRTdHJpbmcuY2hhckNvZGVBdChpKSwgOCk7XG4gICAgICAgICAgICBjb25zdCBjb2RlVmlldyA9IG5ldyBVaW50OEFycmF5KGNvZGVCdWYpO1xuICAgICAgICAgICAgaWYgKGNvZGVWaWV3Lmxlbmd0aCA+IDQpXG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjb25zdCBkaWYgPSA0IC0gY29kZVZpZXcubGVuZ3RoO1xuICAgICAgICAgICAgZm9yIChsZXQgaiA9IChjb2RlVmlldy5sZW5ndGggLSAxKTsgaiA+PSAwOyBqLS0pXG4gICAgICAgICAgICAgICAgdmFsdWVIZXhWaWV3W2kgKiA0ICsgaiArIGRpZl0gPSBjb2RlVmlld1tqXTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnZhbHVlQmxvY2sudmFsdWUgPSBpbnB1dFN0cmluZztcbiAgICB9XG59XG5Mb2NhbFVuaXZlcnNhbFN0cmluZ1ZhbHVlQmxvY2suTkFNRSA9IFwiVW5pdmVyc2FsU3RyaW5nVmFsdWVCbG9ja1wiO1xuXG52YXIgX2EkZztcbmNsYXNzIFVuaXZlcnNhbFN0cmluZyBleHRlbmRzIExvY2FsVW5pdmVyc2FsU3RyaW5nVmFsdWVCbG9jayB7XG4gICAgY29uc3RydWN0b3IoeyAuLi5wYXJhbWV0ZXJzIH0gPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDI4O1xuICAgIH1cbn1cbl9hJGcgPSBVbml2ZXJzYWxTdHJpbmc7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5Vbml2ZXJzYWxTdHJpbmcgPSBfYSRnO1xufSkoKTtcblVuaXZlcnNhbFN0cmluZy5OQU1FID0gXCJVbml2ZXJzYWxTdHJpbmdcIjtcblxudmFyIF9hJGY7XG5jbGFzcyBOdW1lcmljU3RyaW5nIGV4dGVuZHMgTG9jYWxTaW1wbGVTdHJpbmdCbG9jayB7XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gMTg7XG4gICAgfVxufVxuX2EkZiA9IE51bWVyaWNTdHJpbmc7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5OdW1lcmljU3RyaW5nID0gX2EkZjtcbn0pKCk7XG5OdW1lcmljU3RyaW5nLk5BTUUgPSBcIk51bWVyaWNTdHJpbmdcIjtcblxudmFyIF9hJGU7XG5jbGFzcyBQcmludGFibGVTdHJpbmcgZXh0ZW5kcyBMb2NhbFNpbXBsZVN0cmluZ0Jsb2NrIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAxOTtcbiAgICB9XG59XG5fYSRlID0gUHJpbnRhYmxlU3RyaW5nO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuUHJpbnRhYmxlU3RyaW5nID0gX2EkZTtcbn0pKCk7XG5QcmludGFibGVTdHJpbmcuTkFNRSA9IFwiUHJpbnRhYmxlU3RyaW5nXCI7XG5cbnZhciBfYSRkO1xuY2xhc3MgVGVsZXRleFN0cmluZyBleHRlbmRzIExvY2FsU2ltcGxlU3RyaW5nQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDIwO1xuICAgIH1cbn1cbl9hJGQgPSBUZWxldGV4U3RyaW5nO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuVGVsZXRleFN0cmluZyA9IF9hJGQ7XG59KSgpO1xuVGVsZXRleFN0cmluZy5OQU1FID0gXCJUZWxldGV4U3RyaW5nXCI7XG5cbnZhciBfYSRjO1xuY2xhc3MgVmlkZW90ZXhTdHJpbmcgZXh0ZW5kcyBMb2NhbFNpbXBsZVN0cmluZ0Jsb2NrIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAyMTtcbiAgICB9XG59XG5fYSRjID0gVmlkZW90ZXhTdHJpbmc7XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5WaWRlb3RleFN0cmluZyA9IF9hJGM7XG59KSgpO1xuVmlkZW90ZXhTdHJpbmcuTkFNRSA9IFwiVmlkZW90ZXhTdHJpbmdcIjtcblxudmFyIF9hJGI7XG5jbGFzcyBJQTVTdHJpbmcgZXh0ZW5kcyBMb2NhbFNpbXBsZVN0cmluZ0Jsb2NrIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAyMjtcbiAgICB9XG59XG5fYSRiID0gSUE1U3RyaW5nO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuSUE1U3RyaW5nID0gX2EkYjtcbn0pKCk7XG5JQTVTdHJpbmcuTkFNRSA9IFwiSUE1U3RyaW5nXCI7XG5cbnZhciBfYSRhO1xuY2xhc3MgR3JhcGhpY1N0cmluZyBleHRlbmRzIExvY2FsU2ltcGxlU3RyaW5nQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDI1O1xuICAgIH1cbn1cbl9hJGEgPSBHcmFwaGljU3RyaW5nO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuR3JhcGhpY1N0cmluZyA9IF9hJGE7XG59KSgpO1xuR3JhcGhpY1N0cmluZy5OQU1FID0gXCJHcmFwaGljU3RyaW5nXCI7XG5cbnZhciBfYSQ5O1xuY2xhc3MgVmlzaWJsZVN0cmluZyBleHRlbmRzIExvY2FsU2ltcGxlU3RyaW5nQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDI2O1xuICAgIH1cbn1cbl9hJDkgPSBWaXNpYmxlU3RyaW5nO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuVmlzaWJsZVN0cmluZyA9IF9hJDk7XG59KSgpO1xuVmlzaWJsZVN0cmluZy5OQU1FID0gXCJWaXNpYmxlU3RyaW5nXCI7XG5cbnZhciBfYSQ4O1xuY2xhc3MgR2VuZXJhbFN0cmluZyBleHRlbmRzIExvY2FsU2ltcGxlU3RyaW5nQmxvY2sge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDI3O1xuICAgIH1cbn1cbl9hJDggPSBHZW5lcmFsU3RyaW5nO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuR2VuZXJhbFN0cmluZyA9IF9hJDg7XG59KSgpO1xuR2VuZXJhbFN0cmluZy5OQU1FID0gXCJHZW5lcmFsU3RyaW5nXCI7XG5cbnZhciBfYSQ3O1xuY2xhc3MgQ2hhcmFjdGVyU3RyaW5nIGV4dGVuZHMgTG9jYWxTaW1wbGVTdHJpbmdCbG9jayB7XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gMjk7XG4gICAgfVxufVxuX2EkNyA9IENoYXJhY3RlclN0cmluZztcbigoKSA9PiB7XG4gICAgdHlwZVN0b3JlLkNoYXJhY3RlclN0cmluZyA9IF9hJDc7XG59KSgpO1xuQ2hhcmFjdGVyU3RyaW5nLk5BTUUgPSBcIkNoYXJhY3RlclN0cmluZ1wiO1xuXG52YXIgX2EkNjtcbmNsYXNzIFVUQ1RpbWUgZXh0ZW5kcyBWaXNpYmxlU3RyaW5nIHtcbiAgICBjb25zdHJ1Y3Rvcih7IHZhbHVlLCB2YWx1ZURhdGUsIC4uLnBhcmFtZXRlcnMgfSA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLnllYXIgPSAwO1xuICAgICAgICB0aGlzLm1vbnRoID0gMDtcbiAgICAgICAgdGhpcy5kYXkgPSAwO1xuICAgICAgICB0aGlzLmhvdXIgPSAwO1xuICAgICAgICB0aGlzLm1pbnV0ZSA9IDA7XG4gICAgICAgIHRoaXMuc2Vjb25kID0gMDtcbiAgICAgICAgaWYgKHZhbHVlKSB7XG4gICAgICAgICAgICB0aGlzLmZyb21TdHJpbmcodmFsdWUpO1xuICAgICAgICAgICAgdGhpcy52YWx1ZUJsb2NrLnZhbHVlSGV4VmlldyA9IG5ldyBVaW50OEFycmF5KHZhbHVlLmxlbmd0aCk7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbHVlLmxlbmd0aDsgaSsrKVxuICAgICAgICAgICAgICAgIHRoaXMudmFsdWVCbG9jay52YWx1ZUhleFZpZXdbaV0gPSB2YWx1ZS5jaGFyQ29kZUF0KGkpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh2YWx1ZURhdGUpIHtcbiAgICAgICAgICAgIHRoaXMuZnJvbURhdGUodmFsdWVEYXRlKTtcbiAgICAgICAgICAgIHRoaXMudmFsdWVCbG9jay52YWx1ZUhleFZpZXcgPSBuZXcgVWludDhBcnJheSh0aGlzLnRvQnVmZmVyKCkpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAyMztcbiAgICB9XG4gICAgZnJvbUJ1ZmZlcihpbnB1dEJ1ZmZlcikge1xuICAgICAgICB0aGlzLmZyb21TdHJpbmcoU3RyaW5nLmZyb21DaGFyQ29kZS5hcHBseShudWxsLCBwdnRzdXRpbHMuQnVmZmVyU291cmNlQ29udmVydGVyLnRvVWludDhBcnJheShpbnB1dEJ1ZmZlcikpKTtcbiAgICB9XG4gICAgdG9CdWZmZXIoKSB7XG4gICAgICAgIGNvbnN0IHN0ciA9IHRoaXMudG9TdHJpbmcoKTtcbiAgICAgICAgY29uc3QgYnVmZmVyID0gbmV3IEFycmF5QnVmZmVyKHN0ci5sZW5ndGgpO1xuICAgICAgICBjb25zdCB2aWV3ID0gbmV3IFVpbnQ4QXJyYXkoYnVmZmVyKTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdHIubGVuZ3RoOyBpKyspXG4gICAgICAgICAgICB2aWV3W2ldID0gc3RyLmNoYXJDb2RlQXQoaSk7XG4gICAgICAgIHJldHVybiBidWZmZXI7XG4gICAgfVxuICAgIGZyb21EYXRlKGlucHV0RGF0ZSkge1xuICAgICAgICB0aGlzLnllYXIgPSBpbnB1dERhdGUuZ2V0VVRDRnVsbFllYXIoKTtcbiAgICAgICAgdGhpcy5tb250aCA9IGlucHV0RGF0ZS5nZXRVVENNb250aCgpICsgMTtcbiAgICAgICAgdGhpcy5kYXkgPSBpbnB1dERhdGUuZ2V0VVRDRGF0ZSgpO1xuICAgICAgICB0aGlzLmhvdXIgPSBpbnB1dERhdGUuZ2V0VVRDSG91cnMoKTtcbiAgICAgICAgdGhpcy5taW51dGUgPSBpbnB1dERhdGUuZ2V0VVRDTWludXRlcygpO1xuICAgICAgICB0aGlzLnNlY29uZCA9IGlucHV0RGF0ZS5nZXRVVENTZWNvbmRzKCk7XG4gICAgfVxuICAgIHRvRGF0ZSgpIHtcbiAgICAgICAgcmV0dXJuIChuZXcgRGF0ZShEYXRlLlVUQyh0aGlzLnllYXIsIHRoaXMubW9udGggLSAxLCB0aGlzLmRheSwgdGhpcy5ob3VyLCB0aGlzLm1pbnV0ZSwgdGhpcy5zZWNvbmQpKSk7XG4gICAgfVxuICAgIGZyb21TdHJpbmcoaW5wdXRTdHJpbmcpIHtcbiAgICAgICAgY29uc3QgcGFyc2VyID0gLyhcXGR7Mn0pKFxcZHsyfSkoXFxkezJ9KShcXGR7Mn0pKFxcZHsyfSkoXFxkezJ9KVovaWc7XG4gICAgICAgIGNvbnN0IHBhcnNlckFycmF5ID0gcGFyc2VyLmV4ZWMoaW5wdXRTdHJpbmcpO1xuICAgICAgICBpZiAocGFyc2VyQXJyYXkgPT09IG51bGwpIHtcbiAgICAgICAgICAgIHRoaXMuZXJyb3IgPSBcIldyb25nIGlucHV0IHN0cmluZyBmb3IgY29udmVyc2lvblwiO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHllYXIgPSBwYXJzZUludChwYXJzZXJBcnJheVsxXSwgMTApO1xuICAgICAgICBpZiAoeWVhciA+PSA1MClcbiAgICAgICAgICAgIHRoaXMueWVhciA9IDE5MDAgKyB5ZWFyO1xuICAgICAgICBlbHNlXG4gICAgICAgICAgICB0aGlzLnllYXIgPSAyMDAwICsgeWVhcjtcbiAgICAgICAgdGhpcy5tb250aCA9IHBhcnNlSW50KHBhcnNlckFycmF5WzJdLCAxMCk7XG4gICAgICAgIHRoaXMuZGF5ID0gcGFyc2VJbnQocGFyc2VyQXJyYXlbM10sIDEwKTtcbiAgICAgICAgdGhpcy5ob3VyID0gcGFyc2VJbnQocGFyc2VyQXJyYXlbNF0sIDEwKTtcbiAgICAgICAgdGhpcy5taW51dGUgPSBwYXJzZUludChwYXJzZXJBcnJheVs1XSwgMTApO1xuICAgICAgICB0aGlzLnNlY29uZCA9IHBhcnNlSW50KHBhcnNlckFycmF5WzZdLCAxMCk7XG4gICAgfVxuICAgIHRvU3RyaW5nKGVuY29kaW5nID0gXCJpc29cIikge1xuICAgICAgICBpZiAoZW5jb2RpbmcgPT09IFwiaXNvXCIpIHtcbiAgICAgICAgICAgIGNvbnN0IG91dHB1dEFycmF5ID0gbmV3IEFycmF5KDcpO1xuICAgICAgICAgICAgb3V0cHV0QXJyYXlbMF0gPSBwdnV0aWxzLnBhZE51bWJlcigoKHRoaXMueWVhciA8IDIwMDApID8gKHRoaXMueWVhciAtIDE5MDApIDogKHRoaXMueWVhciAtIDIwMDApKSwgMik7XG4gICAgICAgICAgICBvdXRwdXRBcnJheVsxXSA9IHB2dXRpbHMucGFkTnVtYmVyKHRoaXMubW9udGgsIDIpO1xuICAgICAgICAgICAgb3V0cHV0QXJyYXlbMl0gPSBwdnV0aWxzLnBhZE51bWJlcih0aGlzLmRheSwgMik7XG4gICAgICAgICAgICBvdXRwdXRBcnJheVszXSA9IHB2dXRpbHMucGFkTnVtYmVyKHRoaXMuaG91ciwgMik7XG4gICAgICAgICAgICBvdXRwdXRBcnJheVs0XSA9IHB2dXRpbHMucGFkTnVtYmVyKHRoaXMubWludXRlLCAyKTtcbiAgICAgICAgICAgIG91dHB1dEFycmF5WzVdID0gcHZ1dGlscy5wYWROdW1iZXIodGhpcy5zZWNvbmQsIDIpO1xuICAgICAgICAgICAgb3V0cHV0QXJyYXlbNl0gPSBcIlpcIjtcbiAgICAgICAgICAgIHJldHVybiBvdXRwdXRBcnJheS5qb2luKFwiXCIpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBzdXBlci50b1N0cmluZyhlbmNvZGluZyk7XG4gICAgfVxuICAgIG9uQXNjaWlFbmNvZGluZygpIHtcbiAgICAgICAgcmV0dXJuIGAke3RoaXMuY29uc3RydWN0b3IuTkFNRX0gOiAke3RoaXMudG9EYXRlKCkudG9JU09TdHJpbmcoKX1gO1xuICAgIH1cbiAgICB0b0pTT04oKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdXBlci50b0pTT04oKSxcbiAgICAgICAgICAgIHllYXI6IHRoaXMueWVhcixcbiAgICAgICAgICAgIG1vbnRoOiB0aGlzLm1vbnRoLFxuICAgICAgICAgICAgZGF5OiB0aGlzLmRheSxcbiAgICAgICAgICAgIGhvdXI6IHRoaXMuaG91cixcbiAgICAgICAgICAgIG1pbnV0ZTogdGhpcy5taW51dGUsXG4gICAgICAgICAgICBzZWNvbmQ6IHRoaXMuc2Vjb25kLFxuICAgICAgICB9O1xuICAgIH1cbn1cbl9hJDYgPSBVVENUaW1lO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuVVRDVGltZSA9IF9hJDY7XG59KSgpO1xuVVRDVGltZS5OQU1FID0gXCJVVENUaW1lXCI7XG5cbnZhciBfYSQ1O1xuY2xhc3MgR2VuZXJhbGl6ZWRUaW1lIGV4dGVuZHMgVVRDVGltZSB7XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHZhciBfYjtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIChfYiA9IHRoaXMubWlsbGlzZWNvbmQpICE9PSBudWxsICYmIF9iICE9PSB2b2lkIDAgPyBfYiA6ICh0aGlzLm1pbGxpc2Vjb25kID0gMCk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAyNDtcbiAgICB9XG4gICAgZnJvbURhdGUoaW5wdXREYXRlKSB7XG4gICAgICAgIHN1cGVyLmZyb21EYXRlKGlucHV0RGF0ZSk7XG4gICAgICAgIHRoaXMubWlsbGlzZWNvbmQgPSBpbnB1dERhdGUuZ2V0VVRDTWlsbGlzZWNvbmRzKCk7XG4gICAgfVxuICAgIHRvRGF0ZSgpIHtcbiAgICAgICAgY29uc3QgdXRjRGF0ZSA9IERhdGUuVVRDKHRoaXMueWVhciwgdGhpcy5tb250aCAtIDEsIHRoaXMuZGF5LCB0aGlzLmhvdXIsIHRoaXMubWludXRlLCB0aGlzLnNlY29uZCwgdGhpcy5taWxsaXNlY29uZCk7XG4gICAgICAgIHJldHVybiAobmV3IERhdGUodXRjRGF0ZSkpO1xuICAgIH1cbiAgICBmcm9tU3RyaW5nKGlucHV0U3RyaW5nKSB7XG4gICAgICAgIGxldCBpc1VUQyA9IGZhbHNlO1xuICAgICAgICBsZXQgdGltZVN0cmluZyA9IFwiXCI7XG4gICAgICAgIGxldCBkYXRlVGltZVN0cmluZyA9IFwiXCI7XG4gICAgICAgIGxldCBmcmFjdGlvblBhcnQgPSAwO1xuICAgICAgICBsZXQgcGFyc2VyO1xuICAgICAgICBsZXQgaG91ckRpZmZlcmVuY2UgPSAwO1xuICAgICAgICBsZXQgbWludXRlRGlmZmVyZW5jZSA9IDA7XG4gICAgICAgIGlmIChpbnB1dFN0cmluZ1tpbnB1dFN0cmluZy5sZW5ndGggLSAxXSA9PT0gXCJaXCIpIHtcbiAgICAgICAgICAgIHRpbWVTdHJpbmcgPSBpbnB1dFN0cmluZy5zdWJzdHJpbmcoMCwgaW5wdXRTdHJpbmcubGVuZ3RoIC0gMSk7XG4gICAgICAgICAgICBpc1VUQyA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCBudW1iZXIgPSBuZXcgTnVtYmVyKGlucHV0U3RyaW5nW2lucHV0U3RyaW5nLmxlbmd0aCAtIDFdKTtcbiAgICAgICAgICAgIGlmIChpc05hTihudW1iZXIudmFsdWVPZigpKSlcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJXcm9uZyBpbnB1dCBzdHJpbmcgZm9yIGNvbnZlcnNpb25cIik7XG4gICAgICAgICAgICB0aW1lU3RyaW5nID0gaW5wdXRTdHJpbmc7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGlzVVRDKSB7XG4gICAgICAgICAgICBpZiAodGltZVN0cmluZy5pbmRleE9mKFwiK1wiKSAhPT0gLTEpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiV3JvbmcgaW5wdXQgc3RyaW5nIGZvciBjb252ZXJzaW9uXCIpO1xuICAgICAgICAgICAgaWYgKHRpbWVTdHJpbmcuaW5kZXhPZihcIi1cIikgIT09IC0xKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIldyb25nIGlucHV0IHN0cmluZyBmb3IgY29udmVyc2lvblwiKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGxldCBtdWx0aXBsaWVyID0gMTtcbiAgICAgICAgICAgIGxldCBkaWZmZXJlbmNlUG9zaXRpb24gPSB0aW1lU3RyaW5nLmluZGV4T2YoXCIrXCIpO1xuICAgICAgICAgICAgbGV0IGRpZmZlcmVuY2VTdHJpbmcgPSBcIlwiO1xuICAgICAgICAgICAgaWYgKGRpZmZlcmVuY2VQb3NpdGlvbiA9PT0gLTEpIHtcbiAgICAgICAgICAgICAgICBkaWZmZXJlbmNlUG9zaXRpb24gPSB0aW1lU3RyaW5nLmluZGV4T2YoXCItXCIpO1xuICAgICAgICAgICAgICAgIG11bHRpcGxpZXIgPSAtMTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChkaWZmZXJlbmNlUG9zaXRpb24gIT09IC0xKSB7XG4gICAgICAgICAgICAgICAgZGlmZmVyZW5jZVN0cmluZyA9IHRpbWVTdHJpbmcuc3Vic3RyaW5nKGRpZmZlcmVuY2VQb3NpdGlvbiArIDEpO1xuICAgICAgICAgICAgICAgIHRpbWVTdHJpbmcgPSB0aW1lU3RyaW5nLnN1YnN0cmluZygwLCBkaWZmZXJlbmNlUG9zaXRpb24pO1xuICAgICAgICAgICAgICAgIGlmICgoZGlmZmVyZW5jZVN0cmluZy5sZW5ndGggIT09IDIpICYmIChkaWZmZXJlbmNlU3RyaW5nLmxlbmd0aCAhPT0gNCkpXG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIldyb25nIGlucHV0IHN0cmluZyBmb3IgY29udmVyc2lvblwiKTtcbiAgICAgICAgICAgICAgICBsZXQgbnVtYmVyID0gcGFyc2VJbnQoZGlmZmVyZW5jZVN0cmluZy5zdWJzdHJpbmcoMCwgMiksIDEwKTtcbiAgICAgICAgICAgICAgICBpZiAoaXNOYU4obnVtYmVyLnZhbHVlT2YoKSkpXG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIldyb25nIGlucHV0IHN0cmluZyBmb3IgY29udmVyc2lvblwiKTtcbiAgICAgICAgICAgICAgICBob3VyRGlmZmVyZW5jZSA9IG11bHRpcGxpZXIgKiBudW1iZXI7XG4gICAgICAgICAgICAgICAgaWYgKGRpZmZlcmVuY2VTdHJpbmcubGVuZ3RoID09PSA0KSB7XG4gICAgICAgICAgICAgICAgICAgIG51bWJlciA9IHBhcnNlSW50KGRpZmZlcmVuY2VTdHJpbmcuc3Vic3RyaW5nKDIsIDQpLCAxMCk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc05hTihudW1iZXIudmFsdWVPZigpKSlcbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIldyb25nIGlucHV0IHN0cmluZyBmb3IgY29udmVyc2lvblwiKTtcbiAgICAgICAgICAgICAgICAgICAgbWludXRlRGlmZmVyZW5jZSA9IG11bHRpcGxpZXIgKiBudW1iZXI7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGxldCBmcmFjdGlvblBvaW50UG9zaXRpb24gPSB0aW1lU3RyaW5nLmluZGV4T2YoXCIuXCIpO1xuICAgICAgICBpZiAoZnJhY3Rpb25Qb2ludFBvc2l0aW9uID09PSAtMSlcbiAgICAgICAgICAgIGZyYWN0aW9uUG9pbnRQb3NpdGlvbiA9IHRpbWVTdHJpbmcuaW5kZXhPZihcIixcIik7XG4gICAgICAgIGlmIChmcmFjdGlvblBvaW50UG9zaXRpb24gIT09IC0xKSB7XG4gICAgICAgICAgICBjb25zdCBmcmFjdGlvblBhcnRDaGVjayA9IG5ldyBOdW1iZXIoYDAke3RpbWVTdHJpbmcuc3Vic3RyaW5nKGZyYWN0aW9uUG9pbnRQb3NpdGlvbil9YCk7XG4gICAgICAgICAgICBpZiAoaXNOYU4oZnJhY3Rpb25QYXJ0Q2hlY2sudmFsdWVPZigpKSlcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJXcm9uZyBpbnB1dCBzdHJpbmcgZm9yIGNvbnZlcnNpb25cIik7XG4gICAgICAgICAgICBmcmFjdGlvblBhcnQgPSBmcmFjdGlvblBhcnRDaGVjay52YWx1ZU9mKCk7XG4gICAgICAgICAgICBkYXRlVGltZVN0cmluZyA9IHRpbWVTdHJpbmcuc3Vic3RyaW5nKDAsIGZyYWN0aW9uUG9pbnRQb3NpdGlvbik7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZVxuICAgICAgICAgICAgZGF0ZVRpbWVTdHJpbmcgPSB0aW1lU3RyaW5nO1xuICAgICAgICBzd2l0Y2ggKHRydWUpIHtcbiAgICAgICAgICAgIGNhc2UgKGRhdGVUaW1lU3RyaW5nLmxlbmd0aCA9PT0gOCk6XG4gICAgICAgICAgICAgICAgcGFyc2VyID0gLyhcXGR7NH0pKFxcZHsyfSkoXFxkezJ9KS9pZztcbiAgICAgICAgICAgICAgICBpZiAoZnJhY3Rpb25Qb2ludFBvc2l0aW9uICE9PSAtMSlcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiV3JvbmcgaW5wdXQgc3RyaW5nIGZvciBjb252ZXJzaW9uXCIpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAoZGF0ZVRpbWVTdHJpbmcubGVuZ3RoID09PSAxMCk6XG4gICAgICAgICAgICAgICAgcGFyc2VyID0gLyhcXGR7NH0pKFxcZHsyfSkoXFxkezJ9KShcXGR7Mn0pL2lnO1xuICAgICAgICAgICAgICAgIGlmIChmcmFjdGlvblBvaW50UG9zaXRpb24gIT09IC0xKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBmcmFjdGlvblJlc3VsdCA9IDYwICogZnJhY3Rpb25QYXJ0O1xuICAgICAgICAgICAgICAgICAgICB0aGlzLm1pbnV0ZSA9IE1hdGguZmxvb3IoZnJhY3Rpb25SZXN1bHQpO1xuICAgICAgICAgICAgICAgICAgICBmcmFjdGlvblJlc3VsdCA9IDYwICogKGZyYWN0aW9uUmVzdWx0IC0gdGhpcy5taW51dGUpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNlY29uZCA9IE1hdGguZmxvb3IoZnJhY3Rpb25SZXN1bHQpO1xuICAgICAgICAgICAgICAgICAgICBmcmFjdGlvblJlc3VsdCA9IDEwMDAgKiAoZnJhY3Rpb25SZXN1bHQgLSB0aGlzLnNlY29uZCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMubWlsbGlzZWNvbmQgPSBNYXRoLmZsb29yKGZyYWN0aW9uUmVzdWx0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIChkYXRlVGltZVN0cmluZy5sZW5ndGggPT09IDEyKTpcbiAgICAgICAgICAgICAgICBwYXJzZXIgPSAvKFxcZHs0fSkoXFxkezJ9KShcXGR7Mn0pKFxcZHsyfSkoXFxkezJ9KS9pZztcbiAgICAgICAgICAgICAgICBpZiAoZnJhY3Rpb25Qb2ludFBvc2l0aW9uICE9PSAtMSkge1xuICAgICAgICAgICAgICAgICAgICBsZXQgZnJhY3Rpb25SZXN1bHQgPSA2MCAqIGZyYWN0aW9uUGFydDtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWNvbmQgPSBNYXRoLmZsb29yKGZyYWN0aW9uUmVzdWx0KTtcbiAgICAgICAgICAgICAgICAgICAgZnJhY3Rpb25SZXN1bHQgPSAxMDAwICogKGZyYWN0aW9uUmVzdWx0IC0gdGhpcy5zZWNvbmQpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLm1pbGxpc2Vjb25kID0gTWF0aC5mbG9vcihmcmFjdGlvblJlc3VsdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAoZGF0ZVRpbWVTdHJpbmcubGVuZ3RoID09PSAxNCk6XG4gICAgICAgICAgICAgICAgcGFyc2VyID0gLyhcXGR7NH0pKFxcZHsyfSkoXFxkezJ9KShcXGR7Mn0pKFxcZHsyfSkoXFxkezJ9KS9pZztcbiAgICAgICAgICAgICAgICBpZiAoZnJhY3Rpb25Qb2ludFBvc2l0aW9uICE9PSAtMSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBmcmFjdGlvblJlc3VsdCA9IDEwMDAgKiBmcmFjdGlvblBhcnQ7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMubWlsbGlzZWNvbmQgPSBNYXRoLmZsb29yKGZyYWN0aW9uUmVzdWx0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIldyb25nIGlucHV0IHN0cmluZyBmb3IgY29udmVyc2lvblwiKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBwYXJzZXJBcnJheSA9IHBhcnNlci5leGVjKGRhdGVUaW1lU3RyaW5nKTtcbiAgICAgICAgaWYgKHBhcnNlckFycmF5ID09PSBudWxsKVxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiV3JvbmcgaW5wdXQgc3RyaW5nIGZvciBjb252ZXJzaW9uXCIpO1xuICAgICAgICBmb3IgKGxldCBqID0gMTsgaiA8IHBhcnNlckFycmF5Lmxlbmd0aDsgaisrKSB7XG4gICAgICAgICAgICBzd2l0Y2ggKGopIHtcbiAgICAgICAgICAgICAgICBjYXNlIDE6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMueWVhciA9IHBhcnNlSW50KHBhcnNlckFycmF5W2pdLCAxMCk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMjpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5tb250aCA9IHBhcnNlSW50KHBhcnNlckFycmF5W2pdLCAxMCk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgMzpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kYXkgPSBwYXJzZUludChwYXJzZXJBcnJheVtqXSwgMTApO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDQ6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuaG91ciA9IHBhcnNlSW50KHBhcnNlckFycmF5W2pdLCAxMCkgKyBob3VyRGlmZmVyZW5jZTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSA1OlxuICAgICAgICAgICAgICAgICAgICB0aGlzLm1pbnV0ZSA9IHBhcnNlSW50KHBhcnNlckFycmF5W2pdLCAxMCkgKyBtaW51dGVEaWZmZXJlbmNlO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDY6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2Vjb25kID0gcGFyc2VJbnQocGFyc2VyQXJyYXlbal0sIDEwKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiV3JvbmcgaW5wdXQgc3RyaW5nIGZvciBjb252ZXJzaW9uXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChpc1VUQyA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgIGNvbnN0IHRlbXBEYXRlID0gbmV3IERhdGUodGhpcy55ZWFyLCB0aGlzLm1vbnRoLCB0aGlzLmRheSwgdGhpcy5ob3VyLCB0aGlzLm1pbnV0ZSwgdGhpcy5zZWNvbmQsIHRoaXMubWlsbGlzZWNvbmQpO1xuICAgICAgICAgICAgdGhpcy55ZWFyID0gdGVtcERhdGUuZ2V0VVRDRnVsbFllYXIoKTtcbiAgICAgICAgICAgIHRoaXMubW9udGggPSB0ZW1wRGF0ZS5nZXRVVENNb250aCgpO1xuICAgICAgICAgICAgdGhpcy5kYXkgPSB0ZW1wRGF0ZS5nZXRVVENEYXkoKTtcbiAgICAgICAgICAgIHRoaXMuaG91ciA9IHRlbXBEYXRlLmdldFVUQ0hvdXJzKCk7XG4gICAgICAgICAgICB0aGlzLm1pbnV0ZSA9IHRlbXBEYXRlLmdldFVUQ01pbnV0ZXMoKTtcbiAgICAgICAgICAgIHRoaXMuc2Vjb25kID0gdGVtcERhdGUuZ2V0VVRDU2Vjb25kcygpO1xuICAgICAgICAgICAgdGhpcy5taWxsaXNlY29uZCA9IHRlbXBEYXRlLmdldFVUQ01pbGxpc2Vjb25kcygpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHRvU3RyaW5nKGVuY29kaW5nID0gXCJpc29cIikge1xuICAgICAgICBpZiAoZW5jb2RpbmcgPT09IFwiaXNvXCIpIHtcbiAgICAgICAgICAgIGNvbnN0IG91dHB1dEFycmF5ID0gW107XG4gICAgICAgICAgICBvdXRwdXRBcnJheS5wdXNoKHB2dXRpbHMucGFkTnVtYmVyKHRoaXMueWVhciwgNCkpO1xuICAgICAgICAgICAgb3V0cHV0QXJyYXkucHVzaChwdnV0aWxzLnBhZE51bWJlcih0aGlzLm1vbnRoLCAyKSk7XG4gICAgICAgICAgICBvdXRwdXRBcnJheS5wdXNoKHB2dXRpbHMucGFkTnVtYmVyKHRoaXMuZGF5LCAyKSk7XG4gICAgICAgICAgICBvdXRwdXRBcnJheS5wdXNoKHB2dXRpbHMucGFkTnVtYmVyKHRoaXMuaG91ciwgMikpO1xuICAgICAgICAgICAgb3V0cHV0QXJyYXkucHVzaChwdnV0aWxzLnBhZE51bWJlcih0aGlzLm1pbnV0ZSwgMikpO1xuICAgICAgICAgICAgb3V0cHV0QXJyYXkucHVzaChwdnV0aWxzLnBhZE51bWJlcih0aGlzLnNlY29uZCwgMikpO1xuICAgICAgICAgICAgaWYgKHRoaXMubWlsbGlzZWNvbmQgIT09IDApIHtcbiAgICAgICAgICAgICAgICBvdXRwdXRBcnJheS5wdXNoKFwiLlwiKTtcbiAgICAgICAgICAgICAgICBvdXRwdXRBcnJheS5wdXNoKHB2dXRpbHMucGFkTnVtYmVyKHRoaXMubWlsbGlzZWNvbmQsIDMpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIG91dHB1dEFycmF5LnB1c2goXCJaXCIpO1xuICAgICAgICAgICAgcmV0dXJuIG91dHB1dEFycmF5LmpvaW4oXCJcIik7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHN1cGVyLnRvU3RyaW5nKGVuY29kaW5nKTtcbiAgICB9XG4gICAgdG9KU09OKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3VwZXIudG9KU09OKCksXG4gICAgICAgICAgICBtaWxsaXNlY29uZDogdGhpcy5taWxsaXNlY29uZCxcbiAgICAgICAgfTtcbiAgICB9XG59XG5fYSQ1ID0gR2VuZXJhbGl6ZWRUaW1lO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuR2VuZXJhbGl6ZWRUaW1lID0gX2EkNTtcbn0pKCk7XG5HZW5lcmFsaXplZFRpbWUuTkFNRSA9IFwiR2VuZXJhbGl6ZWRUaW1lXCI7XG5cbnZhciBfYSQ0O1xuY2xhc3MgREFURSBleHRlbmRzIFV0ZjhTdHJpbmcge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDMxO1xuICAgIH1cbn1cbl9hJDQgPSBEQVRFO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuREFURSA9IF9hJDQ7XG59KSgpO1xuREFURS5OQU1FID0gXCJEQVRFXCI7XG5cbnZhciBfYSQzO1xuY2xhc3MgVGltZU9mRGF5IGV4dGVuZHMgVXRmOFN0cmluZyB7XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gMzI7XG4gICAgfVxufVxuX2EkMyA9IFRpbWVPZkRheTtcbigoKSA9PiB7XG4gICAgdHlwZVN0b3JlLlRpbWVPZkRheSA9IF9hJDM7XG59KSgpO1xuVGltZU9mRGF5Lk5BTUUgPSBcIlRpbWVPZkRheVwiO1xuXG52YXIgX2EkMjtcbmNsYXNzIERhdGVUaW1lIGV4dGVuZHMgVXRmOFN0cmluZyB7XG4gICAgY29uc3RydWN0b3IocGFyYW1ldGVycyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnQ2xhc3MgPSAxO1xuICAgICAgICB0aGlzLmlkQmxvY2sudGFnTnVtYmVyID0gMzM7XG4gICAgfVxufVxuX2EkMiA9IERhdGVUaW1lO1xuKCgpID0+IHtcbiAgICB0eXBlU3RvcmUuRGF0ZVRpbWUgPSBfYSQyO1xufSkoKTtcbkRhdGVUaW1lLk5BTUUgPSBcIkRhdGVUaW1lXCI7XG5cbnZhciBfYSQxO1xuY2xhc3MgRHVyYXRpb24gZXh0ZW5kcyBVdGY4U3RyaW5nIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgICAgc3VwZXIocGFyYW1ldGVycyk7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdDbGFzcyA9IDE7XG4gICAgICAgIHRoaXMuaWRCbG9jay50YWdOdW1iZXIgPSAzNDtcbiAgICB9XG59XG5fYSQxID0gRHVyYXRpb247XG4oKCkgPT4ge1xuICAgIHR5cGVTdG9yZS5EdXJhdGlvbiA9IF9hJDE7XG59KSgpO1xuRHVyYXRpb24uTkFNRSA9IFwiRHVyYXRpb25cIjtcblxudmFyIF9hO1xuY2xhc3MgVElNRSBleHRlbmRzIFV0ZjhTdHJpbmcge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtZXRlcnMgPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ0NsYXNzID0gMTtcbiAgICAgICAgdGhpcy5pZEJsb2NrLnRhZ051bWJlciA9IDE0O1xuICAgIH1cbn1cbl9hID0gVElNRTtcbigoKSA9PiB7XG4gICAgdHlwZVN0b3JlLlRJTUUgPSBfYTtcbn0pKCk7XG5USU1FLk5BTUUgPSBcIlRJTUVcIjtcblxuY2xhc3MgQW55IHtcbiAgICBjb25zdHJ1Y3Rvcih7IG5hbWUgPSBFTVBUWV9TVFJJTkcsIG9wdGlvbmFsID0gZmFsc2UgfSA9IHt9KSB7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgICAgIHRoaXMub3B0aW9uYWwgPSBvcHRpb25hbDtcbiAgICB9XG59XG5cbmNsYXNzIENob2ljZSBleHRlbmRzIEFueSB7XG4gICAgY29uc3RydWN0b3IoeyB2YWx1ZSA9IFtdLCAuLi5wYXJhbWV0ZXJzIH0gPSB7fSkge1xuICAgICAgICBzdXBlcihwYXJhbWV0ZXJzKTtcbiAgICAgICAgdGhpcy52YWx1ZSA9IHZhbHVlO1xuICAgIH1cbn1cblxuY2xhc3MgUmVwZWF0ZWQgZXh0ZW5kcyBBbnkge1xuICAgIGNvbnN0cnVjdG9yKHsgdmFsdWUgPSBuZXcgQW55KCksIGxvY2FsID0gZmFsc2UsIC4uLnBhcmFtZXRlcnMgfSA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHBhcmFtZXRlcnMpO1xuICAgICAgICB0aGlzLnZhbHVlID0gdmFsdWU7XG4gICAgICAgIHRoaXMubG9jYWwgPSBsb2NhbDtcbiAgICB9XG59XG5cbmNsYXNzIFJhd0RhdGEge1xuICAgIGdldCBkYXRhKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5kYXRhVmlldy5zbGljZSgpLmJ1ZmZlcjtcbiAgICB9XG4gICAgc2V0IGRhdGEodmFsdWUpIHtcbiAgICAgICAgdGhpcy5kYXRhVmlldyA9IHB2dHN1dGlscy5CdWZmZXJTb3VyY2VDb252ZXJ0ZXIudG9VaW50OEFycmF5KHZhbHVlKTtcbiAgICB9XG4gICAgY29uc3RydWN0b3IoeyBkYXRhID0gRU1QVFlfVklFVyB9ID0ge30pIHtcbiAgICAgICAgdGhpcy5kYXRhVmlldyA9IHB2dHN1dGlscy5CdWZmZXJTb3VyY2VDb252ZXJ0ZXIudG9VaW50OEFycmF5KGRhdGEpO1xuICAgIH1cbiAgICBmcm9tQkVSKGlucHV0QnVmZmVyLCBpbnB1dE9mZnNldCwgaW5wdXRMZW5ndGgpIHtcbiAgICAgICAgY29uc3QgZW5kTGVuZ3RoID0gaW5wdXRPZmZzZXQgKyBpbnB1dExlbmd0aDtcbiAgICAgICAgdGhpcy5kYXRhVmlldyA9IHB2dHN1dGlscy5CdWZmZXJTb3VyY2VDb252ZXJ0ZXIudG9VaW50OEFycmF5KGlucHV0QnVmZmVyKS5zdWJhcnJheShpbnB1dE9mZnNldCwgZW5kTGVuZ3RoKTtcbiAgICAgICAgcmV0dXJuIGVuZExlbmd0aDtcbiAgICB9XG4gICAgdG9CRVIoX3NpemVPbmx5KSB7XG4gICAgICAgIHJldHVybiB0aGlzLmRhdGFWaWV3LnNsaWNlKCkuYnVmZmVyO1xuICAgIH1cbn1cblxuZnVuY3Rpb24gY29tcGFyZVNjaGVtYShyb290LCBpbnB1dERhdGEsIGlucHV0U2NoZW1hKSB7XG4gICAgaWYgKGlucHV0U2NoZW1hIGluc3RhbmNlb2YgQ2hvaWNlKSB7XG4gICAgICAgIGZvciAoY29uc3QgZWxlbWVudCBvZiBpbnB1dFNjaGVtYS52YWx1ZSkge1xuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gY29tcGFyZVNjaGVtYShyb290LCBpbnB1dERhdGEsIGVsZW1lbnQpO1xuICAgICAgICAgICAgaWYgKHJlc3VsdC52ZXJpZmllZCkge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHZlcmlmaWVkOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IHJvb3QsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB7XG4gICAgICAgICAgICBjb25zdCBfcmVzdWx0ID0ge1xuICAgICAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgICByZXN1bHQ6IHsgZXJyb3I6IFwiV3JvbmcgdmFsdWVzIGZvciBDaG9pY2UgdHlwZVwiIH0sXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgaWYgKGlucHV0U2NoZW1hLmhhc093blByb3BlcnR5KE5BTUUpKVxuICAgICAgICAgICAgICAgIF9yZXN1bHQubmFtZSA9IGlucHV0U2NoZW1hLm5hbWU7XG4gICAgICAgICAgICByZXR1cm4gX3Jlc3VsdDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoaW5wdXRTY2hlbWEgaW5zdGFuY2VvZiBBbnkpIHtcbiAgICAgICAgaWYgKGlucHV0U2NoZW1hLmhhc093blByb3BlcnR5KE5BTUUpKVxuICAgICAgICAgICAgcm9vdFtpbnB1dFNjaGVtYS5uYW1lXSA9IGlucHV0RGF0YTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiB0cnVlLFxuICAgICAgICAgICAgcmVzdWx0OiByb290LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoKHJvb3QgaW5zdGFuY2VvZiBPYmplY3QpID09PSBmYWxzZSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgcmVzdWx0OiB7IGVycm9yOiBcIldyb25nIHJvb3Qgb2JqZWN0XCIgfSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgaWYgKChpbnB1dERhdGEgaW5zdGFuY2VvZiBPYmplY3QpID09PSBmYWxzZSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgcmVzdWx0OiB7IGVycm9yOiBcIldyb25nIEFTTi4xIGRhdGFcIiB9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoKGlucHV0U2NoZW1hIGluc3RhbmNlb2YgT2JqZWN0KSA9PT0gZmFsc2UpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgIHJlc3VsdDogeyBlcnJvcjogXCJXcm9uZyBBU04uMSBzY2hlbWFcIiB9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoKElEX0JMT0NLIGluIGlucHV0U2NoZW1hKSA9PT0gZmFsc2UpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgIHJlc3VsdDogeyBlcnJvcjogXCJXcm9uZyBBU04uMSBzY2hlbWFcIiB9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoKEZST01fQkVSIGluIGlucHV0U2NoZW1hLmlkQmxvY2spID09PSBmYWxzZSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgcmVzdWx0OiB7IGVycm9yOiBcIldyb25nIEFTTi4xIHNjaGVtYVwiIH0sXG4gICAgICAgIH07XG4gICAgfVxuICAgIGlmICgoVE9fQkVSIGluIGlucHV0U2NoZW1hLmlkQmxvY2spID09PSBmYWxzZSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgcmVzdWx0OiB7IGVycm9yOiBcIldyb25nIEFTTi4xIHNjaGVtYVwiIH0sXG4gICAgICAgIH07XG4gICAgfVxuICAgIGNvbnN0IGVuY29kZWRJZCA9IGlucHV0U2NoZW1hLmlkQmxvY2sudG9CRVIoZmFsc2UpO1xuICAgIGlmIChlbmNvZGVkSWQuYnl0ZUxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgcmVzdWx0OiB7IGVycm9yOiBcIkVycm9yIGVuY29kaW5nIGlkQmxvY2sgZm9yIEFTTi4xIHNjaGVtYVwiIH0sXG4gICAgICAgIH07XG4gICAgfVxuICAgIGNvbnN0IGRlY29kZWRPZmZzZXQgPSBpbnB1dFNjaGVtYS5pZEJsb2NrLmZyb21CRVIoZW5jb2RlZElkLCAwLCBlbmNvZGVkSWQuYnl0ZUxlbmd0aCk7XG4gICAgaWYgKGRlY29kZWRPZmZzZXQgPT09IC0xKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB2ZXJpZmllZDogZmFsc2UsXG4gICAgICAgICAgICByZXN1bHQ6IHsgZXJyb3I6IFwiRXJyb3IgZGVjb2RpbmcgaWRCbG9jayBmb3IgQVNOLjEgc2NoZW1hXCIgfSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgaWYgKGlucHV0U2NoZW1hLmlkQmxvY2suaGFzT3duUHJvcGVydHkoVEFHX0NMQVNTKSA9PT0gZmFsc2UpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgIHJlc3VsdDogeyBlcnJvcjogXCJXcm9uZyBBU04uMSBzY2hlbWFcIiB9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoaW5wdXRTY2hlbWEuaWRCbG9jay50YWdDbGFzcyAhPT0gaW5wdXREYXRhLmlkQmxvY2sudGFnQ2xhc3MpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgIHJlc3VsdDogcm9vdCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgaWYgKGlucHV0U2NoZW1hLmlkQmxvY2suaGFzT3duUHJvcGVydHkoVEFHX05VTUJFUikgPT09IGZhbHNlKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB2ZXJpZmllZDogZmFsc2UsXG4gICAgICAgICAgICByZXN1bHQ6IHsgZXJyb3I6IFwiV3JvbmcgQVNOLjEgc2NoZW1hXCIgfSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgaWYgKGlucHV0U2NoZW1hLmlkQmxvY2sudGFnTnVtYmVyICE9PSBpbnB1dERhdGEuaWRCbG9jay50YWdOdW1iZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgIHJlc3VsdDogcm9vdCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgaWYgKGlucHV0U2NoZW1hLmlkQmxvY2suaGFzT3duUHJvcGVydHkoSVNfQ09OU1RSVUNURUQpID09PSBmYWxzZSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgcmVzdWx0OiB7IGVycm9yOiBcIldyb25nIEFTTi4xIHNjaGVtYVwiIH0sXG4gICAgICAgIH07XG4gICAgfVxuICAgIGlmIChpbnB1dFNjaGVtYS5pZEJsb2NrLmlzQ29uc3RydWN0ZWQgIT09IGlucHV0RGF0YS5pZEJsb2NrLmlzQ29uc3RydWN0ZWQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgIHJlc3VsdDogcm9vdCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgaWYgKCEoSVNfSEVYX09OTFkgaW4gaW5wdXRTY2hlbWEuaWRCbG9jaykpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgIHJlc3VsdDogeyBlcnJvcjogXCJXcm9uZyBBU04uMSBzY2hlbWFcIiB9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoaW5wdXRTY2hlbWEuaWRCbG9jay5pc0hleE9ubHkgIT09IGlucHV0RGF0YS5pZEJsb2NrLmlzSGV4T25seSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgcmVzdWx0OiByb290LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoaW5wdXRTY2hlbWEuaWRCbG9jay5pc0hleE9ubHkpIHtcbiAgICAgICAgaWYgKChWQUxVRV9IRVhfVklFVyBpbiBpbnB1dFNjaGVtYS5pZEJsb2NrKSA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICAgIHJlc3VsdDogeyBlcnJvcjogXCJXcm9uZyBBU04uMSBzY2hlbWFcIiB9LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBzY2hlbWFWaWV3ID0gaW5wdXRTY2hlbWEuaWRCbG9jay52YWx1ZUhleFZpZXc7XG4gICAgICAgIGNvbnN0IGFzbjFWaWV3ID0gaW5wdXREYXRhLmlkQmxvY2sudmFsdWVIZXhWaWV3O1xuICAgICAgICBpZiAoc2NoZW1hVmlldy5sZW5ndGggIT09IGFzbjFWaWV3Lmxlbmd0aCkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICB2ZXJpZmllZDogZmFsc2UsXG4gICAgICAgICAgICAgICAgcmVzdWx0OiByb290LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNjaGVtYVZpZXcubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGlmIChzY2hlbWFWaWV3W2ldICE9PSBhc24xVmlld1sxXSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiByb290LFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKGlucHV0U2NoZW1hLm5hbWUpIHtcbiAgICAgICAgaW5wdXRTY2hlbWEubmFtZSA9IGlucHV0U2NoZW1hLm5hbWUucmVwbGFjZSgvXlxccyt8XFxzKyQvZywgRU1QVFlfU1RSSU5HKTtcbiAgICAgICAgaWYgKGlucHV0U2NoZW1hLm5hbWUpXG4gICAgICAgICAgICByb290W2lucHV0U2NoZW1hLm5hbWVdID0gaW5wdXREYXRhO1xuICAgIH1cbiAgICBpZiAoaW5wdXRTY2hlbWEgaW5zdGFuY2VvZiB0eXBlU3RvcmUuQ29uc3RydWN0ZWQpIHtcbiAgICAgICAgbGV0IGFkbWlzc2lvbiA9IDA7XG4gICAgICAgIGxldCByZXN1bHQgPSB7XG4gICAgICAgICAgICB2ZXJpZmllZDogZmFsc2UsXG4gICAgICAgICAgICByZXN1bHQ6IHsgZXJyb3I6IFwiVW5rbm93biBlcnJvclwiIH0sXG4gICAgICAgIH07XG4gICAgICAgIGxldCBtYXhMZW5ndGggPSBpbnB1dFNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlLmxlbmd0aDtcbiAgICAgICAgaWYgKG1heExlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGlmIChpbnB1dFNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlWzBdIGluc3RhbmNlb2YgUmVwZWF0ZWQpIHtcbiAgICAgICAgICAgICAgICBtYXhMZW5ndGggPSBpbnB1dERhdGEudmFsdWVCbG9jay52YWx1ZS5sZW5ndGg7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG1heExlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICB2ZXJpZmllZDogdHJ1ZSxcbiAgICAgICAgICAgICAgICByZXN1bHQ6IHJvb3QsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGlmICgoaW5wdXREYXRhLnZhbHVlQmxvY2sudmFsdWUubGVuZ3RoID09PSAwKVxuICAgICAgICAgICAgJiYgKGlucHV0U2NoZW1hLnZhbHVlQmxvY2sudmFsdWUubGVuZ3RoICE9PSAwKSkge1xuICAgICAgICAgICAgbGV0IF9vcHRpb25hbCA9IHRydWU7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGlucHV0U2NoZW1hLnZhbHVlQmxvY2sudmFsdWUubGVuZ3RoOyBpKyspXG4gICAgICAgICAgICAgICAgX29wdGlvbmFsID0gX29wdGlvbmFsICYmIChpbnB1dFNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlW2ldLm9wdGlvbmFsIHx8IGZhbHNlKTtcbiAgICAgICAgICAgIGlmIChfb3B0aW9uYWwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICB2ZXJpZmllZDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiByb290LFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaW5wdXRTY2hlbWEubmFtZSkge1xuICAgICAgICAgICAgICAgIGlucHV0U2NoZW1hLm5hbWUgPSBpbnB1dFNjaGVtYS5uYW1lLnJlcGxhY2UoL15cXHMrfFxccyskL2csIEVNUFRZX1NUUklORyk7XG4gICAgICAgICAgICAgICAgaWYgKGlucHV0U2NoZW1hLm5hbWUpXG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSByb290W2lucHV0U2NoZW1hLm5hbWVdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcm9vdC5lcnJvciA9IFwiSW5jb25zaXN0ZW50IG9iamVjdCBsZW5ndGhcIjtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICAgIHJlc3VsdDogcm9vdCxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBtYXhMZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgaWYgKChpIC0gYWRtaXNzaW9uKSA+PSBpbnB1dERhdGEudmFsdWVCbG9jay52YWx1ZS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICBpZiAoaW5wdXRTY2hlbWEudmFsdWVCbG9jay52YWx1ZVtpXS5vcHRpb25hbCA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgX3Jlc3VsdCA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlcmlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdDogcm9vdCxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgcm9vdC5lcnJvciA9IFwiSW5jb25zaXN0ZW50IGxlbmd0aCBiZXR3ZWVuIEFTTi4xIGRhdGEgYW5kIHNjaGVtYVwiO1xuICAgICAgICAgICAgICAgICAgICBpZiAoaW5wdXRTY2hlbWEubmFtZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXRTY2hlbWEubmFtZSA9IGlucHV0U2NoZW1hLm5hbWUucmVwbGFjZSgvXlxccyt8XFxzKyQvZywgRU1QVFlfU1RSSU5HKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbnB1dFNjaGVtYS5uYW1lKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIHJvb3RbaW5wdXRTY2hlbWEubmFtZV07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgX3Jlc3VsdC5uYW1lID0gaW5wdXRTY2hlbWEubmFtZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3Jlc3VsdDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBpZiAoaW5wdXRTY2hlbWEudmFsdWVCbG9jay52YWx1ZVswXSBpbnN0YW5jZW9mIFJlcGVhdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdCA9IGNvbXBhcmVTY2hlbWEocm9vdCwgaW5wdXREYXRhLnZhbHVlQmxvY2sudmFsdWVbaV0sIGlucHV0U2NoZW1hLnZhbHVlQmxvY2sudmFsdWVbMF0udmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICBpZiAocmVzdWx0LnZlcmlmaWVkID09PSBmYWxzZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlucHV0U2NoZW1hLnZhbHVlQmxvY2sudmFsdWVbMF0ub3B0aW9uYWwpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWRtaXNzaW9uKys7XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoaW5wdXRTY2hlbWEubmFtZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dFNjaGVtYS5uYW1lID0gaW5wdXRTY2hlbWEubmFtZS5yZXBsYWNlKC9eXFxzK3xcXHMrJC9nLCBFTVBUWV9TVFJJTkcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoaW5wdXRTY2hlbWEubmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbGV0ZSByb290W2lucHV0U2NoZW1hLm5hbWVdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmICgoTkFNRSBpbiBpbnB1dFNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlWzBdKSAmJiAoaW5wdXRTY2hlbWEudmFsdWVCbG9jay52YWx1ZVswXS5uYW1lLmxlbmd0aCA+IDApKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgYXJyYXlSb290ID0ge307XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoKExPQ0FMIGluIGlucHV0U2NoZW1hLnZhbHVlQmxvY2sudmFsdWVbMF0pICYmIChpbnB1dFNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlWzBdLmxvY2FsKSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcnJheVJvb3QgPSBpbnB1dERhdGE7XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJyYXlSb290ID0gcm9vdDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgYXJyYXlSb290W2lucHV0U2NoZW1hLnZhbHVlQmxvY2sudmFsdWVbMF0ubmFtZV0gPT09IFwidW5kZWZpbmVkXCIpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJyYXlSb290W2lucHV0U2NoZW1hLnZhbHVlQmxvY2sudmFsdWVbMF0ubmFtZV0gPSBbXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFycmF5Um9vdFtpbnB1dFNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlWzBdLm5hbWVdLnB1c2goaW5wdXREYXRhLnZhbHVlQmxvY2sudmFsdWVbaV0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXN1bHQgPSBjb21wYXJlU2NoZW1hKHJvb3QsIGlucHV0RGF0YS52YWx1ZUJsb2NrLnZhbHVlW2kgLSBhZG1pc3Npb25dLCBpbnB1dFNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlW2ldKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc3VsdC52ZXJpZmllZCA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbnB1dFNjaGVtYS52YWx1ZUJsb2NrLnZhbHVlW2ldLm9wdGlvbmFsKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFkbWlzc2lvbisrO1xuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlucHV0U2NoZW1hLm5hbWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXRTY2hlbWEubmFtZSA9IGlucHV0U2NoZW1hLm5hbWUucmVwbGFjZSgvXlxccyt8XFxzKyQvZywgRU1QVFlfU1RSSU5HKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlucHV0U2NoZW1hLm5hbWUpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWxldGUgcm9vdFtpbnB1dFNjaGVtYS5uYW1lXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAocmVzdWx0LnZlcmlmaWVkID09PSBmYWxzZSkge1xuICAgICAgICAgICAgY29uc3QgX3Jlc3VsdCA9IHtcbiAgICAgICAgICAgICAgICB2ZXJpZmllZDogZmFsc2UsXG4gICAgICAgICAgICAgICAgcmVzdWx0OiByb290LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmIChpbnB1dFNjaGVtYS5uYW1lKSB7XG4gICAgICAgICAgICAgICAgaW5wdXRTY2hlbWEubmFtZSA9IGlucHV0U2NoZW1hLm5hbWUucmVwbGFjZSgvXlxccyt8XFxzKyQvZywgRU1QVFlfU1RSSU5HKTtcbiAgICAgICAgICAgICAgICBpZiAoaW5wdXRTY2hlbWEubmFtZSkge1xuICAgICAgICAgICAgICAgICAgICBkZWxldGUgcm9vdFtpbnB1dFNjaGVtYS5uYW1lXTtcbiAgICAgICAgICAgICAgICAgICAgX3Jlc3VsdC5uYW1lID0gaW5wdXRTY2hlbWEubmFtZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gX3Jlc3VsdDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IHRydWUsXG4gICAgICAgICAgICByZXN1bHQ6IHJvb3QsXG4gICAgICAgIH07XG4gICAgfVxuICAgIGlmIChpbnB1dFNjaGVtYS5wcmltaXRpdmVTY2hlbWFcbiAgICAgICAgJiYgKFZBTFVFX0hFWF9WSUVXIGluIGlucHV0RGF0YS52YWx1ZUJsb2NrKSkge1xuICAgICAgICBjb25zdCBhc24xID0gbG9jYWxGcm9tQkVSKGlucHV0RGF0YS52YWx1ZUJsb2NrLnZhbHVlSGV4Vmlldyk7XG4gICAgICAgIGlmIChhc24xLm9mZnNldCA9PT0gLTEpIHtcbiAgICAgICAgICAgIGNvbnN0IF9yZXN1bHQgPSB7XG4gICAgICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICAgIHJlc3VsdDogYXNuMS5yZXN1bHQsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgaWYgKGlucHV0U2NoZW1hLm5hbWUpIHtcbiAgICAgICAgICAgICAgICBpbnB1dFNjaGVtYS5uYW1lID0gaW5wdXRTY2hlbWEubmFtZS5yZXBsYWNlKC9eXFxzK3xcXHMrJC9nLCBFTVBUWV9TVFJJTkcpO1xuICAgICAgICAgICAgICAgIGlmIChpbnB1dFNjaGVtYS5uYW1lKSB7XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSByb290W2lucHV0U2NoZW1hLm5hbWVdO1xuICAgICAgICAgICAgICAgICAgICBfcmVzdWx0Lm5hbWUgPSBpbnB1dFNjaGVtYS5uYW1lO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBfcmVzdWx0O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBjb21wYXJlU2NoZW1hKHJvb3QsIGFzbjEucmVzdWx0LCBpbnB1dFNjaGVtYS5wcmltaXRpdmVTY2hlbWEpO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICB2ZXJpZmllZDogdHJ1ZSxcbiAgICAgICAgcmVzdWx0OiByb290LFxuICAgIH07XG59XG5mdW5jdGlvbiB2ZXJpZnlTY2hlbWEoaW5wdXRCdWZmZXIsIGlucHV0U2NoZW1hKSB7XG4gICAgaWYgKChpbnB1dFNjaGVtYSBpbnN0YW5jZW9mIE9iamVjdCkgPT09IGZhbHNlKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB2ZXJpZmllZDogZmFsc2UsXG4gICAgICAgICAgICByZXN1bHQ6IHsgZXJyb3I6IFwiV3JvbmcgQVNOLjEgc2NoZW1hIHR5cGVcIiB9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBjb25zdCBhc24xID0gbG9jYWxGcm9tQkVSKHB2dHN1dGlscy5CdWZmZXJTb3VyY2VDb252ZXJ0ZXIudG9VaW50OEFycmF5KGlucHV0QnVmZmVyKSk7XG4gICAgaWYgKGFzbjEub2Zmc2V0ID09PSAtMSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgcmVzdWx0OiBhc24xLnJlc3VsdCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgcmV0dXJuIGNvbXBhcmVTY2hlbWEoYXNuMS5yZXN1bHQsIGFzbjEucmVzdWx0LCBpbnB1dFNjaGVtYSk7XG59XG5cbmV4cG9ydCB7IEFueSwgQmFzZUJsb2NrLCBCYXNlU3RyaW5nQmxvY2ssIEJpdFN0cmluZywgQm1wU3RyaW5nLCBCb29sZWFuLCBDaGFyYWN0ZXJTdHJpbmcsIENob2ljZSwgQ29uc3RydWN0ZWQsIERBVEUsIERhdGVUaW1lLCBEdXJhdGlvbiwgRW5kT2ZDb250ZW50LCBFbnVtZXJhdGVkLCBHZW5lcmFsU3RyaW5nLCBHZW5lcmFsaXplZFRpbWUsIEdyYXBoaWNTdHJpbmcsIEhleEJsb2NrLCBJQTVTdHJpbmcsIEludGVnZXIsIE51bGwsIE51bWVyaWNTdHJpbmcsIE9iamVjdElkZW50aWZpZXIsIE9jdGV0U3RyaW5nLCBQcmltaXRpdmUsIFByaW50YWJsZVN0cmluZywgUmF3RGF0YSwgUmVsYXRpdmVPYmplY3RJZGVudGlmaWVyLCBSZXBlYXRlZCwgU2VxdWVuY2UsIFNldCwgVElNRSwgVGVsZXRleFN0cmluZywgVGltZU9mRGF5LCBVVENUaW1lLCBVbml2ZXJzYWxTdHJpbmcsIFV0ZjhTdHJpbmcsIFZhbHVlQmxvY2ssIFZpZGVvdGV4U3RyaW5nLCBWaWV3V3JpdGVyLCBWaXNpYmxlU3RyaW5nLCBjb21wYXJlU2NoZW1hLCBmcm9tQkVSLCB2ZXJpZnlTY2hlbWEgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/asn1js/build/index.es.js\n");

/***/ })

};
;