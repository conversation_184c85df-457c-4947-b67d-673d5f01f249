<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751302970435" clover="3.2.0">
  <project timestamp="1751302970436" name="All files">
    <metrics statements="225" coveredstatements="73" conditionals="82" coveredconditionals="31" methods="74" coveredmethods="21" elements="381" coveredelements="125" complexity="0" loc="225" ncloc="225" packages="14" files="44" classes="44"/>
    <package name="app">
      <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="layout.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/layout.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/page.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.auth.sign-in">
      <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
      <file name="page.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-in/page.tsx">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="4" count="1" type="stmt"/>
      </file>
    </package>
    <package name="app.auth.sign-out">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="page.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-out/page.tsx">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="app.auth.sign-up">
      <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
      <file name="page.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-up/page.tsx">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="4" count="1" type="stmt"/>
      </file>
    </package>
    <package name="app.dashboard">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="layout.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/dashboard/layout.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/dashboard/page.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.user-profile">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/user-profile/page.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.auth">
      <metrics statements="13" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="ProtectedRoute.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/ProtectedRoute.tsx">
        <metrics statements="11" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
      </file>
      <file name="SignInButton.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/SignInButton.tsx">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="UserButton.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/UserButton.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
      </file>
      <file name="UserProfile.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/UserProfile.tsx">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="components.dashboard">
      <metrics statements="52" coveredstatements="41" conditionals="38" coveredconditionals="24" methods="11" coveredmethods="10"/>
      <file name="Dashboard.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/Dashboard.tsx">
        <metrics statements="16" coveredstatements="15" conditionals="16" coveredconditionals="11" methods="2" coveredmethods="2"/>
        <line num="13" count="3" type="stmt"/>
        <line num="14" count="3" type="stmt"/>
        <line num="15" count="3" type="stmt"/>
        <line num="16" count="3" type="stmt"/>
        <line num="17" count="3" type="stmt"/>
        <line num="19" count="3" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="1" type="stmt"/>
        <line num="33" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="35" count="3" type="stmt"/>
      </file>
      <file name="StreamerCard.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/StreamerCard.tsx">
        <metrics statements="12" coveredstatements="5" conditionals="12" coveredconditionals="7" methods="2" coveredmethods="1"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="17" count="2" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="2" type="stmt"/>
      </file>
      <file name="StreamerList.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/StreamerList.tsx">
        <metrics statements="7" coveredstatements="6" conditionals="2" coveredconditionals="1" methods="4" coveredmethods="4"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="3" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="20" count="2" type="stmt"/>
        <line num="21" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="2" type="stmt"/>
      </file>
      <file name="UserStatus.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/UserStatus.tsx">
        <metrics statements="17" coveredstatements="15" conditionals="8" coveredconditionals="5" methods="3" coveredmethods="3"/>
        <line num="9" count="4" type="stmt"/>
        <line num="10" count="4" type="stmt"/>
        <line num="11" count="4" type="stmt"/>
        <line num="13" count="4" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="29" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="30" count="3" type="stmt"/>
        <line num="33" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
      </file>
    </package>
    <package name="components.layout">
      <metrics statements="25" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="12" coveredmethods="0"/>
      <file name="Footer.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Footer.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
      </file>
      <file name="Grid.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Grid.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
      </file>
      <file name="Header.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Header.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
      <file name="Layout.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Layout.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
      </file>
      <file name="Sidebar.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Sidebar.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.ui">
      <metrics statements="63" coveredstatements="27" conditionals="12" coveredconditionals="5" methods="22" coveredmethods="8"/>
      <file name="Button.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Button.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="6" coveredconditionals="5" methods="1" coveredmethods="1"/>
        <line num="10" count="2" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="59" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="60" count="11" type="stmt"/>
        <line num="72" count="2" type="stmt"/>
      </file>
      <file name="Card.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Card.tsx">
        <metrics statements="18" coveredstatements="18" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="6"/>
        <line num="5" count="1" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="24" count="2" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="36" count="2" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="51" count="2" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="63" count="2" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="71" count="2" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
      </file>
      <file name="EmptyState.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/EmptyState.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
      </file>
      <file name="ErrorState.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/ErrorState.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
      </file>
      <file name="Input.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Input.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
      </file>
      <file name="Label.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Label.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="LoadingSpinner.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/LoadingSpinner.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="6" count="2" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="28" count="4" type="stmt"/>
        <line num="42" count="2" type="stmt"/>
      </file>
      <file name="Skeleton.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Skeleton.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
      </file>
      <file name="Table.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Table.tsx">
        <metrics statements="24" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
      </file>
      <file name="Toast.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Toast.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
    </package>
    <package name="context">
      <metrics statements="11" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="LayoutContext.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/context/LayoutContext.tsx">
        <metrics statements="11" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
    </package>
    <package name="hooks">
      <metrics statements="30" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="useApi.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useApi.tsx">
        <metrics statements="24" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
      </file>
      <file name="useAuth.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useAuth.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
      </file>
      <file name="useLocalStorage.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useLocalStorage.tsx">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="useStreamers.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useStreamers.tsx">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="useUserStatus.tsx" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useUserStatus.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="21" coveredstatements="3" conditionals="9" coveredconditionals="2" methods="6" coveredmethods="1"/>
      <file name="api.ts" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/api.ts">
        <metrics statements="20" coveredstatements="2" conditionals="9" coveredconditionals="2" methods="5" coveredmethods="0"/>
        <line num="6" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
      </file>
      <file name="auth.ts" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/auth.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="utils.ts" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/utils.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="5" count="27" type="stmt"/>
      </file>
    </package>
    <package name="types">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="api.ts" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/api.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="assignment.ts" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/assignment.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="streamer.ts" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/streamer.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="user.ts" path="/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/user.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
  </project>
</coverage>
