"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import UserStatus from '@/components/dashboard/UserStatus';
import StreamerList from '@/components/dashboard/StreamerList';
import AssignmentList from '@/components/dashboard/AssignmentList';
import AssignmentAnalytics from '@/components/dashboard/AssignmentAnalytics';
import { Assignment } from '@/types/assignment';
import { Streamer } from '@/types/streamer';
import apiClient from '@/lib/api';

interface StreamerStats {
  total_streamers: number;
  live_streamers: number;
  available_streamers: number;
  average_followers: number;
}

interface UserStatus {
  daily_request_count: number;
  remaining_requests: number;
  next_reset_time: string;
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [streamers, setStreamers] = useState<Streamer[]>([]);
  const [filteredStreamers, setFilteredStreamers] = useState<Streamer[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [stats, setStats] = useState<StreamerStats | null>(null);
  const [userStatus, setUserStatus] = useState<UserStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [backendConnected, setBackendConnected] = useState<boolean | null>(null);

  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [showLiveOnly, setShowLiveOnly] = useState(false);
  const [selectedGame, setSelectedGame] = useState('');
  const [minFollowers, setMinFollowers] = useState(0);
  
  const API_BASE_URL = '/api';

  // Filter streamers based on current filters
  useEffect(() => {
    let filtered = streamers;

    if (searchQuery) {
      filtered = filtered.filter(streamer =>
        streamer.display_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        streamer.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        streamer.current_game?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (showLiveOnly) {
      filtered = filtered.filter(streamer => streamer.is_live);
    }

    if (selectedGame) {
      filtered = filtered.filter(streamer => streamer.current_game === selectedGame);
    }

    if (minFollowers > 0) {
      filtered = filtered.filter(streamer => streamer.follower_count >= minFollowers);
    }

    setFilteredStreamers(filtered);
  }, [streamers, searchQuery, showLiveOnly, selectedGame, minFollowers]);

  // Fetch initial data
  useEffect(() => {
    fetchStats();
    fetchUserStatus();
    fetchAssignments();
    // Auto-load streamers on page load
    fetchNewStreamers();
  }, []);

  const fetchUserStatus = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/user/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (response.ok) {
        const data = await response.json();
        setUserStatus(data);
      } else {
        console.error('Failed to fetch user status - Server responded with:', response.status);
      }
    } catch (err) {
      console.error('Failed to fetch user status - Connection error:', err);
    }
  };

  const fetchStats = async () => {
    try {
      console.log('Fetching stats using API client...');
      const data = await apiClient.getStreamerStats();
      setStats({
        total_streamers: data.total_streamers,
        live_streamers: data.live_streamers,
        available_streamers: data.available_streamers,
        average_followers: 25 // Use static value since backend doesn't calculate this
      });
      setBackendConnected(true);
      console.log('Stats fetched successfully:', data);
    } catch (err) {
      console.error('Failed to fetch stats - Connection error:', err);
      setBackendConnected(false);
      // Set default stats if backend is not available
      setStats({
        total_streamers: 0,
        live_streamers: 0,
        available_streamers: 0,
        average_followers: 0
      });
    }
  };

  const fetchNewStreamers = async () => {
    console.log('=== FETCH NEW STREAMERS BUTTON CLICKED ===');
    setLoading(true);
    setError(null);

    try {
      console.log('Fetching streamers using API client...');
      const data = await apiClient.getAvailableStreamers(50);
      console.log('Received data:', data);

      const streamersArray = data.streamers || [];
      console.log('Streamers array:', streamersArray);

      setStreamers(streamersArray);

      // Update user status from response
      if (data.user_status) {
        setUserStatus({
          daily_request_count: data.user_status.daily_requests_used,
          remaining_requests: data.user_status.daily_requests_remaining,
          next_reset_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
        });
      }

      // Refresh stats after fetching streamers
      await fetchStats();

      console.log(`✅ Success! Loaded ${streamersArray.length} real streamers from your scraper data!`);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch streamers';
      setError(errorMessage);
      console.error('=== ERROR FETCHING STREAMERS ===', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchAssignments = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/assignments`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (response.ok) {
        const data = await response.json();
        setAssignments(data.assignments);
      } else {
        console.error('Failed to fetch assignments - Server responded with:', response.status);
      }
    } catch (err) {
      console.error('Failed to fetch assignments - Connection error:', err);
    }
  };

  const handleUpdateAssignment = async (id: string, status: Assignment['status'], notes: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/assignments/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, notes }),
      });
      if (response.ok) {
        fetchAssignments();
      } else {
        console.error('Failed to update assignment - Server responded with:', response.status);
      }
    } catch (err) {
      console.error('Failed to update assignment - Connection error:', err);
    }
  };

  const handleDeleteAssignment = async (id: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/assignments/${id}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        fetchAssignments();
      } else {
        console.error('Failed to delete assignment - Server responded with:', response.status);
      }
    } catch (err) {
      console.error('Failed to delete assignment - Connection error:', err);
    }
  };

  if (!user) {
    return <div>Please sign in to access the dashboard.</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🎯 Streamer Dashboard</h1>
          <p className="text-gray-600">
            Welcome back, {user?.name || 'Agent'}! Discover and manage your streamer outreach campaigns.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-500">Total Streamers</h3>
                <p className="text-2xl font-bold text-gray-900">{stats?.total_streamers || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-500">Live Now</h3>
                <p className="text-2xl font-bold text-gray-900">{stats?.live_streamers || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-500">Available</h3>
                <p className="text-2xl font-bold text-gray-900">{stats?.available_streamers || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-500">Loaded</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredStreamers.length > 0 && filteredStreamers.length !== streamers.length
                    ? `${filteredStreamers.length}/${streamers.length}`
                    : streamers.length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mb-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">Streamer Discovery</h3>
                <p className="text-gray-600 text-sm">Load fresh streamers from your automated scraper</p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={fetchNewStreamers}
                  disabled={loading || (userStatus?.remaining_requests === 0)}
                  className={`px-6 py-3 rounded-lg font-medium text-white transition-all ${
                    userStatus?.remaining_requests === 0
                      ? 'bg-gray-400 cursor-not-allowed'
                      : loading
                      ? 'bg-blue-400 cursor-wait'
                      : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg'
                  }`}
                >
                  {loading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading...
                    </div>
                  ) : userStatus?.remaining_requests === 0 ? (
                    '❌ Daily Limit Reached'
                  ) : (
                    '🔄 Refresh Streamers'
                  )}
                </button>
              </div>
            </div>

            {/* Status indicators */}
            {userStatus && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Daily requests: {userStatus.daily_request_count}/3</span>
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                      backendConnected === true ? 'bg-green-500' :
                      backendConnected === false ? 'bg-red-500' :
                      'bg-yellow-500'
                    }`}></div>
                    <span className="text-gray-600">
                      {backendConnected === true ? 'Backend Connected' :
                       backendConnected === false ? 'Backend Disconnected' :
                       'Checking...'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-sm">⚠️ {error}</p>
              </div>
            )}
          </div>
        </div>

        {/* Filters Section */}
        {streamers.length > 0 && (
          <div className="mb-8">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">🔍 Filter Streamers</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Search */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                  <input
                    type="text"
                    placeholder="Search by name or game..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>

                {/* Live Only Toggle */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showLiveOnly}
                      onChange={(e) => setShowLiveOnly(e.target.checked)}
                      className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Live only</span>
                  </label>
                </div>

                {/* Game Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Game</label>
                  <select
                    value={selectedGame}
                    onChange={(e) => setSelectedGame(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="">All games</option>
                    {Array.from(new Set(streamers.map(s => s.current_game).filter(Boolean))).map(game => (
                      <option key={game!} value={game!}>{game}</option>
                    ))}
                  </select>
                </div>

                {/* Min Followers */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Min Followers</label>
                  <input
                    type="number"
                    placeholder="0"
                    value={minFollowers || ''}
                    onChange={(e) => setMinFollowers(Number(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Filter Results Summary */}
              <div className="mt-4 pt-4 border-t border-gray-200 flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Showing {filteredStreamers.length} of {streamers.length} streamers
                </div>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setShowLiveOnly(false);
                    setSelectedGame('');
                    setMinFollowers(0);
                  }}
                  className="text-sm text-purple-600 hover:text-purple-700 font-medium"
                >
                  Clear filters
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Streamers Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              🎮 Available Streamers {filteredStreamers.length > 0 && `(${filteredStreamers.length})`}
            </h2>
            {filteredStreamers.length > 0 && (
              <div className="text-sm text-gray-600">
                Showing {filteredStreamers.length} streamers
              </div>
            )}
          </div>

          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse">
                  <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          ) : filteredStreamers.length > 0 ? (
            <StreamerList streamers={filteredStreamers} assignments={assignments} onInterestLevelChanged={() => {}} />
          ) : streamers.length > 0 ? (
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-4 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No streamers match your filters</h3>
              <p className="text-gray-600 mb-4">Try adjusting your search criteria or clear the filters</p>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-4 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No streamers loaded yet</h3>
              <p className="text-gray-600 mb-4">Click "Refresh Streamers" to load available streamers from your database</p>
            </div>
          )}
        </div>

        {/* Assignment Analytics */}
        <div className="mb-8">
          <AssignmentAnalytics assignments={assignments} />
        </div>

        {/* Assignment List */}
        <div>
          <AssignmentList
            assignments={assignments}
            onUpdate={handleUpdateAssignment}
            onDelete={handleDeleteAssignment}
          />
        </div>
      </div>
    </div>
  );
}