"""Add performance optimization indexes

Revision ID: add_performance_indexes_20250630
Revises: 5e93577c74bf
Create Date: 2025-06-30 20:15:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_performance_indexes_20250630'
down_revision: Union[str, None] = '5e93577c74bf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add composite indexes for optimized queries."""
    
    # Streamers table composite indexes for frequent query patterns
    op.create_index('idx_streamers_available', 'streamers', ['is_live', 'follower_count', 'language'])
    op.create_index('idx_streamers_live_followers', 'streamers', ['is_live', 'follower_count'])
    op.create_index('idx_streamers_search', 'streamers', ['username', 'display_name'])
    op.create_index('idx_streamers_pagination', 'streamers', ['created_at', 'id'])
    op.create_index('idx_streamers_follower_sort', 'streamers', ['follower_count', 'id'])
    
    # Assignment table indexes for recent assignment lookups
    op.create_index('idx_assignments_recent', 'assignments', ['assigned_at', 'streamer_id'])
    op.create_index('idx_assignments_status_time', 'assignments', ['status', 'assigned_at'])


def downgrade() -> None:
    """Remove performance optimization indexes."""
    
    # Remove assignment indexes
    op.drop_index('idx_assignments_status_time', table_name='assignments')
    op.drop_index('idx_assignments_recent', table_name='assignments')
    
    # Remove streamer indexes  
    op.drop_index('idx_streamers_follower_sort', table_name='streamers')
    op.drop_index('idx_streamers_pagination', table_name='streamers')
    op.drop_index('idx_streamers_search', table_name='streamers')
    op.drop_index('idx_streamers_live_followers', table_name='streamers')
    op.drop_index('idx_streamers_available', table_name='streamers')