"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/StreamerList */ \"(app-pages-browser)/./src/components/dashboard/StreamerList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/AssignmentList */ \"(app-pages-browser)/./src/components/dashboard/AssignmentList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AssignmentAnalytics */ \"(app-pages-browser)/./src/components/dashboard/AssignmentAnalytics.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [streamers, setStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredStreamers, setFilteredStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStatus, setUserStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [backendConnected, setBackendConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filter states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showLiveOnly, setShowLiveOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedGame, setSelectedGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [minFollowers, setMinFollowers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Live verification states\n    const [useVerifiedLive, setUseVerifiedLive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Always use verified live only\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const API_BASE_URL = \"/api\";\n    // Filter streamers based on current filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = streamers;\n        if (searchQuery) {\n            filtered = filtered.filter((streamer)=>{\n                var _streamer_current_game;\n                return streamer.display_name.toLowerCase().includes(searchQuery.toLowerCase()) || streamer.username.toLowerCase().includes(searchQuery.toLowerCase()) || ((_streamer_current_game = streamer.current_game) === null || _streamer_current_game === void 0 ? void 0 : _streamer_current_game.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        if (showLiveOnly) {\n            filtered = filtered.filter((streamer)=>streamer.is_live);\n        }\n        if (selectedGame) {\n            filtered = filtered.filter((streamer)=>streamer.current_game === selectedGame);\n        }\n        if (minFollowers > 0) {\n            filtered = filtered.filter((streamer)=>streamer.follower_count >= minFollowers);\n        }\n        setFilteredStreamers(filtered);\n    }, [\n        streamers,\n        searchQuery,\n        showLiveOnly,\n        selectedGame,\n        minFollowers\n    ]);\n    // Fetch initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStats();\n        fetchUserStatus();\n        fetchAssignments();\n        // Auto-load streamers on page load\n        fetchNewStreamers();\n    }, []);\n    const fetchUserStatus = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/user/status\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUserStatus(data);\n            } else {\n                console.error(\"Failed to fetch user status - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch user status - Connection error:\", err);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            console.log(\"Fetching stats using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getStreamerStats();\n            setStats({\n                total_streamers: data.total_streamers,\n                live_streamers: data.live_streamers,\n                available_streamers: data.available_streamers,\n                average_followers: 25 // Use static value since backend doesn't calculate this\n            });\n            setBackendConnected(true);\n            console.log(\"Stats fetched successfully:\", data);\n        } catch (err) {\n            console.error(\"Failed to fetch stats - Connection error:\", err);\n            setBackendConnected(false);\n            // Set default stats if backend is not available\n            setStats({\n                total_streamers: 0,\n                live_streamers: 0,\n                available_streamers: 0,\n                average_followers: 0\n            });\n        }\n    };\n    const fetchNewStreamers = async ()=>{\n        console.log(\"=== FETCH NEW STREAMERS BUTTON CLICKED ===\");\n        setLoading(true);\n        setError(null);\n        setVerificationStatus(\"\");\n        try {\n            if (useVerifiedLive) {\n                console.log(\"Fetching VERIFIED LIVE streamers using real-time API...\");\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getLiveVerifiedStreamers(10);\n                console.log(\"Received verified live data:\", data);\n                const streamersArray = data.streamers || [];\n                setStreamers(streamersArray);\n                setVerificationStatus(\"\".concat(data.status, \" (checked \").concat(data.checked_count, \" streamers)\"));\n                console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" VERIFIED LIVE streamers!\"));\n            } else {\n                console.log(\"Fetching streamers using database API...\");\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getAvailableStreamers(50);\n                console.log(\"Received database data:\", data);\n                const streamersArray = data.streamers || [];\n                setStreamers(streamersArray);\n                setVerificationStatus(\"Using database data (may include offline streamers)\");\n                // Update user status from response\n                if (data.user_status) {\n                    setUserStatus({\n                        daily_request_count: data.user_status.daily_requests_used,\n                        remaining_requests: data.user_status.daily_requests_remaining,\n                        next_reset_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now\n                    });\n                }\n                console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" streamers from database!\"));\n            }\n            // Refresh stats after fetching streamers\n            await fetchStats();\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to fetch streamers\";\n            setError(errorMessage);\n            console.error(\"=== ERROR FETCHING STREAMERS ===\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAssignments = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAssignments(data.assignments);\n            } else {\n                console.error(\"Failed to fetch assignments - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch assignments - Connection error:\", err);\n        }\n    };\n    const handleUpdateAssignment = async (id, status, notes)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status,\n                    notes\n                })\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to update assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to update assignment - Connection error:\", err);\n        }\n    };\n    const handleDeleteAssignment = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to delete assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to delete assignment - Connection error:\", err);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Please sign in to access the dashboard.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 233,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"\\uD83C\\uDFAF Streamer Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.name) || \"Agent\",\n                                \"! Discover and manage your streamer outreach campaigns.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Total Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.total_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-green-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-green-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Live Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.live_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-purple-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.available_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-yellow-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-yellow-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Loaded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: filteredStreamers.length > 0 && filteredStreamers.length !== streamers.length ? \"\".concat(filteredStreamers.length, \"/\").concat(streamers.length) : streamers.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                children: \"Streamer Discovery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: useVerifiedLive ? \"Get real-time verified live streamers (recommended for outreach)\" : \"Load streamers from database (may include offline streamers)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            verificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-purple-600 mt-1\",\n                                                children: [\n                                                    \"\\uD83D\\uDCCA \",\n                                                    verificationStatus\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center bg-gray-50 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: useVerifiedLive,\n                                                            onChange: (e)=>setUseVerifiedLive(e.target.checked),\n                                                            className: \"rounded border-gray-300 text-purple-600 focus:ring-purple-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: useVerifiedLive ? \"✅ Live Verified\" : \"\\uD83D\\uDCCA Database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: fetchNewStreamers,\n                                                disabled: loading || (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0,\n                                                className: \"px-6 py-3 rounded-lg font-medium text-white transition-all \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"bg-gray-400 cursor-not-allowed\" : loading ? \"bg-blue-400 cursor-wait\" : useVerifiedLive ? \"bg-green-600 hover:bg-green-700 hover:shadow-lg\" : \"bg-blue-600 hover:bg-blue-700 hover:shadow-lg\"),\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        useVerifiedLive ? \"Verifying...\" : \"Loading...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this) : (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"❌ Daily Limit Reached\" : useVerifiedLive ? \"\\uD83D\\uDD34 Get Live Streamers\" : \"\\uD83D\\uDD04 Refresh Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this),\n                            userStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"Daily requests: \",\n                                                userStatus.daily_request_count,\n                                                \"/3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full mr-2 \".concat(backendConnected === true ? \"bg-green-500\" : backendConnected === false ? \"bg-red-500\" : \"bg-yellow-500\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: backendConnected === true ? \"Backend Connected\" : backendConnected === false ? \"Backend Disconnected\" : \"Checking...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: [\n                                        \"⚠️ \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                streamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"\\uD83D\\uDD0D Filter Streamers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search by name or game...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: showLiveOnly,\n                                                        onChange: (e)=>setShowLiveOnly(e.target.checked),\n                                                        className: \"rounded border-gray-300 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                        children: \"Live only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Game\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedGame,\n                                                onChange: (e)=>setSelectedGame(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All games\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    Array.from(new Set(streamers.map((s)=>s.current_game).filter(Boolean))).map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: game,\n                                                            children: game\n                                                        }, game, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Min Followers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"0\",\n                                                value: minFollowers || \"\",\n                                                onChange: (e)=>setMinFollowers(Number(e.target.value) || 0),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Showing \",\n                                            filteredStreamers.length,\n                                            \" of \",\n                                            streamers.length,\n                                            \" streamers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setSearchQuery(\"\");\n                                            setShowLiveOnly(false);\n                                            setSelectedGame(\"\");\n                                            setMinFollowers(0);\n                                        },\n                                        className: \"text-sm text-purple-600 hover:text-purple-700 font-medium\",\n                                        children: \"Clear filters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        \"\\uD83C\\uDFAE Available Streamers \",\n                                        filteredStreamers.length > 0 && \"(\".concat(filteredStreamers.length, \")\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this),\n                                filteredStreamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredStreamers.length,\n                                        \" streamers\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(8)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-48 bg-gray-200 rounded-lg mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-2/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this) : filteredStreamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            streamers: filteredStreamers,\n                            assignments: assignments,\n                            onInterestLevelChanged: ()=>{}\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 13\n                        }, this) : streamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers match your filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Try adjusting your search criteria or clear the filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers loaded yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'Click \"Refresh Streamers\" to load available streamers from your database'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        assignments: assignments\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 531,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        assignments: assignments,\n                        onUpdate: handleUpdateAssignment,\n                        onDelete: handleDeleteAssignment\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 536,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"N9LcwhoMswGSW2bkjNzTjbEQrbQ=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});