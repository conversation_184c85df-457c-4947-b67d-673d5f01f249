"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/StreamerList */ \"(app-pages-browser)/./src/components/dashboard/StreamerList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/AssignmentList */ \"(app-pages-browser)/./src/components/dashboard/AssignmentList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AssignmentAnalytics */ \"(app-pages-browser)/./src/components/dashboard/AssignmentAnalytics.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [streamers, setStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredStreamers, setFilteredStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStatus, setUserStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [backendConnected, setBackendConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filter states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showLiveOnly, setShowLiveOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedGame, setSelectedGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [minFollowers, setMinFollowers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Live verification states - always use verified live only\n    const useVerifiedLive = true;\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [batchSize, setBatchSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100); // Default batch size\n    const [quotaInfo, setQuotaInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // Streamer quota information\n    const API_BASE_URL = \"/api\";\n    // Filter streamers based on current filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = streamers;\n        if (searchQuery) {\n            filtered = filtered.filter((streamer)=>{\n                var _streamer_current_game;\n                return streamer.display_name.toLowerCase().includes(searchQuery.toLowerCase()) || streamer.username.toLowerCase().includes(searchQuery.toLowerCase()) || ((_streamer_current_game = streamer.current_game) === null || _streamer_current_game === void 0 ? void 0 : _streamer_current_game.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        if (showLiveOnly) {\n            filtered = filtered.filter((streamer)=>streamer.is_live);\n        }\n        if (selectedGame) {\n            filtered = filtered.filter((streamer)=>streamer.current_game === selectedGame);\n        }\n        if (minFollowers > 0) {\n            filtered = filtered.filter((streamer)=>streamer.follower_count >= minFollowers);\n        }\n        setFilteredStreamers(filtered);\n    }, [\n        streamers,\n        searchQuery,\n        showLiveOnly,\n        selectedGame,\n        minFollowers\n    ]);\n    // Fetch initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStats();\n        fetchUserStatus();\n        fetchAssignments();\n        // Auto-load streamers on page load\n        fetchNewStreamers();\n    }, []);\n    const fetchUserStatus = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/user/status\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUserStatus(data);\n            } else {\n                console.error(\"Failed to fetch user status - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch user status - Connection error:\", err);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            console.log(\"Fetching stats using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getStreamerStats();\n            setStats({\n                total_streamers: data.total_streamers,\n                live_streamers: data.live_streamers,\n                available_streamers: data.available_streamers,\n                average_followers: 25 // Use static value since backend doesn't calculate this\n            });\n            setBackendConnected(true);\n            console.log(\"Stats fetched successfully:\", data);\n        } catch (err) {\n            console.error(\"Failed to fetch stats - Connection error:\", err);\n            setBackendConnected(false);\n            // Set default stats if backend is not available\n            setStats({\n                total_streamers: 0,\n                live_streamers: 0,\n                available_streamers: 0,\n                average_followers: 0\n            });\n        }\n    };\n    const fetchNewStreamers = async ()=>{\n        console.log(\"=== FETCH NEW STREAMERS BUTTON CLICKED ===\");\n        setLoading(true);\n        setError(null);\n        setVerificationStatus(\"\");\n        try {\n            if (useVerifiedLive) {\n                console.log(\"Fetching VERIFIED LIVE streamers using real-time API...\");\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getLiveVerifiedStreamers(batchSize);\n                console.log(\"Received verified live data:\", data);\n                const streamersArray = data.streamers || [];\n                setStreamers(streamersArray);\n                // Update quota information if available\n                if (data.quota_info) {\n                    setQuotaInfo(data.quota_info);\n                    console.log(\"Quota info:\", data.quota_info);\n                }\n                const quotaText = data.quota_info ? \" | Quota: \".concat(data.quota_info.total_received_today, \"/\").concat(data.quota_info.daily_limit, \" (\").concat(data.quota_info.remaining_quota, \" remaining)\") : \"\";\n                setVerificationStatus(\"\".concat(data.status, \" (checked \").concat(data.checked_count, \" streamers)\").concat(quotaText));\n                console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" VERIFIED LIVE streamers!\"));\n            } else {\n                console.log(\"Fetching streamers using database API...\");\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getAvailableStreamers(50);\n                console.log(\"Received database data:\", data);\n                const streamersArray = data.streamers || [];\n                setStreamers(streamersArray);\n                setVerificationStatus(\"Using database data (may include offline streamers)\");\n                // Update user status from response\n                if (data.user_status) {\n                    setUserStatus({\n                        daily_request_count: data.user_status.daily_requests_used,\n                        remaining_requests: data.user_status.daily_requests_remaining,\n                        next_reset_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now\n                    });\n                }\n                console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" streamers from database!\"));\n            }\n            // Refresh stats after fetching streamers\n            await fetchStats();\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to fetch streamers\";\n            setError(errorMessage);\n            console.error(\"=== ERROR FETCHING STREAMERS ===\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAssignments = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAssignments(data.assignments);\n            } else {\n                console.error(\"Failed to fetch assignments - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch assignments - Connection error:\", err);\n        }\n    };\n    const handleUpdateAssignment = async (id, status, notes)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status,\n                    notes\n                })\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to update assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to update assignment - Connection error:\", err);\n        }\n    };\n    const handleDeleteAssignment = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to delete assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to delete assignment - Connection error:\", err);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Please sign in to access the dashboard.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 246,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"\\uD83C\\uDFAF Streamer Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.name) || \"Agent\",\n                                \"! Discover and manage your streamer outreach campaigns.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Total Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.total_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-green-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-green-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Live Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.live_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-purple-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.available_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-yellow-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-yellow-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Loaded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: filteredStreamers.length > 0 && filteredStreamers.length !== streamers.length ? \"\".concat(filteredStreamers.length, \"/\").concat(streamers.length) : streamers.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                children: \"Streamer Discovery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: useVerifiedLive ? \"Get real-time verified live streamers (recommended for outreach)\" : \"Load streamers from database (may include offline streamers)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            verificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-purple-600 mt-1\",\n                                                children: [\n                                                    \"\\uD83D\\uDCCA \",\n                                                    verificationStatus\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center bg-green-50 rounded-lg p-2 border border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-green-700\",\n                                                    children: \"✅ Live Verified Mode Only\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this),\n                                            quotaInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center bg-orange-50 rounded-lg p-2 border border-orange-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-orange-700\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCCA Daily Quota: \",\n                                                        quotaInfo.total_received_today,\n                                                        \"/\",\n                                                        quotaInfo.daily_limit,\n                                                        \"(\",\n                                                        quotaInfo.remaining_quota,\n                                                        \" remaining)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center bg-blue-50 rounded-lg p-2 border border-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-blue-700 mr-2\",\n                                                        children: \"Batch Size:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: batchSize,\n                                                        onChange: (e)=>setBatchSize(Number(e.target.value)),\n                                                        className: \"text-sm bg-white border border-blue-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                        disabled: quotaInfo && quotaInfo.remaining_quota <= 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 10,\n                                                                children: \"10 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 25,\n                                                                children: \"25 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 50,\n                                                                children: \"50 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 100,\n                                                                children: \"100 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 200,\n                                                                children: \"200 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 300,\n                                                                children: \"300 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 500,\n                                                                children: \"500 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: fetchNewStreamers,\n                                                disabled: loading || (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 || quotaInfo && quotaInfo.remaining_quota <= 0,\n                                                className: \"px-6 py-3 rounded-lg font-medium text-white transition-all \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 || quotaInfo && quotaInfo.remaining_quota <= 0 ? \"bg-gray-400 cursor-not-allowed\" : loading ? \"bg-blue-400 cursor-wait\" : useVerifiedLive ? \"bg-green-600 hover:bg-green-700 hover:shadow-lg\" : \"bg-blue-600 hover:bg-blue-700 hover:shadow-lg\"),\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        useVerifiedLive ? \"Verifying...\" : \"Loading...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this) : (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"❌ Daily Request Limit Reached\" : quotaInfo && quotaInfo.remaining_quota <= 0 ? \"\\uD83D\\uDCCA Daily Streamer Quota Reached\" : useVerifiedLive ? \"\\uD83D\\uDD34 Get \".concat(Math.min(batchSize, (quotaInfo === null || quotaInfo === void 0 ? void 0 : quotaInfo.remaining_quota) || batchSize), \" Live Streamers\") : \"\\uD83D\\uDD04 Refresh Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            userStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"Daily requests: \",\n                                                userStatus.daily_request_count,\n                                                \"/3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full mr-2 \".concat(backendConnected === true ? \"bg-green-500\" : backendConnected === false ? \"bg-red-500\" : \"bg-yellow-500\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: backendConnected === true ? \"Backend Connected\" : backendConnected === false ? \"Backend Disconnected\" : \"Checking...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: [\n                                        \"⚠️ \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this),\n                streamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"\\uD83D\\uDD0D Filter Streamers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search by name or game...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: showLiveOnly,\n                                                        onChange: (e)=>setShowLiveOnly(e.target.checked),\n                                                        className: \"rounded border-gray-300 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                        children: \"Live only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Game\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedGame,\n                                                onChange: (e)=>setSelectedGame(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All games\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    Array.from(new Set(streamers.map((s)=>s.current_game).filter(Boolean))).map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: game,\n                                                            children: game\n                                                        }, game, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Min Followers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"0\",\n                                                value: minFollowers || \"\",\n                                                onChange: (e)=>setMinFollowers(Number(e.target.value) || 0),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Showing \",\n                                            filteredStreamers.length,\n                                            \" of \",\n                                            streamers.length,\n                                            \" streamers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setSearchQuery(\"\");\n                                            setShowLiveOnly(false);\n                                            setSelectedGame(\"\");\n                                            setMinFollowers(0);\n                                        },\n                                        className: \"text-sm text-purple-600 hover:text-purple-700 font-medium\",\n                                        children: \"Clear filters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        \"\\uD83C\\uDFAE Available Streamers \",\n                                        filteredStreamers.length > 0 && \"(\".concat(filteredStreamers.length, \")\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                filteredStreamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredStreamers.length,\n                                        \" streamers\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(8)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-48 bg-gray-200 rounded-lg mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-2/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this) : filteredStreamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            streamers: filteredStreamers,\n                            assignments: assignments,\n                            onInterestLevelChanged: ()=>{}\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 13\n                        }, this) : streamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers match your filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Try adjusting your search criteria or clear the filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers loaded yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'Click \"Refresh Streamers\" to load available streamers from your database'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        assignments: assignments\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        assignments: assignments,\n                        onUpdate: handleUpdateAssignment,\n                        onDelete: handleDeleteAssignment\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"IIso5DxU6vObfUyo2kjVsCQbsmM=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});