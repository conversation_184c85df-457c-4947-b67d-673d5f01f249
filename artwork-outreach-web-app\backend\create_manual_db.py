#!/usr/bin/env python3
"""
Manual database creation script
"""

import os
import sys
import asyncio
import requests
import json

# Try to import psycopg2 for direct database connection
try:
    import psycopg2
    from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
    PSYCOPG2_AVAILABLE = True
except ImportError:
    print("⚠️  psycopg2 not available, will use API approach only")
    PSYCOPG2_AVAILABLE = False

# Supabase REST API configuration - Alternative to direct database connection
SUPABASE_URL = "https://wejiqonfxofwbiubqmpo.supabase.co"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indlamlxb25meG9md2JpdWJxbXBvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTM3MjkyMSwiZXhwIjoyMDY2OTQ4OTIxfQ.YNXeWA1bj9oHLgrfPqp3le8ajlikYatPOxGcB-iseks"

# Database configuration for fallback
DB_HOST = "db.wejiqonfxofwbiubqmpo.supabase.co"
DB_PORT = "5432"
DB_NAME = "postgres"
DB_USER = "postgres"
DB_PASSWORD = "CYfWe7F54AEWBMz5"

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def create_tables_via_api():
    """Create database tables using Supabase REST API"""
    print("🗄️  Creating database tables via Supabase REST API...")
    
    headers = {
        "apikey": SUPABASE_SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    # SQL commands to execute
    sql_commands = [
        'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
        '''
        CREATE TABLE IF NOT EXISTS streamers (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            twitch_user_id VARCHAR NOT NULL UNIQUE,
            username VARCHAR NOT NULL,
            display_name VARCHAR,
            follower_count INTEGER DEFAULT 0 CHECK (follower_count >= 0),
            is_live BOOLEAN DEFAULT FALSE,
            current_game VARCHAR,
            stream_title VARCHAR,
            thumbnail_url VARCHAR,
            profile_image_url VARCHAR,
            language VARCHAR DEFAULT 'en',
            last_seen_live_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        ''',
        '''
        CREATE TABLE IF NOT EXISTS user_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            email VARCHAR NOT NULL UNIQUE,
            full_name VARCHAR,
            daily_request_count INTEGER DEFAULT 0 CHECK (daily_request_count >= 0),
            last_request_date TIMESTAMPTZ,
            role VARCHAR DEFAULT 'agent',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        ''',
        '''
        CREATE TABLE IF NOT EXISTS assignments (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            agent_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
            streamer_id VARCHAR NOT NULL REFERENCES streamers(twitch_user_id) ON DELETE CASCADE,
            status VARCHAR DEFAULT 'assigned',
            assigned_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            deleted_at TIMESTAMPTZ,
            notes TEXT,
            UNIQUE(agent_id, streamer_id)
        );
        ''',
        '''
        CREATE TABLE IF NOT EXISTS scraper_runs (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            run_at TIMESTAMPTZ DEFAULT NOW(),
            status VARCHAR NOT NULL,
            streamers_found INTEGER DEFAULT 0 CHECK (streamers_found >= 0),
            details TEXT
        );
        '''
    ]
    
    try:
        for sql in sql_commands:
            # Use Supabase's RPC endpoint to execute SQL
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
                headers=headers,
                json={"sql": sql.strip()}
            )
            
            if response.status_code not in [200, 201, 204]:
                print(f"⚠️  SQL command failed: {response.status_code} - {response.text}")
                continue
        
        print("✅ Database tables created successfully via API!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables via API: {e}")
        return False

def create_tables():
    """Create database tables manually"""
    if not PSYCOPG2_AVAILABLE:
        print("❌ psycopg2 not available for direct database connection")
        return False
        
    print("🗄️  Creating database tables manually...")
    
    try:
        # Connect to database
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create extension for UUID
        cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")
        
        # Create streamers table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS streamers (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                twitch_user_id VARCHAR NOT NULL UNIQUE,
                username VARCHAR NOT NULL,
                display_name VARCHAR,
                follower_count INTEGER DEFAULT 0 CHECK (follower_count >= 0),
                is_live BOOLEAN DEFAULT FALSE,
                current_game VARCHAR,
                stream_title VARCHAR,
                thumbnail_url VARCHAR,
                profile_image_url VARCHAR,
                language VARCHAR DEFAULT 'en',
                last_seen_live_at TIMESTAMPTZ,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
        """)
        
        # Create user_profiles table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_profiles (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                email VARCHAR NOT NULL UNIQUE,
                full_name VARCHAR,
                daily_request_count INTEGER DEFAULT 0 CHECK (daily_request_count >= 0),
                last_request_date TIMESTAMPTZ,
                role VARCHAR DEFAULT 'agent',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
        """)
        
        # Create assignments table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS assignments (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                agent_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
                streamer_id VARCHAR NOT NULL REFERENCES streamers(twitch_user_id) ON DELETE CASCADE,
                status VARCHAR DEFAULT 'assigned',
                assigned_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                deleted_at TIMESTAMPTZ,
                notes TEXT,
                UNIQUE(agent_id, streamer_id)
            );
        """)
        
        # Create scraper_runs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS scraper_runs (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                run_at TIMESTAMPTZ DEFAULT NOW(),
                status VARCHAR NOT NULL,
                streamers_found INTEGER DEFAULT 0 CHECK (streamers_found >= 0),
                details TEXT
            );
        """)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_streamers_twitch_user_id ON streamers(twitch_user_id);",
            "CREATE INDEX IF NOT EXISTS idx_streamers_username ON streamers(username);",
            "CREATE INDEX IF NOT EXISTS idx_streamers_follower_count ON streamers(follower_count);",
            "CREATE INDEX IF NOT EXISTS idx_streamers_is_live ON streamers(is_live);",
            "CREATE INDEX IF NOT EXISTS idx_streamers_language ON streamers(language);",
            "CREATE INDEX IF NOT EXISTS idx_streamers_available ON streamers(is_live, language, follower_count);",
            "CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);",
            "CREATE INDEX IF NOT EXISTS idx_assignments_agent_id ON assignments(agent_id);",
            "CREATE INDEX IF NOT EXISTS idx_assignments_streamer_id ON assignments(streamer_id);",
            "CREATE INDEX IF NOT EXISTS idx_assignments_assigned_at ON assignments(assigned_at);",
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        # Create update trigger function
        cursor.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ language 'plpgsql';
        """)
        
        # Create triggers
        triggers = [
            "CREATE TRIGGER update_streamers_updated_at BEFORE UPDATE ON streamers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();",
            "CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();",
            "CREATE TRIGGER update_assignments_updated_at BEFORE UPDATE ON assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();"
        ]
        
        for trigger_sql in triggers:
            try:
                cursor.execute(trigger_sql)
            except psycopg2.errors.DuplicateObject:
                # Trigger already exists
                pass
        
        # Insert test user
        cursor.execute("""
            INSERT INTO user_profiles (email, full_name, role, is_active)
            VALUES ('<EMAIL>', 'Test Agent', 'agent', true)
            ON CONFLICT (email) DO NOTHING;
        """)
        
        print("✅ Database tables created successfully!")
        
        # Verify tables exist
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name IN ('streamers', 'user_profiles', 'assignments', 'scraper_runs')
        """)
        tables = cursor.fetchall()
        print(f"📋 Created tables: {', '.join([t[0] for t in tables])}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def insert_sample_data_via_api():
    """Insert sample data using Supabase REST API"""
    print("📊 Inserting sample data via API...")
    
    headers = {
        "apikey": SUPABASE_SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    # Sample streamers
    sample_streamers = [
        {
            "twitch_user_id": "123456789",
            "username": "pixel_artist_jane",
            "display_name": "Pixel Artist Jane",
            "follower_count": 42,
            "is_live": True,
            "current_game": "Art",
            "stream_title": "Creating pixel art for indie game",
            "language": "en"
        },
        {
            "twitch_user_id": "987654321",
            "username": "small_rpg_gamer",
            "display_name": "Small RPG Gamer",
            "follower_count": 28,
            "is_live": False,
            "current_game": "Baldur's Gate 3",
            "stream_title": "First time playing this!",
            "language": "en"
        },
        {
            "twitch_user_id": "456789123",
            "username": "indie_dev_alex",
            "display_name": "Indie Dev Alex",
            "follower_count": 15,
            "is_live": True,
            "current_game": "Software and Game Development",
            "stream_title": "Building a 2D platformer",
            "language": "en"
        }
    ]
    
    try:
        # Insert streamers
        for streamer in sample_streamers:
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/streamers",
                headers=headers,
                json=streamer
            )
            if response.status_code not in [200, 201, 409]:  # 409 = conflict (already exists)
                print(f"⚠️  Failed to insert streamer {streamer['username']}: {response.status_code}")
        
        # Insert test user
        test_user = {
            "email": "<EMAIL>",
            "full_name": "Test Agent",
            "role": "agent",
            "is_active": True
        }
        
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/user_profiles",
            headers=headers,
            json=test_user
        )
        
        print(f"✅ Inserted {len(sample_streamers)} sample streamers and 1 test user via API")
        
    except Exception as e:
        print(f"❌ Error inserting sample data via API: {e}")

def insert_sample_data():
    """Insert sample streamers for testing"""
    if not PSYCOPG2_AVAILABLE:
        return insert_sample_data_via_api()
        
    print("📊 Inserting sample data...")
    
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Sample streamers
        sample_streamers = [
            ("123456789", "pixel_artist_jane", "Pixel Artist Jane", 42, True, "Art", "Creating pixel art for indie game", "en"),
            ("987654321", "small_rpg_gamer", "Small RPG Gamer", 28, False, "Baldur's Gate 3", "First time playing this!", "en"),
            ("456789123", "indie_dev_alex", "Indie Dev Alex", 15, True, "Software and Game Development", "Building a 2D platformer", "en"),
            ("789123456", "cozy_gamer_sam", "Cozy Gamer Sam", 8, False, "Stardew Valley", "Chill farming stream", "en"),
            ("321654987", "art_streamer_mike", "Art Streamer Mike", 35, True, "Art", "Digital painting tutorial", "en"),
        ]
        
        for streamer in sample_streamers:
            cursor.execute("""
                INSERT INTO streamers (twitch_user_id, username, display_name, follower_count, is_live, current_game, stream_title, language, last_seen_live_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
                ON CONFLICT (twitch_user_id) DO UPDATE SET
                    follower_count = EXCLUDED.follower_count,
                    is_live = EXCLUDED.is_live,
                    current_game = EXCLUDED.current_game,
                    stream_title = EXCLUDED.stream_title,
                    last_seen_live_at = EXCLUDED.last_seen_live_at,
                    updated_at = NOW();
            """, streamer)
        
        print(f"✅ Inserted {len(sample_streamers)} sample streamers")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error inserting sample data: {e}")

def main():
    """Main setup function"""
    print("🚀 Setting up Artwork Outreach Database Manually\n")
    
    # Try API approach first
    print("Attempting to create tables via Supabase REST API...")
    if create_tables_via_api():
        insert_sample_data()
        
        print("\n🎉 Database setup complete!")
        print("💡 Next steps:")
        print("   1. Start the backend: uvicorn app.main:app --reload")
        print("   2. Test the API: http://localhost:8000/docs")
        print("   3. Test streamers endpoint: http://localhost:8000/api/v1/streamers/stats")
        return
    
    # Fallback to direct database connection
    print("API approach failed, trying direct database connection...")
    if create_tables():
        insert_sample_data()
        
        print("\n🎉 Database setup complete!")
        print("💡 Next steps:")
        print("   1. Start the backend: uvicorn app.main:app --reload")
        print("   2. Test the API: http://localhost:8000/docs")
        print("   3. Test streamers endpoint: http://localhost:8000/api/v1/streamers/stats")
    else:
        print("\n❌ Database setup failed!")
        print("💡 Alternative: Try using the Supabase dashboard to create tables manually")
        print("   1. Go to https://supabase.com/dashboard")
        print("   2. Open your project: wejiqonfxofwbiubqmpo")
        print("   3. Go to Table Editor and create the tables manually")
        sys.exit(1)

if __name__ == "__main__":
    main()