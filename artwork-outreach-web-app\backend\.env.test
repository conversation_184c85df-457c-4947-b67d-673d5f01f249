# Test Environment Configuration
# This file is used by pytest to configure the application for testing.

# Use an in-memory SQLite database for tests to ensure isolation and speed.
# The database is created and destroyed for each test session.
DATABASE_URL=sqlite:///./test_artwork_outreach.db

# Mocked credentials for testing purposes
CLERK_SECRET_KEY=test_clerk_secret_key
TWITCH_CLIENT_ID=test_twitch_client_id
TWITCH_CLIENT_SECRET=test_twitch_client_secret
SENTRY_DSN=

# App Configuration for testing
ENVIRONMENT=test
LOG_LEVEL=DEBUG
CORS_ORIGINS='[]'