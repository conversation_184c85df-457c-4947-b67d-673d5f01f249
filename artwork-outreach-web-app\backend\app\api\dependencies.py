from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
from app.database.connection import get_db_session
from app.services import auth_service, user_service
from app.database.models import UserProfile

security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db_session: AsyncSession = Depends(get_db_session),
    auth_service_instance: auth_service.ClerkAuthService = Depends(auth_service.get_auth_service),
) -> UserProfile:
    """
    Dependency to get the current user from the token.
    """
    try:
        claims: Dict[str, Any] = await auth_service_instance.verify_token(credentials.credentials)
        user_profile = await user_service.get_or_create_user_profile(
            db_session=db_session, claims=claims
        )
        if not user_profile:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
            )
        return user_profile
    except HTTPException as e:
        # Re-raise HTTPException to let FastAP<PERSON> handle it
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Could not validate credentials: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def limit_requests(
    current_user: UserProfile = Depends(get_current_user),
    db_session: AsyncSession = Depends(get_db_session),
):
    """
    Dependency to limit the number of requests a user can make per day.
    """
    await user_service.check_and_increment_request_count(
        user=current_user, db_session=db_session
    )