#!/usr/bin/env python3
"""
Automation Monitor - Real-time monitoring dashboard for 24/7 Twitch scraper
"""

import asyncio
import json
import time
import requests
from datetime import datetime, timedelta
from pathlib import Path
import sys

class AutomationMonitor:
    """Real-time monitoring for automation system"""
    
    def __init__(self, backend_url="http://localhost:8000"):
        self.backend_url = backend_url
        self.api_base = f"{backend_url}/api/v1"
        self.running = False
        
    def clear_screen(self):
        """Clear terminal screen"""
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_system_status(self):
        """Get comprehensive system status"""
        try:
            # Get automation status
            automation_response = requests.get(f"{self.api_base}/automation/status", timeout=5)
            automation_status = automation_response.json() if automation_response.status_code == 200 else {"error": "Failed to get automation status"}
            
            # Get fallback stats
            stats_response = requests.get(f"{self.api_base}/fallback/stats", timeout=5)
            stats = stats_response.json() if stats_response.status_code == 200 else {"error": "Failed to get stats"}
            
            # Get health check
            health_response = requests.get(f"{self.api_base}/health", timeout=5)
            health = health_response.json() if health_response.status_code == 200 else {"error": "Failed to get health"}
            
            return {
                "automation": automation_status,
                "stats": stats,
                "health": health,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "error": f"Failed to get system status: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def get_log_summary(self):
        """Get recent log summary"""
        try:
            log_dir = Path("logs")
            if not log_dir.exists():
                return {"message": "No logs directory found"}
            
            # Get most recent log file
            log_files = list(log_dir.glob("*.log"))
            if not log_files:
                return {"message": "No log files found"}
            
            latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
            
            # Read last 10 lines
            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-10:] if len(lines) > 10 else lines
            
            return {
                "latest_log_file": str(latest_log),
                "recent_lines": [line.strip() for line in recent_lines],
                "total_lines": len(lines)
            }
            
        except Exception as e:
            return {"error": f"Failed to read logs: {str(e)}"}
    
    def format_status_display(self, status):
        """Format status for terminal display"""
        lines = []
        lines.append("=" * 80)
        lines.append("🚀 TWITCH SCRAPER AUTOMATION MONITOR")
        lines.append(f"⏰ Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("=" * 80)
        
        # System Health
        lines.append("\n🏥 SYSTEM HEALTH:")
        if "error" in status:
            lines.append(f"   ❌ Error: {status['error']}")
        else:
            health = status.get("health", {})
            if health.get("status") == "healthy":
                lines.append("   ✅ Backend: Healthy")
            else:
                lines.append("   ❌ Backend: Unhealthy")
        
        # Automation Status
        lines.append("\n🤖 AUTOMATION STATUS:")
        automation = status.get("automation", {})
        if "error" in automation:
            lines.append(f"   ❌ Error: {automation['error']}")
        else:
            scheduler = automation.get("scheduler", {})
            if scheduler.get("status") == "running":
                lines.append("   ✅ Scheduler: Running")
                lines.append(f"   📊 Active Jobs: {scheduler.get('total_jobs', 0)}")
                
                # Show job details
                jobs = scheduler.get("jobs", [])
                for job in jobs:
                    next_run = job.get("next_run", "Unknown")
                    if next_run != "Unknown" and next_run:
                        try:
                            next_run_dt = datetime.fromisoformat(next_run.replace('Z', '+00:00'))
                            next_run_str = next_run_dt.strftime('%Y-%m-%d %H:%M:%S UTC')
                        except:
                            next_run_str = next_run
                    else:
                        next_run_str = "Not scheduled"
                    
                    lines.append(f"   📅 {job.get('name', 'Unknown')}: {next_run_str}")
            else:
                lines.append("   ❌ Scheduler: Stopped")
        
        # Database Stats
        lines.append("\n📊 DATABASE STATS:")
        stats = status.get("stats", {})
        if "error" in stats:
            lines.append(f"   ❌ Error: {stats['error']}")
        else:
            lines.append(f"   📈 Total Streamers: {stats.get('total_streamers', 'Unknown')}")
            lines.append(f"   🔴 Live Streamers: {stats.get('live_streamers', 'Unknown')}")
            lines.append(f"   ✅ Available Streamers: {stats.get('available_streamers', 'Unknown')}")
            lines.append(f"   👥 Average Followers: {stats.get('average_followers', 'Unknown')}")
        
        # Recent Activity
        lines.append("\n📝 RECENT LOGS:")
        log_summary = self.get_log_summary()
        if "error" in log_summary:
            lines.append(f"   ❌ {log_summary['error']}")
        elif "message" in log_summary:
            lines.append(f"   ℹ️  {log_summary['message']}")
        else:
            lines.append(f"   📄 Log File: {log_summary.get('latest_log_file', 'Unknown')}")
            recent_lines = log_summary.get('recent_lines', [])
            for line in recent_lines[-5:]:  # Show last 5 lines
                if line.strip():
                    # Truncate long lines
                    display_line = line[:70] + "..." if len(line) > 70 else line
                    lines.append(f"   📝 {display_line}")
        
        # Controls
        lines.append("\n🎮 CONTROLS:")
        lines.append("   [R] Refresh Now")
        lines.append("   [S] Start Scheduler")
        lines.append("   [T] Stop Scheduler") 
        lines.append("   [M] Manual Scrape")
        lines.append("   [Q] Quit Monitor")
        
        lines.append("\n" + "=" * 80)
        
        return "\n".join(lines)
    
    async def run_monitor(self):
        """Run the monitoring dashboard"""
        self.running = True
        
        print("🚀 Starting Automation Monitor...")
        print("Press 'Q' to quit, 'R' to refresh")
        
        while self.running:
            try:
                # Get status
                status = self.get_system_status()
                
                # Clear screen and display
                self.clear_screen()
                display = self.format_status_display(status)
                print(display)
                
                # Wait for input or timeout
                try:
                    # Non-blocking input check (simplified for demo)
                    await asyncio.sleep(5)  # Refresh every 5 seconds
                    
                except KeyboardInterrupt:
                    break
                    
            except Exception as e:
                print(f"❌ Monitor error: {e}")
                await asyncio.sleep(5)
        
        print("\n👋 Monitor stopped")
    
    def trigger_manual_scrape(self):
        """Trigger manual scrape"""
        try:
            response = requests.post(f"{self.api_base}/automation/trigger-scrape", timeout=10)
            if response.status_code == 200:
                print("✅ Manual scrape triggered successfully!")
            else:
                print(f"❌ Failed to trigger scrape: {response.status_code}")
        except Exception as e:
            print(f"❌ Error triggering scrape: {e}")
    
    def start_scheduler(self):
        """Start the automation scheduler"""
        try:
            response = requests.post(f"{self.api_base}/automation/start", timeout=10)
            if response.status_code == 200:
                print("✅ Scheduler started successfully!")
            else:
                print(f"❌ Failed to start scheduler: {response.status_code}")
        except Exception as e:
            print(f"❌ Error starting scheduler: {e}")
    
    def stop_scheduler(self):
        """Stop the automation scheduler"""
        try:
            response = requests.post(f"{self.api_base}/automation/stop", timeout=10)
            if response.status_code == 200:
                print("✅ Scheduler stopped successfully!")
            else:
                print(f"❌ Failed to stop scheduler: {response.status_code}")
        except Exception as e:
            print(f"❌ Error stopping scheduler: {e}")

def main():
    """Main function"""
    print("🚀 Twitch Scraper Automation Monitor")
    print("=" * 50)
    
    # Check if backend is running
    monitor = AutomationMonitor()
    try:
        response = requests.get(f"{monitor.api_base}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Backend is not responding. Please start the backend first:")
            print("   cd artwork-outreach-web-app/backend")
            print("   uvicorn app.main:app --reload")
            return
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        print("Please start the backend first:")
        print("   cd artwork-outreach-web-app/backend")
        print("   uvicorn app.main:app --reload")
        return
    
    print("✅ Backend connection successful!")
    print("Starting monitor in 3 seconds...")
    time.sleep(3)
    
    try:
        asyncio.run(monitor.run_monitor())
    except KeyboardInterrupt:
        print("\n👋 Monitor stopped by user")

if __name__ == "__main__":
    main()
