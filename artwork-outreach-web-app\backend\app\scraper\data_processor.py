from typing import List, Dict, Any, Set
from app.schemas.streamer import StreamerCreate
from app.utils.enrichment import enrich_streamer_data
from pydantic import ValidationError
import logging

logger = logging.getLogger(__name__)

class DataProcessor:
    """
    Processes raw scraped data into a clean, validated, and enriched format.
    """

    def process_scraped_data(self, raw_data: List[Dict[str, Any]]) -> List[StreamerCreate]:
        """
        Validates, cleans, enriches, and deduplicates raw streamer data.

        Args:
            raw_data: A list of raw streamer data dictionaries.

        Returns:
            A list of processed and validated StreamerCreate objects.
        """
        processed_streamers: List[StreamerCreate] = []
        processed_twitch_ids: Set[str] = set()

        for streamer_data in raw_data:
            try:
                # Clean and normalize data
                cleaned_data = {k: v.strip() if isinstance(v, str) else v for k, v in streamer_data.items()}

                # Enrich data
                enriched_data = enrich_streamer_data(cleaned_data)

                # Validate data using Pydantic model
                streamer = StreamerCreate(**enriched_data)

                # Deduplicate
                if streamer.twitch_user_id not in processed_twitch_ids:
                    processed_streamers.append(streamer)
                    processed_twitch_ids.add(streamer.twitch_user_id)
                else:
                    logger.info(f"Duplicate streamer found and skipped: {streamer.twitch_user_id}")

            except ValidationError as e:
                logger.error(f"Validation error for streamer data: {streamer_data}. Error: {e}")
            except Exception as e:
                logger.error(f"An unexpected error occurred while processing streamer data: {streamer_data}. Error: {e}")

        return processed_streamers
