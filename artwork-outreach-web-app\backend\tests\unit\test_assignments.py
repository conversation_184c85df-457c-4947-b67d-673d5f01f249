import pytest
from httpx import AsyncClient
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.database.models import User<PERSON><PERSON><PERSON><PERSON>, Streamer, Assignment
from app.api.dependencies import get_current_user
from app.main import app

def create_mock_user(email_suffix: str = ""):
    return UserProfile(
        id=uuid.uuid4(),
        full_name="Test User",
        email=f"test{email_suffix}@example.com",
        role="agent",
        daily_request_count=5,
    )

def create_mock_streamer(twitch_user_id: str, is_live: bool = False, last_seen_live: datetime = None, follower_count: int = 25):
    return Streamer(
        twitch_user_id=twitch_user_id,
        username=f"test_streamer_{twitch_user_id}",
        display_name=f"Test Streamer {twitch_user_id}",
        follower_count=follower_count,
        language="en",
        is_live=is_live,
        last_seen_live_at=last_seen_live or datetime.utcnow(),
    )

@pytest.mark.asyncio
async def test_create_assignment(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test creating a new assignment.
    """
    mock_user = create_mock_user()
    db_session.add(mock_user)

    streamer = create_mock_streamer("12345")
    db_session.add(streamer)
    await db_session.commit()

    app.dependency_overrides[get_current_user] = lambda: mock_user
    
    request_data = {"streamer_ids": [streamer.twitch_user_id]}
    response = await async_client.post("/api/v1/assignments/", json=request_data)
    
    assert response.status_code == 200
    json_response = response.json()
    assert len(json_response) == 1
    assert json_response[0]["streamer_twitch_id"] == streamer.twitch_user_id
    assert json_response[0]["agent_id"] == str(mock_user.id)

    # Verify the assignment in the database
    assignment = await db_session.get(Assignment, json_response[0]["id"])
    assert assignment is not None

@pytest.mark.asyncio
async def test_read_assignments(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test reading a list of assignments.
    """
    mock_user = create_mock_user()
    db_session.add(mock_user)

    streamer = create_mock_streamer("12345")
    db_session.add(streamer)

    assignment = Assignment(streamer_id=streamer.twitch_user_id, agent_id=mock_user.id, status="PENDING")
    db_session.add(assignment)
    await db_session.commit()

    app.dependency_overrides[get_current_user] = lambda: mock_user
    response = await async_client.get("/api/v1/assignments/")
    assert response.status_code == 200
    json_response = response.json()
    assert len(json_response["assignments"]) == 1
    assert json_response["assignments"][0]["id"] == str(assignment.id)

@pytest.mark.asyncio
async def test_update_assignment(async_client: AsyncClient, db_session: AsyncSession):
    """
    Test updating an assignment.
    """
    mock_user = create_mock_user()
    db_session.add(mock_user)

    streamer = create_mock_streamer("12345")
    db_session.add(streamer)

    assignment = Assignment(streamer_id=streamer.twitch_user_id, agent_id=mock_user.id, status="PENDING")
    db_session.add(assignment)
    await db_session.commit()
    await db_session.refresh(assignment)

    app.dependency_overrides[get_current_user] = lambda: mock_user
    
    update_data = {"status": "INTERESTED"}
    response = await async_client.put(f"/api/v1/assignments/{assignment.id}", json=update_data)
    
    assert response.status_code == 200
    json_response = response.json()
    assert json_response["status"] == "INTERESTED"

    # Verify the change in the database
    updated_assignment = await db_session.get(Assignment, assignment.id)
    assert updated_assignment.status == "INTERESTED"