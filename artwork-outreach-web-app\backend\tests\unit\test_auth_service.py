import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>

from app.services.auth_service import ClerkAuthService
from app.config import Settings

@pytest.fixture
def mock_settings_dev():
    """Fixture for settings in development environment."""
    return Settings(ENVIRONMENT="development", CLERK_SECRET_KEY="a-secret-key")

@pytest.fixture
def mock_settings_prod():
    """Fixture for settings in production environment."""
    return Settings(ENVIRONMENT="production", CLERK_SECRET_KEY="a-secret-key")

@pytest.fixture
def auth_service_dev(mock_settings_dev):
    """Fixture for ClerkAuthService in development."""
    return ClerkAuthService(settings=mock_settings_dev)

@pytest.fixture
def auth_service_prod(mock_settings_prod):
    """Fixture for ClerkAuthService in production."""
    return ClerkAuthService(settings=mock_settings_prod)

@pytest.mark.asyncio
async def test_verify_token_dev_mode(auth_service_dev: ClerkAuthService):
    """Test token verification in development mode returns a mock user."""
    token = "any-token"
    claims = await auth_service_dev.verify_token(token)
    
    assert claims["user_id"] == "dev_user_123"
    assert claims["email"] == "<EMAIL>"

@pytest.mark.asyncio
@patch("jwt.decode")
async def test_verify_token_prod_mode_success(mock_jwt_decode, auth_service_prod: ClerkAuthService):
    """Test token verification in production mode with a valid token."""
    mock_payload = {"user_id": "prod_user_456", "email": "<EMAIL>"}
    mock_jwt_decode.return_value = mock_payload
    
    token = "a-valid-jwt-token"
    claims = await auth_service_prod.verify_token(token)
    
    mock_jwt_decode.assert_called_once_with(token, options={"verify_signature": False})
    assert claims == mock_payload

@pytest.mark.asyncio
@patch("jwt.decode")
async def test_verify_token_prod_mode_invalid(mock_jwt_decode, auth_service_prod: ClerkAuthService):
    """Test token verification in production mode with an invalid token."""
    mock_jwt_decode.side_effect = Exception("Invalid token")
    
    token = "an-invalid-jwt-token"
    
    with pytest.raises(HTTPException) as exc_info:
        await auth_service_prod.verify_token(token)
        
    assert exc_info.value.status_code == 401
    assert "Invalid or expired token" in exc_info.value.detail