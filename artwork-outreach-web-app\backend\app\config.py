from functools import lru_cache
from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings):
    # Supabase Configuration
    SUPABASE_URL: str
    SUPABASE_KEY: str
    SUPABASE_SERVICE_ROLE_KEY: str = ""
    
    # Database Configuration
    DATABASE_URL: str
    
    # Clerk Authentication
    CLERK_SECRET_KEY: str
    CLERK_PUBLISHABLE_KEY: str = ""
    
    # Twitch API
    TWITCH_CLIENT_ID: str
    TWITCH_CLIENT_SECRET: str
    
    # Application Configuration
    ENVIRONMENT: str = "development"
    LOG_LEVEL: str = "INFO"
    SECRET_KEY: str = "default-secret-key-change-in-production"
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # Database Pool Configuration
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20
    DB_POOL_RECYCLE: int = 1800
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Monitoring
    SENTRY_DSN: str = ""

    class Config:
        env_file = ".env"

@lru_cache()
def get_settings():
    return Settings()