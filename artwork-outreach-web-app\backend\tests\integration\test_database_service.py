import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database.models import Base, Streamer, Worker, Assignment
from app.services.database_service import DatabaseService

DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="function")
def db_session():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def database_service(db_session):
    return DatabaseService(db_session)

def test_upsert_streamer(database_service: DatabaseService):
    """
    Test the upsert logic for streamers.
    """
    streamer_data = {"twitch_name": "teststreamer", "is_live": True}
    database_service.upsert_streamer(streamer_data)
    
    streamer = db_session.query(Streamer).filter_by(twitch_name="teststreamer").first()
    assert streamer is not None
    assert streamer.is_live is True
    
    updated_streamer_data = {"twitch_name": "teststreamer", "is_live": False}
    database_service.upsert_streamer(updated_streamer_data)
    
    streamer = db_session.query(Streamer).filter_by(twitch_name="teststreamer").first()
    assert streamer.is_live is False

def test_create_worker(database_service: DatabaseService):
    """
    Test creating a worker.
    """
    worker_data = {"username": "testworker", "is_active": True}
    worker = database_service.create_worker(worker_data)
    
    assert worker.id is not None
    assert worker.username == "testworker"

def test_create_assignment(database_service: DatabaseService):
    """
    Test creating an assignment.
    """
    streamer_data = {"twitch_name": "teststreamer", "is_live": True}
    database_service.upsert_streamer(streamer_data)
    streamer = db_session.query(Streamer).filter_by(twitch_name="teststreamer").first()
    
    worker_data = {"username": "testworker", "is_active": True}
    worker = database_service.create_worker(worker_data)
    
    assignment = database_service.create_assignment(worker.id, streamer.id)
    
    assert assignment.id is not None
    assert assignment.worker_id == worker.id
    assert assignment.streamer_id == streamer.id