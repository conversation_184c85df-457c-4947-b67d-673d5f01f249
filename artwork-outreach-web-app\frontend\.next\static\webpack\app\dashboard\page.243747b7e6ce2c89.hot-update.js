"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\nconst API_BASE_URL = \"http://127.0.0.1:8000/api/v1\" || 0;\nclass ApiClient {\n    setTokenProvider(getTokenFn) {\n        this.getTokenFn = getTokenFn;\n    }\n    async makeRequest(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        // Get auth token from Clerk\n        const token = await this.getAuthToken();\n        const defaultHeaders = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            defaultHeaders[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const config = {\n            ...options,\n            headers: {\n                ...defaultHeaders,\n                ...options.headers\n            }\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        detail: \"Unknown error\"\n                    }));\n                throw new Error(errorData.detail || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            throw error;\n        }\n    }\n    async getAuthToken() {\n        if (this.getTokenFn) {\n            try {\n                return await this.getTokenFn();\n            } catch (error) {\n                console.error(\"Failed to get auth token:\", error);\n                return null;\n            }\n        }\n        return null;\n    }\n    calculateTimeSinceLive(lastSeenLiveAt) {\n        if (!lastSeenLiveAt) return null;\n        const now = new Date();\n        const lastSeen = new Date(lastSeenLiveAt);\n        const diffMs = now.getTime() - lastSeen.getTime();\n        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffDays > 0) {\n            return \"\".concat(diffDays, \" day\").concat(diffDays > 1 ? \"s\" : \"\", \" ago\");\n        } else if (diffHours > 0) {\n            return \"\".concat(diffHours, \" hour\").concat(diffHours > 1 ? \"s\" : \"\", \" ago\");\n        } else {\n            return \"Recently\";\n        }\n    }\n    formatFollowerCount(count) {\n        if (count >= 1000000) {\n            return \"\".concat((count / 1000000).toFixed(1), \"M\");\n        } else if (count >= 1000) {\n            return \"\".concat((count / 1000).toFixed(1), \"K\");\n        } else {\n            return count.toString();\n        }\n    }\n    // Streamer API methods\n    async getAvailableStreamers() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50;\n        const backendStreamers = await this.makeRequest(\"/fallback/available?limit=\".concat(limit));\n        // Transform backend data to frontend format\n        const streamers = backendStreamers.map((streamer)=>({\n                id: streamer.id || streamer.twitch_user_id,\n                twitch_user_id: streamer.twitch_user_id,\n                username: streamer.username,\n                display_name: streamer.display_name,\n                follower_count: streamer.follower_count,\n                is_live: streamer.is_live || false,\n                current_game: streamer.current_game || null,\n                stream_title: streamer.stream_title || null,\n                profile_image_url: streamer.thumbnail_url || \"https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png\",\n                language: streamer.language,\n                last_seen_live_at: streamer.last_seen_live_at || null,\n                created_at: streamer.created_at || new Date().toISOString(),\n                updated_at: streamer.updated_at || new Date().toISOString(),\n                time_since_live: this.calculateTimeSinceLive(streamer.last_seen_live_at),\n                formatted_follower_count: this.formatFollowerCount(streamer.follower_count),\n                assignment_status_for_current_user: null\n            }));\n        return {\n            streamers,\n            total: streamers.length,\n            user_status: {\n                daily_requests_used: 1,\n                daily_requests_remaining: 2,\n                can_make_request: true\n            }\n        };\n    }\n    async getLiveVerifiedStreamers() {\n        let batchSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 100;\n        const response = await this.makeRequest(\"/fallback/live-verified?batch_size=\".concat(batchSize));\n        // Transform backend data to frontend format\n        const streamers = response.streamers.map((streamer)=>({\n                id: streamer.id || streamer.twitch_user_id,\n                twitch_user_id: streamer.twitch_user_id,\n                username: streamer.username,\n                display_name: streamer.display_name,\n                follower_count: streamer.follower_count,\n                is_live: true,\n                current_game: streamer.current_game || null,\n                stream_title: streamer.stream_title || null,\n                profile_image_url: streamer.thumbnail_url || \"https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png\",\n                language: streamer.language,\n                last_seen_live_at: new Date().toISOString(),\n                created_at: streamer.created_at || new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                time_since_live: \"Live now\",\n                formatted_follower_count: this.formatFollowerCount(streamer.follower_count),\n                assignment_status_for_current_user: null\n            }));\n        return {\n            streamers,\n            batch_size: response.batch_size,\n            checked_count: response.checked_count,\n            verification_method: response.verification_method,\n            status: response.status,\n            quota_info: response.quota_info\n        };\n    }\n    async getStreamerStats() {\n        const backendStats = await this.makeRequest(\"/fallback/stats\");\n        return {\n            total_streamers: backendStats.total_streamers,\n            live_streamers: backendStats.live_streamers,\n            available_streamers: backendStats.available_streamers,\n            last_updated: new Date().toISOString()\n        };\n    }\n    async triggerScrape() {\n        return this.makeRequest(\"/api/v1/streamers/trigger-scrape\", {\n            method: \"POST\"\n        });\n    }\n    async getScraperStatus() {\n        return this.makeRequest(\"/api/v1/streamers/scraper-status\");\n    }\n    // Assignment API methods\n    async getUserAssignments() {\n        return this.makeRequest(\"/api/v1/assignments/\");\n    }\n    async updateAssignmentStatus(assignmentId, status, notes) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId, \"/status\"), {\n            method: \"PATCH\",\n            body: JSON.stringify({\n                status,\n                notes\n            })\n        });\n    }\n    async deleteAssignment(assignmentId) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId), {\n            method: \"DELETE\"\n        });\n    }\n    // Health check\n    async healthCheck() {\n        return this.makeRequest(\"/api/v1/health\");\n    }\n    constructor(){\n        this.getTokenFn = null;\n        this.baseURL = API_BASE_URL;\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFFQSxNQUFNQSxlQUFlQyw4QkFBb0MsSUFBSTtBQTRDN0QsTUFBTUc7SUFRSkMsaUJBQWlCQyxVQUF3QyxFQUFFO1FBQ3pELElBQUksQ0FBQ0EsVUFBVSxHQUFHQTtJQUNwQjtJQUVBLE1BQWNDLFlBQ1pDLFFBQWdCLEVBRUo7WUFEWkMsVUFBQUEsaUVBQXVCLENBQUM7UUFFeEIsTUFBTUMsTUFBTSxHQUFrQkYsT0FBZixJQUFJLENBQUNHLE9BQU8sRUFBWSxPQUFUSDtRQUU5Qiw0QkFBNEI7UUFDNUIsTUFBTUksUUFBUSxNQUFNLElBQUksQ0FBQ0MsWUFBWTtRQUVyQyxNQUFNQyxpQkFBOEI7WUFDbEMsZ0JBQWdCO1FBQ2xCO1FBRUEsSUFBSUYsT0FBTztZQUNURSxjQUFjLENBQUMsZ0JBQWdCLEdBQUcsVUFBZ0IsT0FBTkY7UUFDOUM7UUFFQSxNQUFNRyxTQUFzQjtZQUMxQixHQUFHTixPQUFPO1lBQ1ZPLFNBQVM7Z0JBQ1AsR0FBR0YsY0FBYztnQkFDakIsR0FBR0wsUUFBUU8sT0FBTztZQUNwQjtRQUNGO1FBRUEsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTVIsS0FBS0s7WUFFbEMsSUFBSSxDQUFDRSxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1DLFlBQVksTUFBTUgsU0FBU0ksSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTzt3QkFBRUMsUUFBUTtvQkFBZ0I7Z0JBQy9FLE1BQU0sSUFBSUMsTUFBTUosVUFBVUcsTUFBTSxJQUFJLFFBQTRCTixPQUFwQkEsU0FBU1EsTUFBTSxFQUFDLE1BQXdCLE9BQXBCUixTQUFTUyxVQUFVO1lBQ3JGO1lBRUEsT0FBTyxNQUFNVCxTQUFTSSxJQUFJO1FBQzVCLEVBQUUsT0FBT00sT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsdUJBQXVCQTtZQUNyQyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFjZCxlQUF1QztRQUNuRCxJQUFJLElBQUksQ0FBQ1AsVUFBVSxFQUFFO1lBQ25CLElBQUk7Z0JBQ0YsT0FBTyxNQUFNLElBQUksQ0FBQ0EsVUFBVTtZQUM5QixFQUFFLE9BQU9xQixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtnQkFDM0MsT0FBTztZQUNUO1FBQ0Y7UUFDQSxPQUFPO0lBQ1Q7SUFFUUUsdUJBQXVCQyxjQUE2QixFQUFpQjtRQUMzRSxJQUFJLENBQUNBLGdCQUFnQixPQUFPO1FBRTVCLE1BQU1DLE1BQU0sSUFBSUM7UUFDaEIsTUFBTUMsV0FBVyxJQUFJRCxLQUFLRjtRQUMxQixNQUFNSSxTQUFTSCxJQUFJSSxPQUFPLEtBQUtGLFNBQVNFLE9BQU87UUFDL0MsTUFBTUMsWUFBWUMsS0FBS0MsS0FBSyxDQUFDSixTQUFVLFFBQU8sS0FBSyxFQUFDO1FBQ3BELE1BQU1LLFdBQVdGLEtBQUtDLEtBQUssQ0FBQ0YsWUFBWTtRQUV4QyxJQUFJRyxXQUFXLEdBQUc7WUFDaEIsT0FBTyxHQUFrQkEsT0FBZkEsVUFBUyxRQUE4QixPQUF4QkEsV0FBVyxJQUFJLE1BQU0sSUFBRztRQUNuRCxPQUFPLElBQUlILFlBQVksR0FBRztZQUN4QixPQUFPLEdBQW9CQSxPQUFqQkEsV0FBVSxTQUFnQyxPQUF6QkEsWUFBWSxJQUFJLE1BQU0sSUFBRztRQUN0RCxPQUFPO1lBQ0wsT0FBTztRQUNUO0lBQ0Y7SUFFUUksb0JBQW9CQyxLQUFhLEVBQVU7UUFDakQsSUFBSUEsU0FBUyxTQUFTO1lBQ3BCLE9BQU8sR0FBZ0MsT0FBN0IsQ0FBQ0EsUUFBUSxPQUFNLEVBQUdDLE9BQU8sQ0FBQyxJQUFHO1FBQ3pDLE9BQU8sSUFBSUQsU0FBUyxNQUFNO1lBQ3hCLE9BQU8sR0FBNkIsT0FBMUIsQ0FBQ0EsUUFBUSxJQUFHLEVBQUdDLE9BQU8sQ0FBQyxJQUFHO1FBQ3RDLE9BQU87WUFDTCxPQUFPRCxNQUFNRSxRQUFRO1FBQ3ZCO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTUMsd0JBQXlFO1lBQW5EQyxRQUFBQSxpRUFBZ0I7UUFDMUMsTUFBTUMsbUJBQW1CLE1BQU0sSUFBSSxDQUFDdkMsV0FBVyxDQUFRLDZCQUFtQyxPQUFOc0M7UUFFcEYsNENBQTRDO1FBQzVDLE1BQU1FLFlBQXdCRCxpQkFBaUJFLEdBQUcsQ0FBQyxDQUFDQyxXQUFtQjtnQkFDckVDLElBQUlELFNBQVNDLEVBQUUsSUFBSUQsU0FBU0UsY0FBYztnQkFDMUNBLGdCQUFnQkYsU0FBU0UsY0FBYztnQkFDdkNDLFVBQVVILFNBQVNHLFFBQVE7Z0JBQzNCQyxjQUFjSixTQUFTSSxZQUFZO2dCQUNuQ0MsZ0JBQWdCTCxTQUFTSyxjQUFjO2dCQUN2Q0MsU0FBU04sU0FBU00sT0FBTyxJQUFJO2dCQUM3QkMsY0FBY1AsU0FBU08sWUFBWSxJQUFJO2dCQUN2Q0MsY0FBY1IsU0FBU1EsWUFBWSxJQUFJO2dCQUN2Q0MsbUJBQW1CVCxTQUFTVSxhQUFhLElBQUk7Z0JBQzdDQyxVQUFVWCxTQUFTVyxRQUFRO2dCQUMzQkMsbUJBQW1CWixTQUFTWSxpQkFBaUIsSUFBSTtnQkFDakRDLFlBQVliLFNBQVNhLFVBQVUsSUFBSSxJQUFJOUIsT0FBTytCLFdBQVc7Z0JBQ3pEQyxZQUFZZixTQUFTZSxVQUFVLElBQUksSUFBSWhDLE9BQU8rQixXQUFXO2dCQUN6REUsaUJBQWlCLElBQUksQ0FBQ3BDLHNCQUFzQixDQUFDb0IsU0FBU1ksaUJBQWlCO2dCQUN2RUssMEJBQTBCLElBQUksQ0FBQzFCLG1CQUFtQixDQUFDUyxTQUFTSyxjQUFjO2dCQUMxRWEsb0NBQW9DO1lBQ3RDO1FBRUEsT0FBTztZQUNMcEI7WUFDQXFCLE9BQU9yQixVQUFVc0IsTUFBTTtZQUN2QkMsYUFBYTtnQkFDWEMscUJBQXFCO2dCQUNyQkMsMEJBQTBCO2dCQUMxQkMsa0JBQWtCO1lBQ3BCO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLDJCQVlIO1lBWjRCQyxZQUFBQSxpRUFBb0I7UUFhakQsTUFBTTFELFdBQVcsTUFBTSxJQUFJLENBQUNWLFdBQVcsQ0FZcEMsc0NBQWdELE9BQVZvRTtRQUV6Qyw0Q0FBNEM7UUFDNUMsTUFBTTVCLFlBQXdCOUIsU0FBUzhCLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLFdBQW1CO2dCQUN2RUMsSUFBSUQsU0FBU0MsRUFBRSxJQUFJRCxTQUFTRSxjQUFjO2dCQUMxQ0EsZ0JBQWdCRixTQUFTRSxjQUFjO2dCQUN2Q0MsVUFBVUgsU0FBU0csUUFBUTtnQkFDM0JDLGNBQWNKLFNBQVNJLFlBQVk7Z0JBQ25DQyxnQkFBZ0JMLFNBQVNLLGNBQWM7Z0JBQ3ZDQyxTQUFTO2dCQUNUQyxjQUFjUCxTQUFTTyxZQUFZLElBQUk7Z0JBQ3ZDQyxjQUFjUixTQUFTUSxZQUFZLElBQUk7Z0JBQ3ZDQyxtQkFBbUJULFNBQVNVLGFBQWEsSUFBSTtnQkFDN0NDLFVBQVVYLFNBQVNXLFFBQVE7Z0JBQzNCQyxtQkFBbUIsSUFBSTdCLE9BQU8rQixXQUFXO2dCQUN6Q0QsWUFBWWIsU0FBU2EsVUFBVSxJQUFJLElBQUk5QixPQUFPK0IsV0FBVztnQkFDekRDLFlBQVksSUFBSWhDLE9BQU8rQixXQUFXO2dCQUNsQ0UsaUJBQWlCO2dCQUNqQkMsMEJBQTBCLElBQUksQ0FBQzFCLG1CQUFtQixDQUFDUyxTQUFTSyxjQUFjO2dCQUMxRWEsb0NBQW9DO1lBQ3RDO1FBRUEsT0FBTztZQUNMcEI7WUFDQTZCLFlBQVkzRCxTQUFTMkQsVUFBVTtZQUMvQkMsZUFBZTVELFNBQVM0RCxhQUFhO1lBQ3JDQyxxQkFBcUI3RCxTQUFTNkQsbUJBQW1CO1lBQ2pEckQsUUFBUVIsU0FBU1EsTUFBTTtZQUN2QnNELFlBQVk5RCxTQUFTOEQsVUFBVTtRQUNqQztJQUNGO0lBRUEsTUFBTUMsbUJBQTJDO1FBQy9DLE1BQU1DLGVBQWUsTUFBTSxJQUFJLENBQUMxRSxXQUFXLENBQU07UUFFakQsT0FBTztZQUNMMkUsaUJBQWlCRCxhQUFhQyxlQUFlO1lBQzdDQyxnQkFBZ0JGLGFBQWFFLGNBQWM7WUFDM0NDLHFCQUFxQkgsYUFBYUcsbUJBQW1CO1lBQ3JEQyxjQUFjLElBQUlyRCxPQUFPK0IsV0FBVztRQUN0QztJQUNGO0lBRUEsTUFBTXVCLGdCQUFvRjtRQUN4RixPQUFPLElBQUksQ0FBQy9FLFdBQVcsQ0FBQyxvQ0FBb0M7WUFDMURnRixRQUFRO1FBQ1Y7SUFDRjtJQUVBLE1BQU1DLG1CQUFpQztRQUNyQyxPQUFPLElBQUksQ0FBQ2pGLFdBQVcsQ0FBQztJQUMxQjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNa0YscUJBQTRDO1FBQ2hELE9BQU8sSUFBSSxDQUFDbEYsV0FBVyxDQUFlO0lBQ3hDO0lBRUEsTUFBTW1GLHVCQUNKQyxZQUFvQixFQUNwQmxFLE1BQWMsRUFDZG1FLEtBQWMsRUFDMkQ7UUFDekUsT0FBTyxJQUFJLENBQUNyRixXQUFXLENBQUMsdUJBQW9DLE9BQWJvRixjQUFhLFlBQVU7WUFDcEVKLFFBQVE7WUFDUk0sTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFdEU7Z0JBQVFtRTtZQUFNO1FBQ3ZDO0lBQ0Y7SUFFQSxNQUFNSSxpQkFBaUJMLFlBQW9CLEVBQXVEO1FBQ2hHLE9BQU8sSUFBSSxDQUFDcEYsV0FBVyxDQUFDLHVCQUFvQyxPQUFib0YsZUFBZ0I7WUFDN0RKLFFBQVE7UUFDVjtJQUNGO0lBRUEsZUFBZTtJQUNmLE1BQU1VLGNBQThEO1FBQ2xFLE9BQU8sSUFBSSxDQUFDMUYsV0FBVyxDQUFDO0lBQzFCO0lBbE9BMkYsYUFBYzthQUZONUYsYUFBb0Q7UUFHMUQsSUFBSSxDQUFDSyxPQUFPLEdBQUdYO0lBQ2pCO0FBaU9GO0FBRU8sTUFBTW1HLFlBQVksSUFBSS9GLFlBQVk7QUFDekMsK0RBQWUrRixTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvYXBpLnRzPzJmYWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU3RyZWFtZXIgfSBmcm9tICdAL3R5cGVzL3N0cmVhbWVyJztcclxuXHJcbmNvbnN0IEFQSV9CQVNFX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FwaS92MSc7XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEFwaVN0cmVhbWVyIHtcclxuICB0d2l0Y2hfdXNlcl9pZDogc3RyaW5nO1xyXG4gIHVzZXJuYW1lOiBzdHJpbmc7XHJcbiAgZGlzcGxheV9uYW1lOiBzdHJpbmc7XHJcbiAgZm9sbG93ZXJfY291bnQ6IG51bWJlcjtcclxuICBpc19saXZlOiBib29sZWFuO1xyXG4gIGN1cnJlbnRfZ2FtZT86IHN0cmluZztcclxuICBzdHJlYW1fdGl0bGU/OiBzdHJpbmc7XHJcbiAgcHJvZmlsZV9pbWFnZV91cmw/OiBzdHJpbmc7XHJcbiAgbGFuZ3VhZ2U6IHN0cmluZztcclxuICBsYXN0X3NlZW5fbGl2ZV9hdD86IHN0cmluZztcclxuICB0d2l0Y2hfdXJsPzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFN0cmVhbWVyTGlzdFJlc3BvbnNlIHtcclxuICBzdHJlYW1lcnM6IFN0cmVhbWVyW107XHJcbiAgdG90YWw6IG51bWJlcjtcclxuICB1c2VyX3N0YXR1czoge1xyXG4gICAgZGFpbHlfcmVxdWVzdHNfdXNlZDogbnVtYmVyO1xyXG4gICAgZGFpbHlfcmVxdWVzdHNfcmVtYWluaW5nOiBudW1iZXI7XHJcbiAgICBjYW5fbWFrZV9yZXF1ZXN0OiBib29sZWFuO1xyXG4gIH07XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgU3RyZWFtZXJTdGF0cyB7XHJcbiAgdG90YWxfc3RyZWFtZXJzOiBudW1iZXI7XHJcbiAgbGl2ZV9zdHJlYW1lcnM6IG51bWJlcjtcclxuICBhdmFpbGFibGVfc3RyZWFtZXJzOiBudW1iZXI7XHJcbiAgbGFzdF91cGRhdGVkOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQXNzaWdubWVudCB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBzdHJlYW1lcl9pZDogc3RyaW5nO1xyXG4gIHN0cmVhbWVyX3VzZXJuYW1lOiBzdHJpbmc7XHJcbiAgc3RyZWFtZXJfZGlzcGxheV9uYW1lOiBzdHJpbmc7XHJcbiAgc3RhdHVzOiBzdHJpbmc7XHJcbiAgYXNzaWduZWRfYXQ6IHN0cmluZztcclxuICB1cGRhdGVkX2F0OiBzdHJpbmc7XHJcbiAgbm90ZXM/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmNsYXNzIEFwaUNsaWVudCB7XHJcbiAgcHJpdmF0ZSBiYXNlVVJMOiBzdHJpbmc7XHJcbiAgcHJpdmF0ZSBnZXRUb2tlbkZuOiAoKCkgPT4gUHJvbWlzZTxzdHJpbmcgfCBudWxsPikgfCBudWxsID0gbnVsbDtcclxuXHJcbiAgY29uc3RydWN0b3IoKSB7XHJcbiAgICB0aGlzLmJhc2VVUkwgPSBBUElfQkFTRV9VUkw7XHJcbiAgfVxyXG5cclxuICBzZXRUb2tlblByb3ZpZGVyKGdldFRva2VuRm46ICgpID0+IFByb21pc2U8c3RyaW5nIHwgbnVsbD4pIHtcclxuICAgIHRoaXMuZ2V0VG9rZW5GbiA9IGdldFRva2VuRm47XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGFzeW5jIG1ha2VSZXF1ZXN0PFQ+KFxyXG4gICAgZW5kcG9pbnQ6IHN0cmluZyxcclxuICAgIG9wdGlvbnM6IFJlcXVlc3RJbml0ID0ge31cclxuICApOiBQcm9taXNlPFQ+IHtcclxuICAgIGNvbnN0IHVybCA9IGAke3RoaXMuYmFzZVVSTH0ke2VuZHBvaW50fWA7XHJcbiAgICBcclxuICAgIC8vIEdldCBhdXRoIHRva2VuIGZyb20gQ2xlcmtcclxuICAgIGNvbnN0IHRva2VuID0gYXdhaXQgdGhpcy5nZXRBdXRoVG9rZW4oKTtcclxuICAgIFxyXG4gICAgY29uc3QgZGVmYXVsdEhlYWRlcnM6IEhlYWRlcnNJbml0ID0ge1xyXG4gICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgfTtcclxuXHJcbiAgICBpZiAodG9rZW4pIHtcclxuICAgICAgZGVmYXVsdEhlYWRlcnNbJ0F1dGhvcml6YXRpb24nXSA9IGBCZWFyZXIgJHt0b2tlbn1gO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNvbmZpZzogUmVxdWVzdEluaXQgPSB7XHJcbiAgICAgIC4uLm9wdGlvbnMsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAuLi5kZWZhdWx0SGVhZGVycyxcclxuICAgICAgICAuLi5vcHRpb25zLmhlYWRlcnMsXHJcbiAgICAgIH0sXHJcbiAgICB9O1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsLCBjb25maWcpO1xyXG4gICAgICBcclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoeyBkZXRhaWw6ICdVbmtub3duIGVycm9yJyB9KSk7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgYEhUVFAgJHtyZXNwb25zZS5zdGF0dXN9OiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdBUEkgcmVxdWVzdCBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHByaXZhdGUgYXN5bmMgZ2V0QXV0aFRva2VuKCk6IFByb21pc2U8c3RyaW5nIHwgbnVsbD4ge1xyXG4gICAgaWYgKHRoaXMuZ2V0VG9rZW5Gbikge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIHJldHVybiBhd2FpdCB0aGlzLmdldFRva2VuRm4oKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2V0IGF1dGggdG9rZW46JywgZXJyb3IpO1xyXG4gICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgY2FsY3VsYXRlVGltZVNpbmNlTGl2ZShsYXN0U2VlbkxpdmVBdDogc3RyaW5nIHwgbnVsbCk6IHN0cmluZyB8IG51bGwge1xyXG4gICAgaWYgKCFsYXN0U2VlbkxpdmVBdCkgcmV0dXJuIG51bGw7XHJcblxyXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgIGNvbnN0IGxhc3RTZWVuID0gbmV3IERhdGUobGFzdFNlZW5MaXZlQXQpO1xyXG4gICAgY29uc3QgZGlmZk1zID0gbm93LmdldFRpbWUoKSAtIGxhc3RTZWVuLmdldFRpbWUoKTtcclxuICAgIGNvbnN0IGRpZmZIb3VycyA9IE1hdGguZmxvb3IoZGlmZk1zIC8gKDEwMDAgKiA2MCAqIDYwKSk7XHJcbiAgICBjb25zdCBkaWZmRGF5cyA9IE1hdGguZmxvb3IoZGlmZkhvdXJzIC8gMjQpO1xyXG5cclxuICAgIGlmIChkaWZmRGF5cyA+IDApIHtcclxuICAgICAgcmV0dXJuIGAke2RpZmZEYXlzfSBkYXkke2RpZmZEYXlzID4gMSA/ICdzJyA6ICcnfSBhZ29gO1xyXG4gICAgfSBlbHNlIGlmIChkaWZmSG91cnMgPiAwKSB7XHJcbiAgICAgIHJldHVybiBgJHtkaWZmSG91cnN9IGhvdXIke2RpZmZIb3VycyA+IDEgPyAncycgOiAnJ30gYWdvYDtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHJldHVybiAnUmVjZW50bHknO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBmb3JtYXRGb2xsb3dlckNvdW50KGNvdW50OiBudW1iZXIpOiBzdHJpbmcge1xyXG4gICAgaWYgKGNvdW50ID49IDEwMDAwMDApIHtcclxuICAgICAgcmV0dXJuIGAkeyhjb3VudCAvIDEwMDAwMDApLnRvRml4ZWQoMSl9TWA7XHJcbiAgICB9IGVsc2UgaWYgKGNvdW50ID49IDEwMDApIHtcclxuICAgICAgcmV0dXJuIGAkeyhjb3VudCAvIDEwMDApLnRvRml4ZWQoMSl9S2A7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICByZXR1cm4gY291bnQudG9TdHJpbmcoKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIFN0cmVhbWVyIEFQSSBtZXRob2RzXHJcbiAgYXN5bmMgZ2V0QXZhaWxhYmxlU3RyZWFtZXJzKGxpbWl0OiBudW1iZXIgPSA1MCk6IFByb21pc2U8U3RyZWFtZXJMaXN0UmVzcG9uc2U+IHtcclxuICAgIGNvbnN0IGJhY2tlbmRTdHJlYW1lcnMgPSBhd2FpdCB0aGlzLm1ha2VSZXF1ZXN0PGFueVtdPihgL2ZhbGxiYWNrL2F2YWlsYWJsZT9saW1pdD0ke2xpbWl0fWApO1xyXG5cclxuICAgIC8vIFRyYW5zZm9ybSBiYWNrZW5kIGRhdGEgdG8gZnJvbnRlbmQgZm9ybWF0XHJcbiAgICBjb25zdCBzdHJlYW1lcnM6IFN0cmVhbWVyW10gPSBiYWNrZW5kU3RyZWFtZXJzLm1hcCgoc3RyZWFtZXI6IGFueSkgPT4gKHtcclxuICAgICAgaWQ6IHN0cmVhbWVyLmlkIHx8IHN0cmVhbWVyLnR3aXRjaF91c2VyX2lkLFxyXG4gICAgICB0d2l0Y2hfdXNlcl9pZDogc3RyZWFtZXIudHdpdGNoX3VzZXJfaWQsXHJcbiAgICAgIHVzZXJuYW1lOiBzdHJlYW1lci51c2VybmFtZSxcclxuICAgICAgZGlzcGxheV9uYW1lOiBzdHJlYW1lci5kaXNwbGF5X25hbWUsXHJcbiAgICAgIGZvbGxvd2VyX2NvdW50OiBzdHJlYW1lci5mb2xsb3dlcl9jb3VudCxcclxuICAgICAgaXNfbGl2ZTogc3RyZWFtZXIuaXNfbGl2ZSB8fCBmYWxzZSxcclxuICAgICAgY3VycmVudF9nYW1lOiBzdHJlYW1lci5jdXJyZW50X2dhbWUgfHwgbnVsbCxcclxuICAgICAgc3RyZWFtX3RpdGxlOiBzdHJlYW1lci5zdHJlYW1fdGl0bGUgfHwgbnVsbCxcclxuICAgICAgcHJvZmlsZV9pbWFnZV91cmw6IHN0cmVhbWVyLnRodW1ibmFpbF91cmwgfHwgJ2h0dHBzOi8vc3RhdGljLWNkbi5qdHZudy5uZXQvanR2X3VzZXJfcGljdHVyZXMvZGVmYXVsdC1wcm9maWxlX2ltYWdlLTMwMHgzMDAucG5nJyxcclxuICAgICAgbGFuZ3VhZ2U6IHN0cmVhbWVyLmxhbmd1YWdlLFxyXG4gICAgICBsYXN0X3NlZW5fbGl2ZV9hdDogc3RyZWFtZXIubGFzdF9zZWVuX2xpdmVfYXQgfHwgbnVsbCxcclxuICAgICAgY3JlYXRlZF9hdDogc3RyZWFtZXIuY3JlYXRlZF9hdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIHVwZGF0ZWRfYXQ6IHN0cmVhbWVyLnVwZGF0ZWRfYXQgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICB0aW1lX3NpbmNlX2xpdmU6IHRoaXMuY2FsY3VsYXRlVGltZVNpbmNlTGl2ZShzdHJlYW1lci5sYXN0X3NlZW5fbGl2ZV9hdCksXHJcbiAgICAgIGZvcm1hdHRlZF9mb2xsb3dlcl9jb3VudDogdGhpcy5mb3JtYXRGb2xsb3dlckNvdW50KHN0cmVhbWVyLmZvbGxvd2VyX2NvdW50KSxcclxuICAgICAgYXNzaWdubWVudF9zdGF0dXNfZm9yX2N1cnJlbnRfdXNlcjogbnVsbFxyXG4gICAgfSkpO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHN0cmVhbWVycyxcclxuICAgICAgdG90YWw6IHN0cmVhbWVycy5sZW5ndGgsXHJcbiAgICAgIHVzZXJfc3RhdHVzOiB7XHJcbiAgICAgICAgZGFpbHlfcmVxdWVzdHNfdXNlZDogMSxcclxuICAgICAgICBkYWlseV9yZXF1ZXN0c19yZW1haW5pbmc6IDIsXHJcbiAgICAgICAgY2FuX21ha2VfcmVxdWVzdDogdHJ1ZVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0TGl2ZVZlcmlmaWVkU3RyZWFtZXJzKGJhdGNoU2l6ZTogbnVtYmVyID0gMTAwKTogUHJvbWlzZTx7XHJcbiAgICBzdHJlYW1lcnM6IFN0cmVhbWVyW107XHJcbiAgICBiYXRjaF9zaXplOiBudW1iZXI7XHJcbiAgICBjaGVja2VkX2NvdW50OiBudW1iZXI7XHJcbiAgICB2ZXJpZmljYXRpb25fbWV0aG9kOiBzdHJpbmc7XHJcbiAgICBzdGF0dXM6IHN0cmluZztcclxuICAgIHF1b3RhX2luZm8/OiB7XHJcbiAgICAgIHN0cmVhbWVyc19kZWxpdmVyZWQ6IG51bWJlcjtcclxuICAgICAgdG90YWxfcmVjZWl2ZWRfdG9kYXk6IG51bWJlcjtcclxuICAgICAgZGFpbHlfbGltaXQ6IG51bWJlcjtcclxuICAgICAgcmVtYWluaW5nX3F1b3RhOiBudW1iZXI7XHJcbiAgICB9O1xyXG4gIH0+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5tYWtlUmVxdWVzdDx7XHJcbiAgICAgIHN0cmVhbWVyczogYW55W107XHJcbiAgICAgIGJhdGNoX3NpemU6IG51bWJlcjtcclxuICAgICAgY2hlY2tlZF9jb3VudDogbnVtYmVyO1xyXG4gICAgICB2ZXJpZmljYXRpb25fbWV0aG9kOiBzdHJpbmc7XHJcbiAgICAgIHN0YXR1czogc3RyaW5nO1xyXG4gICAgICBxdW90YV9pbmZvPzoge1xyXG4gICAgICAgIHN0cmVhbWVyc19kZWxpdmVyZWQ6IG51bWJlcjtcclxuICAgICAgICB0b3RhbF9yZWNlaXZlZF90b2RheTogbnVtYmVyO1xyXG4gICAgICAgIGRhaWx5X2xpbWl0OiBudW1iZXI7XHJcbiAgICAgICAgcmVtYWluaW5nX3F1b3RhOiBudW1iZXI7XHJcbiAgICAgIH07XHJcbiAgICB9PihgL2ZhbGxiYWNrL2xpdmUtdmVyaWZpZWQ/YmF0Y2hfc2l6ZT0ke2JhdGNoU2l6ZX1gKTtcclxuXHJcbiAgICAvLyBUcmFuc2Zvcm0gYmFja2VuZCBkYXRhIHRvIGZyb250ZW5kIGZvcm1hdFxyXG4gICAgY29uc3Qgc3RyZWFtZXJzOiBTdHJlYW1lcltdID0gcmVzcG9uc2Uuc3RyZWFtZXJzLm1hcCgoc3RyZWFtZXI6IGFueSkgPT4gKHtcclxuICAgICAgaWQ6IHN0cmVhbWVyLmlkIHx8IHN0cmVhbWVyLnR3aXRjaF91c2VyX2lkLFxyXG4gICAgICB0d2l0Y2hfdXNlcl9pZDogc3RyZWFtZXIudHdpdGNoX3VzZXJfaWQsXHJcbiAgICAgIHVzZXJuYW1lOiBzdHJlYW1lci51c2VybmFtZSxcclxuICAgICAgZGlzcGxheV9uYW1lOiBzdHJlYW1lci5kaXNwbGF5X25hbWUsXHJcbiAgICAgIGZvbGxvd2VyX2NvdW50OiBzdHJlYW1lci5mb2xsb3dlcl9jb3VudCxcclxuICAgICAgaXNfbGl2ZTogdHJ1ZSwgLy8gQWxsIHN0cmVhbWVycyBmcm9tIHRoaXMgZW5kcG9pbnQgYXJlIHZlcmlmaWVkIGxpdmVcclxuICAgICAgY3VycmVudF9nYW1lOiBzdHJlYW1lci5jdXJyZW50X2dhbWUgfHwgbnVsbCxcclxuICAgICAgc3RyZWFtX3RpdGxlOiBzdHJlYW1lci5zdHJlYW1fdGl0bGUgfHwgbnVsbCxcclxuICAgICAgcHJvZmlsZV9pbWFnZV91cmw6IHN0cmVhbWVyLnRodW1ibmFpbF91cmwgfHwgJ2h0dHBzOi8vc3RhdGljLWNkbi5qdHZudy5uZXQvanR2X3VzZXJfcGljdHVyZXMvZGVmYXVsdC1wcm9maWxlX2ltYWdlLTMwMHgzMDAucG5nJyxcclxuICAgICAgbGFuZ3VhZ2U6IHN0cmVhbWVyLmxhbmd1YWdlLFxyXG4gICAgICBsYXN0X3NlZW5fbGl2ZV9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLCAvLyBKdXN0IHZlcmlmaWVkIGFzIGxpdmVcclxuICAgICAgY3JlYXRlZF9hdDogc3RyZWFtZXIuY3JlYXRlZF9hdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSwgLy8gSnVzdCB1cGRhdGVkXHJcbiAgICAgIHRpbWVfc2luY2VfbGl2ZTogJ0xpdmUgbm93JyxcclxuICAgICAgZm9ybWF0dGVkX2ZvbGxvd2VyX2NvdW50OiB0aGlzLmZvcm1hdEZvbGxvd2VyQ291bnQoc3RyZWFtZXIuZm9sbG93ZXJfY291bnQpLFxyXG4gICAgICBhc3NpZ25tZW50X3N0YXR1c19mb3JfY3VycmVudF91c2VyOiBudWxsXHJcbiAgICB9KSk7XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3RyZWFtZXJzLFxyXG4gICAgICBiYXRjaF9zaXplOiByZXNwb25zZS5iYXRjaF9zaXplLFxyXG4gICAgICBjaGVja2VkX2NvdW50OiByZXNwb25zZS5jaGVja2VkX2NvdW50LFxyXG4gICAgICB2ZXJpZmljYXRpb25fbWV0aG9kOiByZXNwb25zZS52ZXJpZmljYXRpb25fbWV0aG9kLFxyXG4gICAgICBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyxcclxuICAgICAgcXVvdGFfaW5mbzogcmVzcG9uc2UucXVvdGFfaW5mb1xyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldFN0cmVhbWVyU3RhdHMoKTogUHJvbWlzZTxTdHJlYW1lclN0YXRzPiB7XHJcbiAgICBjb25zdCBiYWNrZW5kU3RhdHMgPSBhd2FpdCB0aGlzLm1ha2VSZXF1ZXN0PGFueT4oJy9mYWxsYmFjay9zdGF0cycpO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHRvdGFsX3N0cmVhbWVyczogYmFja2VuZFN0YXRzLnRvdGFsX3N0cmVhbWVycyxcclxuICAgICAgbGl2ZV9zdHJlYW1lcnM6IGJhY2tlbmRTdGF0cy5saXZlX3N0cmVhbWVycyxcclxuICAgICAgYXZhaWxhYmxlX3N0cmVhbWVyczogYmFja2VuZFN0YXRzLmF2YWlsYWJsZV9zdHJlYW1lcnMsXHJcbiAgICAgIGxhc3RfdXBkYXRlZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgdHJpZ2dlclNjcmFwZSgpOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nOyBzdGF0dXM6IHN0cmluZzsgdHJpZ2dlcmVkX2J5OiBzdHJpbmcgfT4ge1xyXG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3QoJy9hcGkvdjEvc3RyZWFtZXJzL3RyaWdnZXItc2NyYXBlJywge1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0U2NyYXBlclN0YXR1cygpOiBQcm9taXNlPGFueT4ge1xyXG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3QoJy9hcGkvdjEvc3RyZWFtZXJzL3NjcmFwZXItc3RhdHVzJyk7XHJcbiAgfVxyXG5cclxuICAvLyBBc3NpZ25tZW50IEFQSSBtZXRob2RzXHJcbiAgYXN5bmMgZ2V0VXNlckFzc2lnbm1lbnRzKCk6IFByb21pc2U8QXNzaWdubWVudFtdPiB7XHJcbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdDxBc3NpZ25tZW50W10+KCcvYXBpL3YxL2Fzc2lnbm1lbnRzLycpO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgdXBkYXRlQXNzaWdubWVudFN0YXR1cyhcclxuICAgIGFzc2lnbm1lbnRJZDogc3RyaW5nLFxyXG4gICAgc3RhdHVzOiBzdHJpbmcsXHJcbiAgICBub3Rlcz86IHN0cmluZ1xyXG4gICk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmc7IGFzc2lnbm1lbnRfaWQ6IHN0cmluZzsgbmV3X3N0YXR1czogc3RyaW5nIH0+IHtcclxuICAgIHJldHVybiB0aGlzLm1ha2VSZXF1ZXN0KGAvYXBpL3YxL2Fzc2lnbm1lbnRzLyR7YXNzaWdubWVudElkfS9zdGF0dXNgLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BBVENIJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBzdGF0dXMsIG5vdGVzIH0pLFxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICBhc3luYyBkZWxldGVBc3NpZ25tZW50KGFzc2lnbm1lbnRJZDogc3RyaW5nKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZzsgYXNzaWdubWVudF9pZDogc3RyaW5nIH0+IHtcclxuICAgIHJldHVybiB0aGlzLm1ha2VSZXF1ZXN0KGAvYXBpL3YxL2Fzc2lnbm1lbnRzLyR7YXNzaWdubWVudElkfWAsIHtcclxuICAgICAgbWV0aG9kOiAnREVMRVRFJyxcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgLy8gSGVhbHRoIGNoZWNrXHJcbiAgYXN5bmMgaGVhbHRoQ2hlY2soKTogUHJvbWlzZTx7IHN0YXR1czogc3RyaW5nOyB0aW1lc3RhbXA6IHN0cmluZyB9PiB7XHJcbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdCgnL2FwaS92MS9oZWFsdGgnKTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSBuZXcgQXBpQ2xpZW50KCk7XHJcbmV4cG9ydCBkZWZhdWx0IGFwaUNsaWVudDsiXSwibmFtZXMiOlsiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCIsIkFwaUNsaWVudCIsInNldFRva2VuUHJvdmlkZXIiLCJnZXRUb2tlbkZuIiwibWFrZVJlcXVlc3QiLCJlbmRwb2ludCIsIm9wdGlvbnMiLCJ1cmwiLCJiYXNlVVJMIiwidG9rZW4iLCJnZXRBdXRoVG9rZW4iLCJkZWZhdWx0SGVhZGVycyIsImNvbmZpZyIsImhlYWRlcnMiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJlcnJvckRhdGEiLCJqc29uIiwiY2F0Y2giLCJkZXRhaWwiLCJFcnJvciIsInN0YXR1cyIsInN0YXR1c1RleHQiLCJlcnJvciIsImNvbnNvbGUiLCJjYWxjdWxhdGVUaW1lU2luY2VMaXZlIiwibGFzdFNlZW5MaXZlQXQiLCJub3ciLCJEYXRlIiwibGFzdFNlZW4iLCJkaWZmTXMiLCJnZXRUaW1lIiwiZGlmZkhvdXJzIiwiTWF0aCIsImZsb29yIiwiZGlmZkRheXMiLCJmb3JtYXRGb2xsb3dlckNvdW50IiwiY291bnQiLCJ0b0ZpeGVkIiwidG9TdHJpbmciLCJnZXRBdmFpbGFibGVTdHJlYW1lcnMiLCJsaW1pdCIsImJhY2tlbmRTdHJlYW1lcnMiLCJzdHJlYW1lcnMiLCJtYXAiLCJzdHJlYW1lciIsImlkIiwidHdpdGNoX3VzZXJfaWQiLCJ1c2VybmFtZSIsImRpc3BsYXlfbmFtZSIsImZvbGxvd2VyX2NvdW50IiwiaXNfbGl2ZSIsImN1cnJlbnRfZ2FtZSIsInN0cmVhbV90aXRsZSIsInByb2ZpbGVfaW1hZ2VfdXJsIiwidGh1bWJuYWlsX3VybCIsImxhbmd1YWdlIiwibGFzdF9zZWVuX2xpdmVfYXQiLCJjcmVhdGVkX2F0IiwidG9JU09TdHJpbmciLCJ1cGRhdGVkX2F0IiwidGltZV9zaW5jZV9saXZlIiwiZm9ybWF0dGVkX2ZvbGxvd2VyX2NvdW50IiwiYXNzaWdubWVudF9zdGF0dXNfZm9yX2N1cnJlbnRfdXNlciIsInRvdGFsIiwibGVuZ3RoIiwidXNlcl9zdGF0dXMiLCJkYWlseV9yZXF1ZXN0c191c2VkIiwiZGFpbHlfcmVxdWVzdHNfcmVtYWluaW5nIiwiY2FuX21ha2VfcmVxdWVzdCIsImdldExpdmVWZXJpZmllZFN0cmVhbWVycyIsImJhdGNoU2l6ZSIsImJhdGNoX3NpemUiLCJjaGVja2VkX2NvdW50IiwidmVyaWZpY2F0aW9uX21ldGhvZCIsInF1b3RhX2luZm8iLCJnZXRTdHJlYW1lclN0YXRzIiwiYmFja2VuZFN0YXRzIiwidG90YWxfc3RyZWFtZXJzIiwibGl2ZV9zdHJlYW1lcnMiLCJhdmFpbGFibGVfc3RyZWFtZXJzIiwibGFzdF91cGRhdGVkIiwidHJpZ2dlclNjcmFwZSIsIm1ldGhvZCIsImdldFNjcmFwZXJTdGF0dXMiLCJnZXRVc2VyQXNzaWdubWVudHMiLCJ1cGRhdGVBc3NpZ25tZW50U3RhdHVzIiwiYXNzaWdubWVudElkIiwibm90ZXMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImRlbGV0ZUFzc2lnbm1lbnQiLCJoZWFsdGhDaGVjayIsImNvbnN0cnVjdG9yIiwiYXBpQ2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});