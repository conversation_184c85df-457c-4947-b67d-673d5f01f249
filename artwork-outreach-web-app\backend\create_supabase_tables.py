#!/usr/bin/env python3
"""
Simple Supabase table creation using supabase-py client
"""

import os
import sys

# Try to import supabase
try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    print("❌ supabase library not available")
    print("💡 Install with: pip install supabase")
    SUPABASE_AVAILABLE = False

# Configuration
SUPABASE_URL = "https://wejiqonfxofwbiubqmpo.supabase.co"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indlamlxb25meG9md2JpdWJxbXBvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTM3MjkyMSwiZXhwIjoyMDY2OTQ4OTIxfQ.YNXeWA1bj9oHLgrfPqp3le8ajlikYatPOxGcB-iseks"

def create_tables_via_sql():
    """Execute SQL commands to create tables"""
    if not SUPABASE_AVAILABLE:
        return False
    
    print("🗄️  Creating database tables via Supabase client...")
    
    try:
        # Create Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
        
        # SQL commands to create tables
        sql_commands = [
            'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
            '''
            CREATE TABLE IF NOT EXISTS streamers (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                twitch_user_id VARCHAR NOT NULL UNIQUE,
                username VARCHAR NOT NULL,
                display_name VARCHAR,
                follower_count INTEGER DEFAULT 0 CHECK (follower_count >= 0),
                is_live BOOLEAN DEFAULT FALSE,
                current_game VARCHAR,
                stream_title VARCHAR,
                thumbnail_url VARCHAR,
                profile_image_url VARCHAR,
                language VARCHAR DEFAULT 'en',
                last_seen_live_at TIMESTAMPTZ,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            ''',
            '''
            CREATE TABLE IF NOT EXISTS user_profiles (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                email VARCHAR NOT NULL UNIQUE,
                full_name VARCHAR,
                daily_request_count INTEGER DEFAULT 0 CHECK (daily_request_count >= 0),
                last_request_date TIMESTAMPTZ,
                role VARCHAR DEFAULT 'agent',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            ''',
            '''
            CREATE TABLE IF NOT EXISTS assignments (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                agent_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
                streamer_id VARCHAR NOT NULL REFERENCES streamers(twitch_user_id) ON DELETE CASCADE,
                status VARCHAR DEFAULT 'assigned',
                assigned_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                deleted_at TIMESTAMPTZ,
                notes TEXT,
                UNIQUE(agent_id, streamer_id)
            );
            ''',
            '''
            CREATE TABLE IF NOT EXISTS scraper_runs (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                run_at TIMESTAMPTZ DEFAULT NOW(),
                status VARCHAR NOT NULL,
                streamers_found INTEGER DEFAULT 0 CHECK (streamers_found >= 0),
                details TEXT
            );
            '''
        ]
        
        # Execute each SQL command
        for sql in sql_commands:
            try:
                result = supabase.postgrest.rpc('exec_sql', {'sql': sql.strip()}).execute()
                print(f"✅ Executed SQL command")
            except Exception as e:
                print(f"⚠️  SQL command failed: {e}")
                # Try alternative approach - use the supabase query method
                continue
        
        print("✅ Database tables creation completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def insert_sample_data():
    """Insert sample data using Supabase client"""
    if not SUPABASE_AVAILABLE:
        return False
    
    print("📊 Inserting sample data...")
    
    try:
        # Create Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
        
        # Sample streamers
        sample_streamers = [
            {
                "twitch_user_id": "123456789",
                "username": "pixel_artist_jane",
                "display_name": "Pixel Artist Jane",
                "follower_count": 42,
                "is_live": True,
                "current_game": "Art",
                "stream_title": "Creating pixel art for indie game",
                "language": "en"
            },
            {
                "twitch_user_id": "987654321",
                "username": "small_rpg_gamer",
                "display_name": "Small RPG Gamer",
                "follower_count": 28,
                "is_live": False,
                "current_game": "Baldur's Gate 3",
                "stream_title": "First time playing this!",
                "language": "en"
            },
            {
                "twitch_user_id": "456789123",
                "username": "indie_dev_alex",
                "display_name": "Indie Dev Alex",
                "follower_count": 15,
                "is_live": True,
                "current_game": "Software and Game Development",
                "stream_title": "Building a 2D platformer",
                "language": "en"
            }
        ]
        
        # Insert streamers
        for streamer in sample_streamers:
            try:
                result = supabase.table('streamers').upsert(streamer).execute()
                print(f"✅ Inserted streamer: {streamer['username']}")
            except Exception as e:
                print(f"⚠️  Failed to insert streamer {streamer['username']}: {e}")
        
        # Insert test user
        test_user = {
            "email": "<EMAIL>",
            "full_name": "Test Agent",
            "role": "agent",
            "is_active": True
        }
        
        try:
            result = supabase.table('user_profiles').upsert(test_user).execute()
            print("✅ Inserted test user")
        except Exception as e:
            print(f"⚠️  Failed to insert test user: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error inserting sample data: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up Artwork Outreach Database via Supabase Client\n")
    
    if not SUPABASE_AVAILABLE:
        print("❌ Supabase client not available!")
        print("💡 Install with: pip install supabase")
        print("\n📖 Alternative: Use Supabase dashboard to create tables manually:")
        print("   1. Go to https://supabase.com/dashboard")
        print("   2. Open your project: wejiqonfxofwbiubqmpo")
        print("   3. Go to SQL Editor")
        print("   4. Run the SQL commands from the documentation")
        sys.exit(1)
    
    if create_tables_via_sql():
        insert_sample_data()
        
        print("\n🎉 Database setup complete!")
        print("💡 Next steps:")
        print("   1. Start the backend: uvicorn app.main:app --reload")
        print("   2. Test the API: http://localhost:8000/docs")
        print("   3. Test streamers endpoint: http://localhost:8000/api/v1/streamers/stats")
    else:
        print("\n❌ Database setup failed!")
        print("💡 Try using the Supabase dashboard to create tables manually")
        sys.exit(1)

if __name__ == "__main__":
    main()