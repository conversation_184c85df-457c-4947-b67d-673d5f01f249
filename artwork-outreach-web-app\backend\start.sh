#!/bin/bash
set -e

echo "Starting Artwork Outreach Backend..."

# Check if running in production
if [ "$ENVIRONMENT" = "production" ]; then
    echo "Production environment detected"
    
    # Run database migrations
    echo "Running database migrations..."
    alembic upgrade head
    
    # Start the application with production settings
    echo "Starting application server..."
    exec uvicorn app.main:app \
        --host 0.0.0.0 \
        --port ${PORT:-8000} \
        --workers ${WEB_CONCURRENCY:-4} \
        --worker-class uvicorn.workers.UvicornWorker \
        --access-log \
        --error-log \
        --log-level info
else
    echo "Development environment detected"
    
    # Start with auto-reload for development
    exec uvicorn app.main:app \
        --host 0.0.0.0 \
        --port ${PORT:-8000} \
        --reload \
        --log-level debug
fi