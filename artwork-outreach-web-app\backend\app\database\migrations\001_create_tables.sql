-- Create tables for Artwork Outreach Web App
-- Migration 001: Initial schema

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create streamers table
CREATE TABLE IF NOT EXISTS streamers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    twitch_user_id VARCHAR NOT NULL UNIQUE,
    username VARCHAR NOT NULL,
    display_name VA<PERSON>HA<PERSON>,
    follower_count INTEGER DEFAULT 0 CHECK (follower_count >= 0),
    is_live BOOLEAN DEFAULT FALSE,
    current_game VARCHAR,
    stream_title VARCHAR,
    thumbnail_url VARCHAR,
    profile_image_url VARCHAR,
    language VARCHAR DEFAULT 'en',
    last_seen_live_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR NOT NULL UNIQUE,
    full_name VA<PERSON><PERSON><PERSON>,
    daily_request_count INTEGER DEFAULT 0 CHECK (daily_request_count >= 0),
    last_request_date TIMESTAMPTZ,
    role VARCHAR DEFAULT 'agent',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create assignments table
CREATE TABLE IF NOT EXISTS assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    streamer_id VARCHAR NOT NULL REFERENCES streamers(twitch_user_id) ON DELETE CASCADE,
    status VARCHAR DEFAULT 'assigned',
    assigned_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    notes TEXT,
    UNIQUE(agent_id, streamer_id)
);

-- Create scraper_runs table
CREATE TABLE IF NOT EXISTS scraper_runs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    run_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR NOT NULL,
    streamers_found INTEGER DEFAULT 0 CHECK (streamers_found >= 0),
    details TEXT
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_streamers_twitch_user_id ON streamers(twitch_user_id);
CREATE INDEX IF NOT EXISTS idx_streamers_username ON streamers(username);
CREATE INDEX IF NOT EXISTS idx_streamers_follower_count ON streamers(follower_count);
CREATE INDEX IF NOT EXISTS idx_streamers_is_live ON streamers(is_live);
CREATE INDEX IF NOT EXISTS idx_streamers_language ON streamers(language);
CREATE INDEX IF NOT EXISTS idx_streamers_last_seen_live_at ON streamers(last_seen_live_at);
CREATE INDEX IF NOT EXISTS idx_streamers_created_at ON streamers(created_at);
CREATE INDEX IF NOT EXISTS idx_streamers_available ON streamers(is_live, language, follower_count);
CREATE INDEX IF NOT EXISTS idx_streamers_live_followers ON streamers(is_live, follower_count);
CREATE INDEX IF NOT EXISTS idx_streamers_search ON streamers(username, display_name);
CREATE INDEX IF NOT EXISTS idx_streamers_pagination ON streamers(created_at, id);
CREATE INDEX IF NOT EXISTS idx_streamers_follower_sort ON streamers(follower_count, id);

CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_last_request_date ON user_profiles(last_request_date);

CREATE INDEX IF NOT EXISTS idx_assignments_agent_id ON assignments(agent_id);
CREATE INDEX IF NOT EXISTS idx_assignments_streamer_id ON assignments(streamer_id);
CREATE INDEX IF NOT EXISTS idx_assignments_status ON assignments(status);
CREATE INDEX IF NOT EXISTS idx_assignments_assigned_at ON assignments(assigned_at);
CREATE INDEX IF NOT EXISTS idx_assignments_deleted_at ON assignments(deleted_at);
CREATE INDEX IF NOT EXISTS idx_assignments_recent ON assignments(assigned_at, streamer_id);
CREATE INDEX IF NOT EXISTS idx_assignments_status_assigned_at ON assignments(status, assigned_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_streamers_updated_at BEFORE UPDATE ON streamers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assignments_updated_at BEFORE UPDATE ON assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();