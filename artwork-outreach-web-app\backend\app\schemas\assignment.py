from __future__ import annotations
from datetime import datetime
from enum import Enum
from typing import List, Optional, TYPE_CHECKING
from uuid import UUID

from pydantic import BaseModel, Field, root_validator

if TYPE_CHECKING:
    from app.schemas.streamer import StreamerResponse


class AssignmentStatus(str, Enum):
    PENDING = "PENDING"
    INTERESTED = "INTERESTED"
    CONTACTED = "CONTACTED"
    REJECTED = "REJECTED"
    COMPLETED = "COMPLETED"


class AssignmentRequest(BaseModel):
    streamer_ids: List[int] = Field(
        ...,
        min_items=1,
        max_items=50,
        description="List of streamer Twitch IDs to assign.",
    )


class UpdateAssignmentRequest(BaseModel):
    status: Optional[AssignmentStatus] = None
    notes: Optional[str] = Field(
        None, max_length=500, description="Additional notes for the assignment."
    )


class AssignmentResponse(BaseModel):
    id: UUID
    agent_id: UUID
    streamer_twitch_id: Optional[str] = None
    status: AssignmentStatus
    assigned_at: datetime
    updated_at: datetime
    notes: Optional[str] = None
    streamer: "StreamerResponse"

    @root_validator(pre=False, skip_on_failure=True)
    def set_computed_fields(cls, values):
        if values.get("streamer"):
            values["streamer_twitch_id"] = values["streamer"].twitch_user_id
        return values

    model_config = {"from_attributes": True}


class AssignmentListResponse(BaseModel):
    assignments: List[AssignmentResponse]
    next_cursor: Optional[str] = None
    previous_cursor: Optional[str] = None


class AssignmentStatsResponse(BaseModel):
    assignments_by_status: dict[str, int]
    conversion_rate: float
    user_performance: dict[str, dict]

from app.schemas.streamer import StreamerResponse

AssignmentResponse.model_rebuild()