"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcz9kZmQ5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvcHJpbWl0aXZlL3NyYy9wcmltaXRpdmUudHN4XG5mdW5jdGlvbiBjb21wb3NlRXZlbnRIYW5kbGVycyhvcmlnaW5hbEV2ZW50SGFuZGxlciwgb3VyRXZlbnRIYW5kbGVyLCB7IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgfSA9IHt9KSB7XG4gIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgIG9yaWdpbmFsRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIGlmIChjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPT09IGZhbHNlIHx8ICFldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICByZXR1cm4gb3VyRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIH1cbiAgfTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VFdmVudEhhbmRsZXJzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-arrow/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/arrow.tsx\n\n\n\nvar NAME = \"Arrow\";\nvar Arrow = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWFycm93L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDdUI7QUFDZDtBQUN4QztBQUNBLFlBQVksNkNBQWdCO0FBQzVCLFVBQVUsa0RBQWtEO0FBQzVELHlCQUF5QixzREFBRztBQUM1QixJQUFJLGdFQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsc0RBQUcsY0FBYywwQkFBMEI7QUFDdEc7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBSUU7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1hcnJvdy9kaXN0L2luZGV4Lm1qcz9jOTYxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9hcnJvdy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1wcmltaXRpdmVcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIE5BTUUgPSBcIkFycm93XCI7XG52YXIgQXJyb3cgPSBSZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIGNvbnN0IHsgY2hpbGRyZW4sIHdpZHRoID0gMTAsIGhlaWdodCA9IDUsIC4uLmFycm93UHJvcHMgfSA9IHByb3BzO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICBQcmltaXRpdmUuc3ZnLFxuICAgIHtcbiAgICAgIC4uLmFycm93UHJvcHMsXG4gICAgICByZWY6IGZvcndhcmRlZFJlZixcbiAgICAgIHdpZHRoLFxuICAgICAgaGVpZ2h0LFxuICAgICAgdmlld0JveDogXCIwIDAgMzAgMTBcIixcbiAgICAgIHByZXNlcnZlQXNwZWN0UmF0aW86IFwibm9uZVwiLFxuICAgICAgY2hpbGRyZW46IHByb3BzLmFzQ2hpbGQgPyBjaGlsZHJlbiA6IC8qIEBfX1BVUkVfXyAqLyBqc3goXCJwb2x5Z29uXCIsIHsgcG9pbnRzOiBcIjAsMCAzMCwwIDE1LDEwXCIgfSlcbiAgICB9XG4gICk7XG59KTtcbkFycm93LmRpc3BsYXlOYW1lID0gTkFNRTtcbnZhciBSb290ID0gQXJyb3c7XG5leHBvcnQge1xuICBBcnJvdyxcbiAgUm9vdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ // src/dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: DIALOG_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n});\nDialogOverlay.displayName = OVERLAY_NAME;\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_10__.createSlot)(\"DialogOverlay.RemoveScroll\");\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        as: Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n});\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            event.preventDefault();\n            context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n});\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n});\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId })=>{\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (titleId) {\n            const hasTitle = document.getElementById(titleId);\n            if (!hasTitle) console.error(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId })=>{\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n        if (descriptionId && describedById) {\n            const hasDescription = document.getElementById(descriptionId);\n            if (!hasDescription) console.warn(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpc21pc3NhYmxlLWxheWVyL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUF1QjtBQUNjO0FBQ2tCO0FBQ3ZCO0FBQ0Q7QUFDRTtBQXFKM0I7QUEvSU4sSUFBTVEseUJBQXlCO0FBQy9CLElBQU1DLGlCQUFpQjtBQUN2QixJQUFNQyx1QkFBdUI7QUFDN0IsSUFBTUMsZ0JBQWdCO0FBRXRCLElBQUlDO0FBRUosSUFBTUMsd0NBQWdDYixnREFBQSxDQUFjO0lBQ2xEZSxRQUFRLG9CQUFJQztJQUNaQyx3Q0FBd0Msb0JBQUlEO0lBQzVDRSxVQUFVLG9CQUFJRjtBQUNoQjtBQXNDQSxJQUFNRyxpQ0FBeUJuQiw2Q0FBQSxDQUM3QixDQUFDcUIsT0FBT0M7SUFDTixNQUFNLEVBQ0pDLDhCQUE4QixPQUM5QkMsZUFBQSxFQUNBQyxvQkFBQSxFQUNBQyxjQUFBLEVBQ0FDLGlCQUFBLEVBQ0FDLFNBQUEsRUFDQSxHQUFHQyxZQUNMLEdBQUlSO0lBQ0osTUFBTVMsVUFBZ0I5Qiw2Q0FBQSxDQUFXYTtJQUNqQyxNQUFNLENBQUNtQixNQUFNQyxRQUFPLEdBQVVqQywyQ0FBQSxDQUF5QztJQUN2RSxNQUFNbUMsZ0JBQWdCSCxNQUFNRyxpQkFBaUJDLFlBQVlDO0lBQ3pELE1BQU0sR0FBR0MsTUFBSyxHQUFVdEMsMkNBQUEsQ0FBUyxDQUFDO0lBQ2xDLE1BQU11QyxlQUFlbkMsNkVBQWVBLENBQUNrQixjQUFjLENBQUNVLFFBQVNDLFFBQVFEO0lBQ3JFLE1BQU1qQixTQUFTeUIsTUFBTUMsSUFBQSxDQUFLWCxRQUFRZixNQUFNO0lBQ3hDLE1BQU0sQ0FBQzJCLDZDQUE0QyxHQUFJO1dBQUlaLFFBQVFiLHNDQUFzQztLQUFBLENBQUUwQixLQUFBLENBQU07SUFDakgsTUFBTUMsb0RBQW9EN0IsT0FBTzhCLE9BQUEsQ0FBUUg7SUFDekUsTUFBTUksUUFBUWQsT0FBT2pCLE9BQU84QixPQUFBLENBQVFiLFFBQVE7SUFDNUMsTUFBTWUsOEJBQThCakIsUUFBUWIsc0NBQUEsQ0FBdUMrQixJQUFBLEdBQU87SUFDMUYsTUFBTUMseUJBQXlCSCxTQUFTRjtJQUV4QyxNQUFNTSxxQkFBcUJDLHNCQUFzQixDQUFDQztRQUNoRCxNQUFNQyxTQUFTRCxNQUFNQyxNQUFBO1FBQ3JCLE1BQU1DLHdCQUF3QjtlQUFJeEIsUUFBUVosUUFBUTtTQUFBLENBQUVxQyxJQUFBLENBQUssQ0FBQ0MsU0FBV0EsT0FBT0MsUUFBQSxDQUFTSjtRQUNyRixJQUFJLENBQUNKLDBCQUEwQkssdUJBQXVCO1FBQ3REN0IsdUJBQXVCMkI7UUFDdkJ6QixvQkFBb0J5QjtRQUNwQixJQUFJLENBQUNBLE1BQU1NLGdCQUFBLEVBQWtCOUI7SUFDL0IsR0FBR087SUFFSCxNQUFNd0IsZUFBZUMsZ0JBQWdCLENBQUNSO1FBQ3BDLE1BQU1DLFNBQVNELE1BQU1DLE1BQUE7UUFDckIsTUFBTVEsa0JBQWtCO2VBQUkvQixRQUFRWixRQUFRO1NBQUEsQ0FBRXFDLElBQUEsQ0FBSyxDQUFDQyxTQUFXQSxPQUFPQyxRQUFBLENBQVNKO1FBQy9FLElBQUlRLGlCQUFpQjtRQUNyQm5DLGlCQUFpQjBCO1FBQ2pCekIsb0JBQW9CeUI7UUFDcEIsSUFBSSxDQUFDQSxNQUFNTSxnQkFBQSxFQUFrQjlCO0lBQy9CLEdBQUdPO0lBRUg3QixvRkFBZ0JBLENBQUMsQ0FBQzhDO1FBQ2hCLE1BQU1VLGlCQUFpQmhCLFVBQVVoQixRQUFRZixNQUFBLENBQU9pQyxJQUFBLEdBQU87UUFDdkQsSUFBSSxDQUFDYyxnQkFBZ0I7UUFDckJ0QyxrQkFBa0I0QjtRQUNsQixJQUFJLENBQUNBLE1BQU1NLGdCQUFBLElBQW9COUIsV0FBVztZQUN4Q3dCLE1BQU1XLGNBQUE7WUFDTm5DO1FBQ0Y7SUFDRixHQUFHTztJQUVHbkMsNENBQUEsQ0FBVTtRQUNkLElBQUksQ0FBQ2dDLE1BQU07UUFDWCxJQUFJVCw2QkFBNkI7WUFDL0IsSUFBSU8sUUFBUWIsc0NBQUEsQ0FBdUMrQixJQUFBLEtBQVMsR0FBRztnQkFDN0RwQyw0QkFBNEJ1QixjQUFjOEIsSUFBQSxDQUFLQyxLQUFBLENBQU1DLGFBQUE7Z0JBQ3JEaEMsY0FBYzhCLElBQUEsQ0FBS0MsS0FBQSxDQUFNQyxhQUFBLEdBQWdCO1lBQzNDO1lBQ0FyQyxRQUFRYixzQ0FBQSxDQUF1Q21ELEdBQUEsQ0FBSXBDO1FBQ3JEO1FBQ0FGLFFBQVFmLE1BQUEsQ0FBT3FELEdBQUEsQ0FBSXBDO1FBQ25CcUM7UUFDQSxPQUFPO1lBQ0wsSUFDRTlDLCtCQUNBTyxRQUFRYixzQ0FBQSxDQUF1QytCLElBQUEsS0FBUyxHQUN4RDtnQkFDQWIsY0FBYzhCLElBQUEsQ0FBS0MsS0FBQSxDQUFNQyxhQUFBLEdBQWdCdkQ7WUFDM0M7UUFDRjtJQUNGLEdBQUc7UUFBQ29CO1FBQU1HO1FBQWVaO1FBQTZCTztLQUFRO0lBUXhEOUIsNENBQUEsQ0FBVTtRQUNkLE9BQU87WUFDTCxJQUFJLENBQUNnQyxNQUFNO1lBQ1hGLFFBQVFmLE1BQUEsQ0FBT3VELE1BQUEsQ0FBT3RDO1lBQ3RCRixRQUFRYixzQ0FBQSxDQUF1Q3FELE1BQUEsQ0FBT3RDO1lBQ3REcUM7UUFDRjtJQUNGLEdBQUc7UUFBQ3JDO1FBQU1GO0tBQVE7SUFFWjlCLDRDQUFBLENBQVU7UUFDZCxNQUFNdUUsZUFBZSxJQUFNakMsTUFBTSxDQUFDO1FBQ2xDRCxTQUFTbUMsZ0JBQUEsQ0FBaUIvRCxnQkFBZ0I4RDtRQUMxQyxPQUFPLElBQU1sQyxTQUFTb0MsbUJBQUEsQ0FBb0JoRSxnQkFBZ0I4RDtJQUM1RCxHQUFHLEVBQUU7SUFFTCxPQUNFLGdCQUFBaEUsc0RBQUFBLENBQUNMLGdFQUFTQSxDQUFDd0UsR0FBQSxFQUFWO1FBQ0UsR0FBRzdDLFVBQUE7UUFDSjhDLEtBQUtwQztRQUNMMkIsT0FBTztZQUNMQyxlQUFlcEIsOEJBQ1hFLHlCQUNFLFNBQ0EsU0FDRjtZQUNKLEdBQUc1QixNQUFNNkMsS0FBQTtRQUNYO1FBQ0FVLGdCQUFnQjNFLHlFQUFvQkEsQ0FBQ29CLE1BQU11RCxjQUFBLEVBQWdCakIsYUFBYWlCLGNBQWM7UUFDdEZDLGVBQWU1RSx5RUFBb0JBLENBQUNvQixNQUFNd0QsYUFBQSxFQUFlbEIsYUFBYWtCLGFBQWE7UUFDbkZDLHNCQUFzQjdFLHlFQUFvQkEsQ0FDeENvQixNQUFNeUQsb0JBQUEsRUFDTjVCLG1CQUFtQjRCLG9CQUFBO0lBQ3JCO0FBR047QUFHRjNELGlCQUFpQjRELFdBQUEsR0FBY3ZFO0FBTS9CLElBQU13RSxjQUFjO0FBS3BCLElBQU1DLHVDQUErQmpGLDZDQUFBLENBR25DLENBQUNxQixPQUFPQztJQUNSLE1BQU1RLFVBQWdCOUIsNkNBQUEsQ0FBV2E7SUFDakMsTUFBTThELE1BQVkzRSx5Q0FBQSxDQUFzQztJQUN4RCxNQUFNdUMsZUFBZW5DLDZFQUFlQSxDQUFDa0IsY0FBY3FEO0lBRTdDM0UsNENBQUEsQ0FBVTtRQUNkLE1BQU1nQyxPQUFPMkMsSUFBSVEsT0FBQTtRQUNqQixJQUFJbkQsTUFBTTtZQUNSRixRQUFRWixRQUFBLENBQVNrRCxHQUFBLENBQUlwQztZQUNyQixPQUFPO2dCQUNMRixRQUFRWixRQUFBLENBQVNvRCxNQUFBLENBQU90QztZQUMxQjtRQUNGO0lBQ0YsR0FBRztRQUFDRixRQUFRWixRQUFRO0tBQUM7SUFFckIsT0FBTyxnQkFBQVgsc0RBQUFBLENBQUNMLGdFQUFTQSxDQUFDd0UsR0FBQSxFQUFWO1FBQWUsR0FBR3JELEtBQUE7UUFBT3NELEtBQUtwQztJQUFBO0FBQ3hDO0FBRUEwQyx1QkFBdUJGLFdBQUEsR0FBY0M7QUFZckMsU0FBUzdCLHNCQUNQMUIsb0JBQUEsRUFDQVUsZ0JBQTBCQyxZQUFZQyxRQUFBO0lBRXRDLE1BQU0rQywyQkFBMkIvRSxnRkFBY0EsQ0FBQ29CO0lBQ2hELE1BQU00RCw4QkFBb0NyRix5Q0FBQSxDQUFPO0lBQ2pELE1BQU1zRixpQkFBdUJ0Rix5Q0FBQSxDQUFPLEtBQU87SUFFckNBLDRDQUFBLENBQVU7UUFDZCxNQUFNdUYsb0JBQW9CLENBQUNuQztZQUN6QixJQUFJQSxNQUFNQyxNQUFBLElBQVUsQ0FBQ2dDLDRCQUE0QkYsT0FBQSxFQUFTO2dCQUd4RCxJQUFTSyw0Q0FBVDtvQkFDRUMsNkJBQ0UvRSxzQkFDQTBFLDBCQUNBTSxhQUNBO3dCQUFFQyxVQUFVO29CQUFLO2dCQUVyQjtnQkFQUyxJQUFBSCwyQ0FBQUE7Z0JBRlQsTUFBTUUsY0FBYztvQkFBRUUsZUFBZXhDO2dCQUFNO2dCQXVCM0MsSUFBSUEsTUFBTXlDLFdBQUEsS0FBZ0IsU0FBUztvQkFDakMxRCxjQUFjc0MsbUJBQUEsQ0FBb0IsU0FBU2EsZUFBZUgsT0FBTztvQkFDakVHLGVBQWVILE9BQUEsR0FBVUs7b0JBQ3pCckQsY0FBY3FDLGdCQUFBLENBQWlCLFNBQVNjLGVBQWVILE9BQUEsRUFBUzt3QkFBRVcsTUFBTTtvQkFBSztnQkFDL0UsT0FBTztvQkFDTE47Z0JBQ0Y7WUFDRixPQUFPO2dCQUdMckQsY0FBY3NDLG1CQUFBLENBQW9CLFNBQVNhLGVBQWVILE9BQU87WUFDbkU7WUFDQUUsNEJBQTRCRixPQUFBLEdBQVU7UUFDeEM7UUFjQSxNQUFNWSxVQUFVQyxPQUFPQyxVQUFBLENBQVc7WUFDaEM5RCxjQUFjcUMsZ0JBQUEsQ0FBaUIsZUFBZWU7UUFDaEQsR0FBRztRQUNILE9BQU87WUFDTFMsT0FBT0UsWUFBQSxDQUFhSDtZQUNwQjVELGNBQWNzQyxtQkFBQSxDQUFvQixlQUFlYztZQUNqRHBELGNBQWNzQyxtQkFBQSxDQUFvQixTQUFTYSxlQUFlSCxPQUFPO1FBQ25FO0lBQ0YsR0FBRztRQUFDaEQ7UUFBZWlEO0tBQXlCO0lBRTVDLE9BQU87UUFBQTtRQUVMTixzQkFBc0IsSUFBT08sNEJBQTRCRixPQUFBLEdBQVU7SUFDckU7QUFDRjtBQU1BLFNBQVN2QixnQkFDUGxDLGNBQUEsRUFDQVMsZ0JBQTBCQyxZQUFZQyxRQUFBO0lBRXRDLE1BQU04RCxxQkFBcUI5RixnRkFBY0EsQ0FBQ3FCO0lBQzFDLE1BQU0wRSw0QkFBa0NwRyx5Q0FBQSxDQUFPO0lBRXpDQSw0Q0FBQSxDQUFVO1FBQ2QsTUFBTXFHLGNBQWMsQ0FBQ2pEO1lBQ25CLElBQUlBLE1BQU1DLE1BQUEsSUFBVSxDQUFDK0MsMEJBQTBCakIsT0FBQSxFQUFTO2dCQUN0RCxNQUFNTyxjQUFjO29CQUFFRSxlQUFleEM7Z0JBQU07Z0JBQzNDcUMsNkJBQTZCOUUsZUFBZXdGLG9CQUFvQlQsYUFBYTtvQkFDM0VDLFVBQVU7Z0JBQ1o7WUFDRjtRQUNGO1FBQ0F4RCxjQUFjcUMsZ0JBQUEsQ0FBaUIsV0FBVzZCO1FBQzFDLE9BQU8sSUFBTWxFLGNBQWNzQyxtQkFBQSxDQUFvQixXQUFXNEI7SUFDNUQsR0FBRztRQUFDbEU7UUFBZWdFO0tBQW1CO0lBRXRDLE9BQU87UUFDTHZCLGdCQUFnQixJQUFPd0IsMEJBQTBCakIsT0FBQSxHQUFVO1FBQzNETixlQUFlLElBQU91QiwwQkFBMEJqQixPQUFBLEdBQVU7SUFDNUQ7QUFDRjtBQUVBLFNBQVNkO0lBQ1AsTUFBTWpCLFFBQVEsSUFBSWtELFlBQVk3RjtJQUM5QjRCLFNBQVNrRSxhQUFBLENBQWNuRDtBQUN6QjtBQUVBLFNBQVNxQyw2QkFDUGUsSUFBQSxFQUNBQyxPQUFBLEVBQ0FDLE1BQUEsRUFDQSxFQUFFZixRQUFBLEVBQVM7SUFFWCxNQUFNdEMsU0FBU3FELE9BQU9kLGFBQUEsQ0FBY3ZDLE1BQUE7SUFDcEMsTUFBTUQsUUFBUSxJQUFJa0QsWUFBWUUsTUFBTTtRQUFFRyxTQUFTO1FBQU9DLFlBQVk7UUFBTUY7SUFBTztJQUMvRSxJQUFJRCxTQUFTcEQsT0FBT21CLGdCQUFBLENBQWlCZ0MsTUFBTUMsU0FBMEI7UUFBRVgsTUFBTTtJQUFLO0lBRWxGLElBQUlILFVBQVU7UUFDWnhGLHNGQUEyQkEsQ0FBQ2tELFFBQVFEO0lBQ3RDLE9BQU87UUFDTEMsT0FBT2tELGFBQUEsQ0FBY25EO0lBQ3ZCO0FBQ0Y7QUFFQSxJQUFNeUQsT0FBTzFGO0FBQ2IsSUFBTTJGLFNBQVM3QiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4uL3NyYy9kaXNtaXNzYWJsZS1sYXllci50c3g/ZDM1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjb21wb3NlRXZlbnRIYW5kbGVycyB9IGZyb20gJ0ByYWRpeC11aS9wcmltaXRpdmUnO1xuaW1wb3J0IHsgUHJpbWl0aXZlLCBkaXNwYXRjaERpc2NyZXRlQ3VzdG9tRXZlbnQgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlJztcbmltcG9ydCB7IHVzZUNvbXBvc2VkUmVmcyB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb21wb3NlLXJlZnMnO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZic7XG5pbXBvcnQgeyB1c2VFc2NhcGVLZXlkb3duIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bic7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIERpc21pc3NhYmxlTGF5ZXJcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgRElTTUlTU0FCTEVfTEFZRVJfTkFNRSA9ICdEaXNtaXNzYWJsZUxheWVyJztcbmNvbnN0IENPTlRFWFRfVVBEQVRFID0gJ2Rpc21pc3NhYmxlTGF5ZXIudXBkYXRlJztcbmNvbnN0IFBPSU5URVJfRE9XTl9PVVRTSURFID0gJ2Rpc21pc3NhYmxlTGF5ZXIucG9pbnRlckRvd25PdXRzaWRlJztcbmNvbnN0IEZPQ1VTX09VVFNJREUgPSAnZGlzbWlzc2FibGVMYXllci5mb2N1c091dHNpZGUnO1xuXG5sZXQgb3JpZ2luYWxCb2R5UG9pbnRlckV2ZW50czogc3RyaW5nO1xuXG5jb25zdCBEaXNtaXNzYWJsZUxheWVyQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoe1xuICBsYXllcnM6IG5ldyBTZXQ8RGlzbWlzc2FibGVMYXllckVsZW1lbnQ+KCksXG4gIGxheWVyc1dpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkOiBuZXcgU2V0PERpc21pc3NhYmxlTGF5ZXJFbGVtZW50PigpLFxuICBicmFuY2hlczogbmV3IFNldDxEaXNtaXNzYWJsZUxheWVyQnJhbmNoRWxlbWVudD4oKSxcbn0pO1xuXG50eXBlIERpc21pc3NhYmxlTGF5ZXJFbGVtZW50ID0gUmVhY3QuQ29tcG9uZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbnR5cGUgUHJpbWl0aXZlRGl2UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIERpc21pc3NhYmxlTGF5ZXJQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHtcbiAgLyoqXG4gICAqIFdoZW4gYHRydWVgLCBob3Zlci9mb2N1cy9jbGljayBpbnRlcmFjdGlvbnMgd2lsbCBiZSBkaXNhYmxlZCBvbiBlbGVtZW50cyBvdXRzaWRlXG4gICAqIHRoZSBgRGlzbWlzc2FibGVMYXllcmAuIFVzZXJzIHdpbGwgbmVlZCB0byBjbGljayB0d2ljZSBvbiBvdXRzaWRlIGVsZW1lbnRzIHRvXG4gICAqIGludGVyYWN0IHdpdGggdGhlbTogb25jZSB0byBjbG9zZSB0aGUgYERpc21pc3NhYmxlTGF5ZXJgLCBhbmQgYWdhaW4gdG8gdHJpZ2dlciB0aGUgZWxlbWVudC5cbiAgICovXG4gIGRpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50cz86IGJvb2xlYW47XG4gIC8qKlxuICAgKiBFdmVudCBoYW5kbGVyIGNhbGxlZCB3aGVuIHRoZSBlc2NhcGUga2V5IGlzIGRvd24uXG4gICAqIENhbiBiZSBwcmV2ZW50ZWQuXG4gICAqL1xuICBvbkVzY2FwZUtleURvd24/OiAoZXZlbnQ6IEtleWJvYXJkRXZlbnQpID0+IHZvaWQ7XG4gIC8qKlxuICAgKiBFdmVudCBoYW5kbGVyIGNhbGxlZCB3aGVuIHRoZSBhIGBwb2ludGVyZG93bmAgZXZlbnQgaGFwcGVucyBvdXRzaWRlIG9mIHRoZSBgRGlzbWlzc2FibGVMYXllcmAuXG4gICAqIENhbiBiZSBwcmV2ZW50ZWQuXG4gICAqL1xuICBvblBvaW50ZXJEb3duT3V0c2lkZT86IChldmVudDogUG9pbnRlckRvd25PdXRzaWRlRXZlbnQpID0+IHZvaWQ7XG4gIC8qKlxuICAgKiBFdmVudCBoYW5kbGVyIGNhbGxlZCB3aGVuIHRoZSBmb2N1cyBtb3ZlcyBvdXRzaWRlIG9mIHRoZSBgRGlzbWlzc2FibGVMYXllcmAuXG4gICAqIENhbiBiZSBwcmV2ZW50ZWQuXG4gICAqL1xuICBvbkZvY3VzT3V0c2lkZT86IChldmVudDogRm9jdXNPdXRzaWRlRXZlbnQpID0+IHZvaWQ7XG4gIC8qKlxuICAgKiBFdmVudCBoYW5kbGVyIGNhbGxlZCB3aGVuIGFuIGludGVyYWN0aW9uIGhhcHBlbnMgb3V0c2lkZSB0aGUgYERpc21pc3NhYmxlTGF5ZXJgLlxuICAgKiBTcGVjaWZpY2FsbHksIHdoZW4gYSBgcG9pbnRlcmRvd25gIGV2ZW50IGhhcHBlbnMgb3V0c2lkZSBvciBmb2N1cyBtb3ZlcyBvdXRzaWRlIG9mIGl0LlxuICAgKiBDYW4gYmUgcHJldmVudGVkLlxuICAgKi9cbiAgb25JbnRlcmFjdE91dHNpZGU/OiAoZXZlbnQ6IFBvaW50ZXJEb3duT3V0c2lkZUV2ZW50IHwgRm9jdXNPdXRzaWRlRXZlbnQpID0+IHZvaWQ7XG4gIC8qKlxuICAgKiBIYW5kbGVyIGNhbGxlZCB3aGVuIHRoZSBgRGlzbWlzc2FibGVMYXllcmAgc2hvdWxkIGJlIGRpc21pc3NlZFxuICAgKi9cbiAgb25EaXNtaXNzPzogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgRGlzbWlzc2FibGVMYXllciA9IFJlYWN0LmZvcndhcmRSZWY8RGlzbWlzc2FibGVMYXllckVsZW1lbnQsIERpc21pc3NhYmxlTGF5ZXJQcm9wcz4oXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzID0gZmFsc2UsXG4gICAgICBvbkVzY2FwZUtleURvd24sXG4gICAgICBvblBvaW50ZXJEb3duT3V0c2lkZSxcbiAgICAgIG9uRm9jdXNPdXRzaWRlLFxuICAgICAgb25JbnRlcmFjdE91dHNpZGUsXG4gICAgICBvbkRpc21pc3MsXG4gICAgICAuLi5sYXllclByb3BzXG4gICAgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KERpc21pc3NhYmxlTGF5ZXJDb250ZXh0KTtcbiAgICBjb25zdCBbbm9kZSwgc2V0Tm9kZV0gPSBSZWFjdC51c2VTdGF0ZTxEaXNtaXNzYWJsZUxheWVyRWxlbWVudCB8IG51bGw+KG51bGwpO1xuICAgIGNvbnN0IG93bmVyRG9jdW1lbnQgPSBub2RlPy5vd25lckRvY3VtZW50ID8/IGdsb2JhbFRoaXM/LmRvY3VtZW50O1xuICAgIGNvbnN0IFssIGZvcmNlXSA9IFJlYWN0LnVzZVN0YXRlKHt9KTtcbiAgICBjb25zdCBjb21wb3NlZFJlZnMgPSB1c2VDb21wb3NlZFJlZnMoZm9yd2FyZGVkUmVmLCAobm9kZSkgPT4gc2V0Tm9kZShub2RlKSk7XG4gICAgY29uc3QgbGF5ZXJzID0gQXJyYXkuZnJvbShjb250ZXh0LmxheWVycyk7XG4gICAgY29uc3QgW2hpZ2hlc3RMYXllcldpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkXSA9IFsuLi5jb250ZXh0LmxheWVyc1dpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkXS5zbGljZSgtMSk7IC8vIHByZXR0aWVyLWlnbm9yZVxuICAgIGNvbnN0IGhpZ2hlc3RMYXllcldpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkSW5kZXggPSBsYXllcnMuaW5kZXhPZihoaWdoZXN0TGF5ZXJXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZCEpOyAvLyBwcmV0dGllci1pZ25vcmVcbiAgICBjb25zdCBpbmRleCA9IG5vZGUgPyBsYXllcnMuaW5kZXhPZihub2RlKSA6IC0xO1xuICAgIGNvbnN0IGlzQm9keVBvaW50ZXJFdmVudHNEaXNhYmxlZCA9IGNvbnRleHQubGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWQuc2l6ZSA+IDA7XG4gICAgY29uc3QgaXNQb2ludGVyRXZlbnRzRW5hYmxlZCA9IGluZGV4ID49IGhpZ2hlc3RMYXllcldpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkSW5kZXg7XG5cbiAgICBjb25zdCBwb2ludGVyRG93bk91dHNpZGUgPSB1c2VQb2ludGVyRG93bk91dHNpZGUoKGV2ZW50KSA9PiB7XG4gICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQgYXMgSFRNTEVsZW1lbnQ7XG4gICAgICBjb25zdCBpc1BvaW50ZXJEb3duT25CcmFuY2ggPSBbLi4uY29udGV4dC5icmFuY2hlc10uc29tZSgoYnJhbmNoKSA9PiBicmFuY2guY29udGFpbnModGFyZ2V0KSk7XG4gICAgICBpZiAoIWlzUG9pbnRlckV2ZW50c0VuYWJsZWQgfHwgaXNQb2ludGVyRG93bk9uQnJhbmNoKSByZXR1cm47XG4gICAgICBvblBvaW50ZXJEb3duT3V0c2lkZT8uKGV2ZW50KTtcbiAgICAgIG9uSW50ZXJhY3RPdXRzaWRlPy4oZXZlbnQpO1xuICAgICAgaWYgKCFldmVudC5kZWZhdWx0UHJldmVudGVkKSBvbkRpc21pc3M/LigpO1xuICAgIH0sIG93bmVyRG9jdW1lbnQpO1xuXG4gICAgY29uc3QgZm9jdXNPdXRzaWRlID0gdXNlRm9jdXNPdXRzaWRlKChldmVudCkgPT4ge1xuICAgICAgY29uc3QgdGFyZ2V0ID0gZXZlbnQudGFyZ2V0IGFzIEhUTUxFbGVtZW50O1xuICAgICAgY29uc3QgaXNGb2N1c0luQnJhbmNoID0gWy4uLmNvbnRleHQuYnJhbmNoZXNdLnNvbWUoKGJyYW5jaCkgPT4gYnJhbmNoLmNvbnRhaW5zKHRhcmdldCkpO1xuICAgICAgaWYgKGlzRm9jdXNJbkJyYW5jaCkgcmV0dXJuO1xuICAgICAgb25Gb2N1c091dHNpZGU/LihldmVudCk7XG4gICAgICBvbkludGVyYWN0T3V0c2lkZT8uKGV2ZW50KTtcbiAgICAgIGlmICghZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkgb25EaXNtaXNzPy4oKTtcbiAgICB9LCBvd25lckRvY3VtZW50KTtcblxuICAgIHVzZUVzY2FwZUtleWRvd24oKGV2ZW50KSA9PiB7XG4gICAgICBjb25zdCBpc0hpZ2hlc3RMYXllciA9IGluZGV4ID09PSBjb250ZXh0LmxheWVycy5zaXplIC0gMTtcbiAgICAgIGlmICghaXNIaWdoZXN0TGF5ZXIpIHJldHVybjtcbiAgICAgIG9uRXNjYXBlS2V5RG93bj8uKGV2ZW50KTtcbiAgICAgIGlmICghZXZlbnQuZGVmYXVsdFByZXZlbnRlZCAmJiBvbkRpc21pc3MpIHtcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgb25EaXNtaXNzKCk7XG4gICAgICB9XG4gICAgfSwgb3duZXJEb2N1bWVudCk7XG5cbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgaWYgKCFub2RlKSByZXR1cm47XG4gICAgICBpZiAoZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzKSB7XG4gICAgICAgIGlmIChjb250ZXh0LmxheWVyc1dpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkLnNpemUgPT09IDApIHtcbiAgICAgICAgICBvcmlnaW5hbEJvZHlQb2ludGVyRXZlbnRzID0gb3duZXJEb2N1bWVudC5ib2R5LnN0eWxlLnBvaW50ZXJFdmVudHM7XG4gICAgICAgICAgb3duZXJEb2N1bWVudC5ib2R5LnN0eWxlLnBvaW50ZXJFdmVudHMgPSAnbm9uZSc7XG4gICAgICAgIH1cbiAgICAgICAgY29udGV4dC5sYXllcnNXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZC5hZGQobm9kZSk7XG4gICAgICB9XG4gICAgICBjb250ZXh0LmxheWVycy5hZGQobm9kZSk7XG4gICAgICBkaXNwYXRjaFVwZGF0ZSgpO1xuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgaWYgKFxuICAgICAgICAgIGRpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50cyAmJlxuICAgICAgICAgIGNvbnRleHQubGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWQuc2l6ZSA9PT0gMVxuICAgICAgICApIHtcbiAgICAgICAgICBvd25lckRvY3VtZW50LmJvZHkuc3R5bGUucG9pbnRlckV2ZW50cyA9IG9yaWdpbmFsQm9keVBvaW50ZXJFdmVudHM7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfSwgW25vZGUsIG93bmVyRG9jdW1lbnQsIGRpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50cywgY29udGV4dF0pO1xuXG4gICAgLyoqXG4gICAgICogV2UgcHVycG9zZWZ1bGx5IHByZXZlbnQgY29tYmluaW5nIHRoaXMgZWZmZWN0IHdpdGggdGhlIGBkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHNgIGVmZmVjdFxuICAgICAqIGJlY2F1c2UgYSBjaGFuZ2UgdG8gYGRpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50c2Agd291bGQgcmVtb3ZlIHRoaXMgbGF5ZXIgZnJvbSB0aGUgc3RhY2tcbiAgICAgKiBhbmQgYWRkIGl0IHRvIHRoZSBlbmQgYWdhaW4gc28gdGhlIGxheWVyaW5nIG9yZGVyIHdvdWxkbid0IGJlIF9jcmVhdGlvbiBvcmRlcl8uXG4gICAgICogV2Ugb25seSB3YW50IHRoZW0gdG8gYmUgcmVtb3ZlZCBmcm9tIGNvbnRleHQgc3RhY2tzIHdoZW4gdW5tb3VudGVkLlxuICAgICAqL1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBpZiAoIW5vZGUpIHJldHVybjtcbiAgICAgICAgY29udGV4dC5sYXllcnMuZGVsZXRlKG5vZGUpO1xuICAgICAgICBjb250ZXh0LmxheWVyc1dpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkLmRlbGV0ZShub2RlKTtcbiAgICAgICAgZGlzcGF0Y2hVcGRhdGUoKTtcbiAgICAgIH07XG4gICAgfSwgW25vZGUsIGNvbnRleHRdKTtcblxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBjb25zdCBoYW5kbGVVcGRhdGUgPSAoKSA9PiBmb3JjZSh7fSk7XG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKENPTlRFWFRfVVBEQVRFLCBoYW5kbGVVcGRhdGUpO1xuICAgICAgcmV0dXJuICgpID0+IGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoQ09OVEVYVF9VUERBVEUsIGhhbmRsZVVwZGF0ZSk7XG4gICAgfSwgW10pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxQcmltaXRpdmUuZGl2XG4gICAgICAgIHsuLi5sYXllclByb3BzfVxuICAgICAgICByZWY9e2NvbXBvc2VkUmVmc31cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICBwb2ludGVyRXZlbnRzOiBpc0JvZHlQb2ludGVyRXZlbnRzRGlzYWJsZWRcbiAgICAgICAgICAgID8gaXNQb2ludGVyRXZlbnRzRW5hYmxlZFxuICAgICAgICAgICAgICA/ICdhdXRvJ1xuICAgICAgICAgICAgICA6ICdub25lJ1xuICAgICAgICAgICAgOiB1bmRlZmluZWQsXG4gICAgICAgICAgLi4ucHJvcHMuc3R5bGUsXG4gICAgICAgIH19XG4gICAgICAgIG9uRm9jdXNDYXB0dXJlPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbkZvY3VzQ2FwdHVyZSwgZm9jdXNPdXRzaWRlLm9uRm9jdXNDYXB0dXJlKX1cbiAgICAgICAgb25CbHVyQ2FwdHVyZT17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25CbHVyQ2FwdHVyZSwgZm9jdXNPdXRzaWRlLm9uQmx1ckNhcHR1cmUpfVxuICAgICAgICBvblBvaW50ZXJEb3duQ2FwdHVyZT17Y29tcG9zZUV2ZW50SGFuZGxlcnMoXG4gICAgICAgICAgcHJvcHMub25Qb2ludGVyRG93bkNhcHR1cmUsXG4gICAgICAgICAgcG9pbnRlckRvd25PdXRzaWRlLm9uUG9pbnRlckRvd25DYXB0dXJlXG4gICAgICAgICl9XG4gICAgICAvPlxuICAgICk7XG4gIH1cbik7XG5cbkRpc21pc3NhYmxlTGF5ZXIuZGlzcGxheU5hbWUgPSBESVNNSVNTQUJMRV9MQVlFUl9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBEaXNtaXNzYWJsZUxheWVyQnJhbmNoXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEJSQU5DSF9OQU1FID0gJ0Rpc21pc3NhYmxlTGF5ZXJCcmFuY2gnO1xuXG50eXBlIERpc21pc3NhYmxlTGF5ZXJCcmFuY2hFbGVtZW50ID0gUmVhY3QuQ29tcG9uZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbmludGVyZmFjZSBEaXNtaXNzYWJsZUxheWVyQnJhbmNoUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVEaXZQcm9wcyB7fVxuXG5jb25zdCBEaXNtaXNzYWJsZUxheWVyQnJhbmNoID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgRGlzbWlzc2FibGVMYXllckJyYW5jaEVsZW1lbnQsXG4gIERpc21pc3NhYmxlTGF5ZXJCcmFuY2hQcm9wc1xuPigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gUmVhY3QudXNlQ29udGV4dChEaXNtaXNzYWJsZUxheWVyQ29udGV4dCk7XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZjxEaXNtaXNzYWJsZUxheWVyQnJhbmNoRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIHJlZik7XG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBub2RlID0gcmVmLmN1cnJlbnQ7XG4gICAgaWYgKG5vZGUpIHtcbiAgICAgIGNvbnRleHQuYnJhbmNoZXMuYWRkKG5vZGUpO1xuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgY29udGV4dC5icmFuY2hlcy5kZWxldGUobm9kZSk7XG4gICAgICB9O1xuICAgIH1cbiAgfSwgW2NvbnRleHQuYnJhbmNoZXNdKTtcblxuICByZXR1cm4gPFByaW1pdGl2ZS5kaXYgey4uLnByb3BzfSByZWY9e2NvbXBvc2VkUmVmc30gLz47XG59KTtcblxuRGlzbWlzc2FibGVMYXllckJyYW5jaC5kaXNwbGF5TmFtZSA9IEJSQU5DSF9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbnR5cGUgUG9pbnRlckRvd25PdXRzaWRlRXZlbnQgPSBDdXN0b21FdmVudDx7IG9yaWdpbmFsRXZlbnQ6IFBvaW50ZXJFdmVudCB9PjtcbnR5cGUgRm9jdXNPdXRzaWRlRXZlbnQgPSBDdXN0b21FdmVudDx7IG9yaWdpbmFsRXZlbnQ6IEZvY3VzRXZlbnQgfT47XG5cbi8qKlxuICogTGlzdGVucyBmb3IgYHBvaW50ZXJkb3duYCBvdXRzaWRlIGEgcmVhY3Qgc3VidHJlZS4gV2UgdXNlIGBwb2ludGVyZG93bmAgcmF0aGVyIHRoYW4gYHBvaW50ZXJ1cGBcbiAqIHRvIG1pbWljIGxheWVyIGRpc21pc3NpbmcgYmVoYXZpb3VyIHByZXNlbnQgaW4gT1MuXG4gKiBSZXR1cm5zIHByb3BzIHRvIHBhc3MgdG8gdGhlIG5vZGUgd2Ugd2FudCB0byBjaGVjayBmb3Igb3V0c2lkZSBldmVudHMuXG4gKi9cbmZ1bmN0aW9uIHVzZVBvaW50ZXJEb3duT3V0c2lkZShcbiAgb25Qb2ludGVyRG93bk91dHNpZGU/OiAoZXZlbnQ6IFBvaW50ZXJEb3duT3V0c2lkZUV2ZW50KSA9PiB2b2lkLFxuICBvd25lckRvY3VtZW50OiBEb2N1bWVudCA9IGdsb2JhbFRoaXM/LmRvY3VtZW50XG4pIHtcbiAgY29uc3QgaGFuZGxlUG9pbnRlckRvd25PdXRzaWRlID0gdXNlQ2FsbGJhY2tSZWYob25Qb2ludGVyRG93bk91dHNpZGUpIGFzIEV2ZW50TGlzdGVuZXI7XG4gIGNvbnN0IGlzUG9pbnRlckluc2lkZVJlYWN0VHJlZVJlZiA9IFJlYWN0LnVzZVJlZihmYWxzZSk7XG4gIGNvbnN0IGhhbmRsZUNsaWNrUmVmID0gUmVhY3QudXNlUmVmKCgpID0+IHt9KTtcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZVBvaW50ZXJEb3duID0gKGV2ZW50OiBQb2ludGVyRXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC50YXJnZXQgJiYgIWlzUG9pbnRlckluc2lkZVJlYWN0VHJlZVJlZi5jdXJyZW50KSB7XG4gICAgICAgIGNvbnN0IGV2ZW50RGV0YWlsID0geyBvcmlnaW5hbEV2ZW50OiBldmVudCB9O1xuXG4gICAgICAgIGZ1bmN0aW9uIGhhbmRsZUFuZERpc3BhdGNoUG9pbnRlckRvd25PdXRzaWRlRXZlbnQoKSB7XG4gICAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudChcbiAgICAgICAgICAgIFBPSU5URVJfRE9XTl9PVVRTSURFLFxuICAgICAgICAgICAgaGFuZGxlUG9pbnRlckRvd25PdXRzaWRlLFxuICAgICAgICAgICAgZXZlbnREZXRhaWwsXG4gICAgICAgICAgICB7IGRpc2NyZXRlOiB0cnVlIH1cbiAgICAgICAgICApO1xuICAgICAgICB9XG5cbiAgICAgICAgLyoqXG4gICAgICAgICAqIE9uIHRvdWNoIGRldmljZXMsIHdlIG5lZWQgdG8gd2FpdCBmb3IgYSBjbGljayBldmVudCBiZWNhdXNlIGJyb3dzZXJzIGltcGxlbWVudFxuICAgICAgICAgKiBhIH4zNTBtcyBkZWxheSBiZXR3ZWVuIHRoZSB0aW1lIHRoZSB1c2VyIHN0b3BzIHRvdWNoaW5nIHRoZSBkaXNwbGF5IGFuZCB3aGVuIHRoZVxuICAgICAgICAgKiBicm93c2VyIGV4ZWN1dHJlcyBldmVudHMuIFdlIG5lZWQgdG8gZW5zdXJlIHdlIGRvbid0IHJlYWN0aXZhdGUgcG9pbnRlci1ldmVudHMgd2l0aGluXG4gICAgICAgICAqIHRoaXMgdGltZWZyYW1lIG90aGVyd2lzZSB0aGUgYnJvd3NlciBtYXkgZXhlY3V0ZSBldmVudHMgdGhhdCBzaG91bGQgaGF2ZSBiZWVuIHByZXZlbnRlZC5cbiAgICAgICAgICpcbiAgICAgICAgICogQWRkaXRpb25hbGx5LCB0aGlzIGFsc28gbGV0cyB1cyBkZWFsIGF1dG9tYXRpY2FsbHkgd2l0aCBjYW5jZWxsYXRpb25zIHdoZW4gYSBjbGljayBldmVudFxuICAgICAgICAgKiBpc24ndCByYWlzZWQgYmVjYXVzZSB0aGUgcGFnZSB3YXMgY29uc2lkZXJlZCBzY3JvbGxlZC9kcmFnLXNjcm9sbGVkLCBsb25nLXByZXNzZWQsIGV0Yy5cbiAgICAgICAgICpcbiAgICAgICAgICogVGhpcyBpcyB3aHkgd2UgYWxzbyBjb250aW51b3VzbHkgcmVtb3ZlIHRoZSBwcmV2aW91cyBsaXN0ZW5lciwgYmVjYXVzZSB3ZSBjYW5ub3QgYmVcbiAgICAgICAgICogY2VydGFpbiB0aGF0IGl0IHdhcyByYWlzZWQsIGFuZCB0aGVyZWZvcmUgY2xlYW5lZC11cC5cbiAgICAgICAgICovXG4gICAgICAgIGlmIChldmVudC5wb2ludGVyVHlwZSA9PT0gJ3RvdWNoJykge1xuICAgICAgICAgIG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVDbGlja1JlZi5jdXJyZW50KTtcbiAgICAgICAgICBoYW5kbGVDbGlja1JlZi5jdXJyZW50ID0gaGFuZGxlQW5kRGlzcGF0Y2hQb2ludGVyRG93bk91dHNpZGVFdmVudDtcbiAgICAgICAgICBvd25lckRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgaGFuZGxlQ2xpY2tSZWYuY3VycmVudCwgeyBvbmNlOiB0cnVlIH0pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGhhbmRsZUFuZERpc3BhdGNoUG9pbnRlckRvd25PdXRzaWRlRXZlbnQoKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gV2UgbmVlZCB0byByZW1vdmUgdGhlIGV2ZW50IGxpc3RlbmVyIGluIGNhc2UgdGhlIG91dHNpZGUgY2xpY2sgaGFzIGJlZW4gY2FuY2VsZWQuXG4gICAgICAgIC8vIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL3JhZGl4LXVpL3ByaW1pdGl2ZXMvaXNzdWVzLzIxNzFcbiAgICAgICAgb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZUNsaWNrUmVmLmN1cnJlbnQpO1xuICAgICAgfVxuICAgICAgaXNQb2ludGVySW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIGlmIHRoaXMgaG9vayBleGVjdXRlcyBpbiBhIGNvbXBvbmVudCB0aGF0IG1vdW50cyB2aWEgYSBgcG9pbnRlcmRvd25gIGV2ZW50LCB0aGUgZXZlbnRcbiAgICAgKiB3b3VsZCBidWJibGUgdXAgdG8gdGhlIGRvY3VtZW50IGFuZCB0cmlnZ2VyIGEgYHBvaW50ZXJEb3duT3V0c2lkZWAgZXZlbnQuIFdlIGF2b2lkXG4gICAgICogdGhpcyBieSBkZWxheWluZyB0aGUgZXZlbnQgbGlzdGVuZXIgcmVnaXN0cmF0aW9uIG9uIHRoZSBkb2N1bWVudC5cbiAgICAgKiBUaGlzIGlzIG5vdCBSZWFjdCBzcGVjaWZpYywgYnV0IHJhdGhlciBob3cgdGhlIERPTSB3b3JrcywgaWU6XG4gICAgICogYGBgXG4gICAgICogYnV0dG9uLmFkZEV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJkb3duJywgKCkgPT4ge1xuICAgICAqICAgY29uc29sZS5sb2coJ0kgd2lsbCBsb2cnKTtcbiAgICAgKiAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJkb3duJywgKCkgPT4ge1xuICAgICAqICAgICBjb25zb2xlLmxvZygnSSB3aWxsIGFsc28gbG9nJyk7XG4gICAgICogICB9KVxuICAgICAqIH0pO1xuICAgICAqL1xuICAgIGNvbnN0IHRpbWVySWQgPSB3aW5kb3cuc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBvd25lckRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJkb3duJywgaGFuZGxlUG9pbnRlckRvd24pO1xuICAgIH0sIDApO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB3aW5kb3cuY2xlYXJUaW1lb3V0KHRpbWVySWQpO1xuICAgICAgb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdwb2ludGVyZG93bicsIGhhbmRsZVBvaW50ZXJEb3duKTtcbiAgICAgIG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVDbGlja1JlZi5jdXJyZW50KTtcbiAgICB9O1xuICB9LCBbb3duZXJEb2N1bWVudCwgaGFuZGxlUG9pbnRlckRvd25PdXRzaWRlXSk7XG5cbiAgcmV0dXJuIHtcbiAgICAvLyBlbnN1cmVzIHdlIGNoZWNrIFJlYWN0IGNvbXBvbmVudCB0cmVlIChub3QganVzdCBET00gdHJlZSlcbiAgICBvblBvaW50ZXJEb3duQ2FwdHVyZTogKCkgPT4gKGlzUG9pbnRlckluc2lkZVJlYWN0VHJlZVJlZi5jdXJyZW50ID0gdHJ1ZSksXG4gIH07XG59XG5cbi8qKlxuICogTGlzdGVucyBmb3Igd2hlbiBmb2N1cyBoYXBwZW5zIG91dHNpZGUgYSByZWFjdCBzdWJ0cmVlLlxuICogUmV0dXJucyBwcm9wcyB0byBwYXNzIHRvIHRoZSByb290IChub2RlKSBvZiB0aGUgc3VidHJlZSB3ZSB3YW50IHRvIGNoZWNrLlxuICovXG5mdW5jdGlvbiB1c2VGb2N1c091dHNpZGUoXG4gIG9uRm9jdXNPdXRzaWRlPzogKGV2ZW50OiBGb2N1c091dHNpZGVFdmVudCkgPT4gdm9pZCxcbiAgb3duZXJEb2N1bWVudDogRG9jdW1lbnQgPSBnbG9iYWxUaGlzPy5kb2N1bWVudFxuKSB7XG4gIGNvbnN0IGhhbmRsZUZvY3VzT3V0c2lkZSA9IHVzZUNhbGxiYWNrUmVmKG9uRm9jdXNPdXRzaWRlKSBhcyBFdmVudExpc3RlbmVyO1xuICBjb25zdCBpc0ZvY3VzSW5zaWRlUmVhY3RUcmVlUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUZvY3VzID0gKGV2ZW50OiBGb2N1c0V2ZW50KSA9PiB7XG4gICAgICBpZiAoZXZlbnQudGFyZ2V0ICYmICFpc0ZvY3VzSW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY29uc3QgZXZlbnREZXRhaWwgPSB7IG9yaWdpbmFsRXZlbnQ6IGV2ZW50IH07XG4gICAgICAgIGhhbmRsZUFuZERpc3BhdGNoQ3VzdG9tRXZlbnQoRk9DVVNfT1VUU0lERSwgaGFuZGxlRm9jdXNPdXRzaWRlLCBldmVudERldGFpbCwge1xuICAgICAgICAgIGRpc2NyZXRlOiBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfTtcbiAgICBvd25lckRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3VzaW4nLCBoYW5kbGVGb2N1cyk7XG4gICAgcmV0dXJuICgpID0+IG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIGhhbmRsZUZvY3VzKTtcbiAgfSwgW293bmVyRG9jdW1lbnQsIGhhbmRsZUZvY3VzT3V0c2lkZV0pO1xuXG4gIHJldHVybiB7XG4gICAgb25Gb2N1c0NhcHR1cmU6ICgpID0+IChpc0ZvY3VzSW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQgPSB0cnVlKSxcbiAgICBvbkJsdXJDYXB0dXJlOiAoKSA9PiAoaXNGb2N1c0luc2lkZVJlYWN0VHJlZVJlZi5jdXJyZW50ID0gZmFsc2UpLFxuICB9O1xufVxuXG5mdW5jdGlvbiBkaXNwYXRjaFVwZGF0ZSgpIHtcbiAgY29uc3QgZXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoQ09OVEVYVF9VUERBVEUpO1xuICBkb2N1bWVudC5kaXNwYXRjaEV2ZW50KGV2ZW50KTtcbn1cblxuZnVuY3Rpb24gaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudDxFIGV4dGVuZHMgQ3VzdG9tRXZlbnQsIE9yaWdpbmFsRXZlbnQgZXh0ZW5kcyBFdmVudD4oXG4gIG5hbWU6IHN0cmluZyxcbiAgaGFuZGxlcjogKChldmVudDogRSkgPT4gdm9pZCkgfCB1bmRlZmluZWQsXG4gIGRldGFpbDogeyBvcmlnaW5hbEV2ZW50OiBPcmlnaW5hbEV2ZW50IH0gJiAoRSBleHRlbmRzIEN1c3RvbUV2ZW50PGluZmVyIEQ+ID8gRCA6IG5ldmVyKSxcbiAgeyBkaXNjcmV0ZSB9OiB7IGRpc2NyZXRlOiBib29sZWFuIH1cbikge1xuICBjb25zdCB0YXJnZXQgPSBkZXRhaWwub3JpZ2luYWxFdmVudC50YXJnZXQ7XG4gIGNvbnN0IGV2ZW50ID0gbmV3IEN1c3RvbUV2ZW50KG5hbWUsIHsgYnViYmxlczogZmFsc2UsIGNhbmNlbGFibGU6IHRydWUsIGRldGFpbCB9KTtcbiAgaWYgKGhhbmRsZXIpIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKG5hbWUsIGhhbmRsZXIgYXMgRXZlbnRMaXN0ZW5lciwgeyBvbmNlOiB0cnVlIH0pO1xuXG4gIGlmIChkaXNjcmV0ZSkge1xuICAgIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudCh0YXJnZXQsIGV2ZW50KTtcbiAgfSBlbHNlIHtcbiAgICB0YXJnZXQuZGlzcGF0Y2hFdmVudChldmVudCk7XG4gIH1cbn1cblxuY29uc3QgUm9vdCA9IERpc21pc3NhYmxlTGF5ZXI7XG5jb25zdCBCcmFuY2ggPSBEaXNtaXNzYWJsZUxheWVyQnJhbmNoO1xuXG5leHBvcnQge1xuICBEaXNtaXNzYWJsZUxheWVyLFxuICBEaXNtaXNzYWJsZUxheWVyQnJhbmNoLFxuICAvL1xuICBSb290LFxuICBCcmFuY2gsXG59O1xuZXhwb3J0IHR5cGUgeyBEaXNtaXNzYWJsZUxheWVyUHJvcHMgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNvbXBvc2VFdmVudEhhbmRsZXJzIiwiUHJpbWl0aXZlIiwiZGlzcGF0Y2hEaXNjcmV0ZUN1c3RvbUV2ZW50IiwidXNlQ29tcG9zZWRSZWZzIiwidXNlQ2FsbGJhY2tSZWYiLCJ1c2VFc2NhcGVLZXlkb3duIiwianN4IiwiRElTTUlTU0FCTEVfTEFZRVJfTkFNRSIsIkNPTlRFWFRfVVBEQVRFIiwiUE9JTlRFUl9ET1dOX09VVFNJREUiLCJGT0NVU19PVVRTSURFIiwib3JpZ2luYWxCb2R5UG9pbnRlckV2ZW50cyIsIkRpc21pc3NhYmxlTGF5ZXJDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsImxheWVycyIsIlNldCIsImxheWVyc1dpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkIiwiYnJhbmNoZXMiLCJEaXNtaXNzYWJsZUxheWVyIiwiZm9yd2FyZFJlZiIsInByb3BzIiwiZm9yd2FyZGVkUmVmIiwiZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzIiwib25Fc2NhcGVLZXlEb3duIiwib25Qb2ludGVyRG93bk91dHNpZGUiLCJvbkZvY3VzT3V0c2lkZSIsIm9uSW50ZXJhY3RPdXRzaWRlIiwib25EaXNtaXNzIiwibGF5ZXJQcm9wcyIsImNvbnRleHQiLCJ1c2VDb250ZXh0Iiwibm9kZSIsInNldE5vZGUiLCJ1c2VTdGF0ZSIsIm93bmVyRG9jdW1lbnQiLCJnbG9iYWxUaGlzIiwiZG9jdW1lbnQiLCJmb3JjZSIsImNvbXBvc2VkUmVmcyIsIkFycmF5IiwiZnJvbSIsImhpZ2hlc3RMYXllcldpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkIiwic2xpY2UiLCJoaWdoZXN0TGF5ZXJXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZEluZGV4IiwiaW5kZXhPZiIsImluZGV4IiwiaXNCb2R5UG9pbnRlckV2ZW50c0Rpc2FibGVkIiwic2l6ZSIsImlzUG9pbnRlckV2ZW50c0VuYWJsZWQiLCJwb2ludGVyRG93bk91dHNpZGUiLCJ1c2VQb2ludGVyRG93bk91dHNpZGUiLCJldmVudCIsInRhcmdldCIsImlzUG9pbnRlckRvd25PbkJyYW5jaCIsInNvbWUiLCJicmFuY2giLCJjb250YWlucyIsImRlZmF1bHRQcmV2ZW50ZWQiLCJmb2N1c091dHNpZGUiLCJ1c2VGb2N1c091dHNpZGUiLCJpc0ZvY3VzSW5CcmFuY2giLCJpc0hpZ2hlc3RMYXllciIsInByZXZlbnREZWZhdWx0IiwidXNlRWZmZWN0IiwiYm9keSIsInN0eWxlIiwicG9pbnRlckV2ZW50cyIsImFkZCIsImRpc3BhdGNoVXBkYXRlIiwiZGVsZXRlIiwiaGFuZGxlVXBkYXRlIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJkaXYiLCJyZWYiLCJvbkZvY3VzQ2FwdHVyZSIsIm9uQmx1ckNhcHR1cmUiLCJvblBvaW50ZXJEb3duQ2FwdHVyZSIsImRpc3BsYXlOYW1lIiwiQlJBTkNIX05BTUUiLCJEaXNtaXNzYWJsZUxheWVyQnJhbmNoIiwidXNlUmVmIiwiY3VycmVudCIsImhhbmRsZVBvaW50ZXJEb3duT3V0c2lkZSIsImlzUG9pbnRlckluc2lkZVJlYWN0VHJlZVJlZiIsImhhbmRsZUNsaWNrUmVmIiwiaGFuZGxlUG9pbnRlckRvd24iLCJoYW5kbGVBbmREaXNwYXRjaFBvaW50ZXJEb3duT3V0c2lkZUV2ZW50IiwiaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudCIsImV2ZW50RGV0YWlsIiwiZGlzY3JldGUiLCJvcmlnaW5hbEV2ZW50IiwicG9pbnRlclR5cGUiLCJvbmNlIiwidGltZXJJZCIsIndpbmRvdyIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJoYW5kbGVGb2N1c091dHNpZGUiLCJpc0ZvY3VzSW5zaWRlUmVhY3RUcmVlUmVmIiwiaGFuZGxlRm9jdXMiLCJDdXN0b21FdmVudCIsImRpc3BhdGNoRXZlbnQiLCJuYW1lIiwiaGFuZGxlciIsImRldGFpbCIsImJ1YmJsZXMiLCJjYW5jZWxhYmxlIiwiUm9vdCIsIkJyYW5jaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/focus-guards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n        document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n        document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n        count++;\n        return ()=>{\n            if (count === 1) {\n                document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node)=>node.remove());\n            }\n            count--;\n        };\n    }, []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // src/focus-scope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContainer(node));\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trapped) {\n            let handleFocusIn2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const target = event.target;\n                if (container.contains(target)) {\n                    lastFocusedElementRef.current = target;\n                } else {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleFocusOut2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const relatedTarget = event.relatedTarget;\n                if (relatedTarget === null) return;\n                if (!container.contains(relatedTarget)) {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleMutations2 = function(mutations) {\n                const focusedElement = document.activeElement;\n                if (focusedElement !== document.body) return;\n                for (const mutation of mutations){\n                    if (mutation.removedNodes.length > 0) focus(container);\n                }\n            };\n            var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n            document.addEventListener(\"focusin\", handleFocusIn2);\n            document.addEventListener(\"focusout\", handleFocusOut2);\n            const mutationObserver = new MutationObserver(handleMutations2);\n            if (container) mutationObserver.observe(container, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                document.removeEventListener(\"focusin\", handleFocusIn2);\n                document.removeEventListener(\"focusout\", handleFocusOut2);\n                mutationObserver.disconnect();\n            };\n        }\n    }, [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (container) {\n            focusScopesStack.add(focusScope);\n            const previouslyFocusedElement = document.activeElement;\n            const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n            if (!hasFocusedCandidate) {\n                const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                container.dispatchEvent(mountEvent);\n                if (!mountEvent.defaultPrevented) {\n                    focusFirst(removeLinks(getTabbableCandidates(container)), {\n                        select: true\n                    });\n                    if (document.activeElement === previouslyFocusedElement) {\n                        focus(container);\n                    }\n                }\n            }\n            return ()=>{\n                container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                setTimeout(()=>{\n                    const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    container.dispatchEvent(unmountEvent);\n                    if (!unmountEvent.defaultPrevented) {\n                        focus(previouslyFocusedElement ?? document.body, {\n                            select: true\n                        });\n                    }\n                    container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    focusScopesStack.remove(focusScope);\n                }, 0);\n            };\n        }\n    }, [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (!loop && !trapped) return;\n        if (focusScope.paused) return;\n        const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n        const focusedElement = document.activeElement;\n        if (isTabKey && focusedElement) {\n            const container2 = event.currentTarget;\n            const [first, last] = getTabbableEdges(container2);\n            const hasTabbableElementsInside = first && last;\n            if (!hasTabbableElementsInside) {\n                if (focusedElement === container2) event.preventDefault();\n            } else {\n                if (!event.shiftKey && focusedElement === last) {\n                    event.preventDefault();\n                    if (loop) focus(first, {\n                        select: true\n                    });\n                } else if (event.shiftKey && focusedElement === first) {\n                    event.preventDefault();\n                    if (loop) focus(last, {\n                        select: true\n                    });\n                }\n            }\n        }\n    }, [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1pZC9kaXN0L2luZGV4Lm1qcz9hMGJkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2lkL3NyYy9pZC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdFwiO1xudmFyIHVzZVJlYWN0SWQgPSBSZWFjdFtcIiB1c2VJZCBcIi50cmltKCkudG9TdHJpbmcoKV0gfHwgKCgpID0+IHZvaWQgMCk7XG52YXIgY291bnQgPSAwO1xuZnVuY3Rpb24gdXNlSWQoZGV0ZXJtaW5pc3RpY0lkKSB7XG4gIGNvbnN0IFtpZCwgc2V0SWRdID0gUmVhY3QudXNlU3RhdGUodXNlUmVhY3RJZCgpKTtcbiAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWRldGVybWluaXN0aWNJZCkgc2V0SWQoKHJlYWN0SWQpID0+IHJlYWN0SWQgPz8gU3RyaW5nKGNvdW50KyspKTtcbiAgfSwgW2RldGVybWluaXN0aWNJZF0pO1xuICByZXR1cm4gZGV0ZXJtaW5pc3RpY0lkIHx8IChpZCA/IGByYWRpeC0ke2lkfWAgOiBcIlwiKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUlkXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: (...args)=>{\n            const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                animationFrame: updatePositionStrategy === \"always\"\n            });\n            return cleanup;\n        },\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: ({ elements, rects, availableWidth, availableHeight })=>{\n                    const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                    const contentStyle = elements.floating.style;\n                    contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                }\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (isPositioned) {\n            handlePlaced?.();\n        }\n    }, [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            stylesRef.current = node2 ? getComputedStyle(node2) : null;\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // src/tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const isOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const skipDelayTimer = skipDelayTimerRef.current;\n        return ()=>window.clearTimeout(skipDelayTimer);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayedRef,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            isOpenDelayedRef.current = false;\n        }, []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            skipDelayTimerRef.current = window.setTimeout(()=>isOpenDelayedRef.current = true, skipDelayDuration);\n        }, [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((inTransit)=>{\n            isPointerInTransitRef.current = inTransit;\n        }, []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: (open2)=>{\n            if (open2) {\n                providerContext.onOpen();\n                document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n            } else {\n                providerContext.onClose();\n            }\n            onOpenChange?.(open2);\n        },\n        caller: TOOLTIP_NAME\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n    }, [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        wasOpenDelayedRef.current = false;\n        setOpen(true);\n    }, [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        setOpen(false);\n    }, [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = window.setTimeout(()=>{\n            wasOpenDelayedRef.current = true;\n            setOpen(true);\n            openTimerRef.current = 0;\n        }, delayDuration);\n    }, [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (openTimerRef.current) {\n                window.clearTimeout(openTimerRef.current);\n                openTimerRef.current = 0;\n            }\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n                else handleOpen();\n            }, [\n                providerContext.isOpenDelayedRef,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (disableHoverableContent) {\n                    handleClose();\n                } else {\n                    window.clearTimeout(openTimerRef.current);\n                    openTimerRef.current = 0;\n                }\n            }, [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>isPointerDownRef.current = false, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>document.removeEventListener(\"pointerup\", handlePointerUp);\n    }, [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                if (context.open) {\n                    context.onClose();\n                }\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        setPointerGraceArea(null);\n        onPointerInTransitChange(false);\n    }, [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event, hoverTarget)=>{\n        const currentTarget = event.currentTarget;\n        const exitPoint = {\n            x: event.clientX,\n            y: event.clientY\n        };\n        const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n        const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n        const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n        const graceArea = getHull([\n            ...paddedExitPoints,\n            ...hoverTargetPoints\n        ]);\n        setPointerGraceArea(graceArea);\n        onPointerInTransitChange(true);\n    }, [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>handleRemoveGraceArea();\n    }, [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trigger && content) {\n            const handleTriggerLeave = (event)=>handleCreateGraceArea(event, content);\n            const handleContentLeave = (event)=>handleCreateGraceArea(event, trigger);\n            trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n            content.addEventListener(\"pointerleave\", handleContentLeave);\n            return ()=>{\n                trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                content.removeEventListener(\"pointerleave\", handleContentLeave);\n            };\n        }\n    }, [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (pointerGraceArea) {\n            const handleTrackPointerGrace = (event)=>{\n                const target = event.target;\n                const pointerPosition = {\n                    x: event.clientX,\n                    y: event.clientY\n                };\n                const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                if (hasEnteredTarget) {\n                    handleRemoveGraceArea();\n                } else if (isPointerOutsideGraceArea) {\n                    handleRemoveGraceArea();\n                    onClose();\n                }\n            };\n            document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n            return ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace);\n        }\n    }, [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar Slottable = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.createSlottable)(\"TooltipContent\");\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        document.addEventListener(TOOLTIP_OPEN, onClose);\n        return ()=>document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (context.trigger) {\n            const handleScroll = (event)=>{\n                const target = event.target;\n                if (target?.contains(context.trigger)) onClose();\n            };\n            window.addEventListener(\"scroll\", handleScroll, {\n                capture: true\n            });\n            return ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n        }\n    }, [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanM/NzgxNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2UtY2FsbGJhY2stcmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n      ref.current = callback;\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => ref.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lZmZlY3QtZXZlbnQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ29FO0FBQ3JDO0FBQy9CLDBCQUEwQix5TEFBSztBQUMvQiw4QkFBOEIseUxBQUs7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLHlDQUFZO0FBQzFCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJO0FBQ0osSUFBSSxrRkFBZTtBQUNuQjtBQUNBLEtBQUs7QUFDTDtBQUNBLFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lZmZlY3QtZXZlbnQvZGlzdC9pbmRleC5tanM/ZDk3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXNlLWVmZmVjdC1ldmVudC50c3hcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZVJlYWN0RWZmZWN0RXZlbnQgPSBSZWFjdFtcIiB1c2VFZmZlY3RFdmVudCBcIi50cmltKCkudG9TdHJpbmcoKV07XG52YXIgdXNlUmVhY3RJbnNlcnRpb25FZmZlY3QgPSBSZWFjdFtcIiB1c2VJbnNlcnRpb25FZmZlY3QgXCIudHJpbSgpLnRvU3RyaW5nKCldO1xuZnVuY3Rpb24gdXNlRWZmZWN0RXZlbnQoY2FsbGJhY2spIHtcbiAgaWYgKHR5cGVvZiB1c2VSZWFjdEVmZmVjdEV2ZW50ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICByZXR1cm4gdXNlUmVhY3RFZmZlY3RFdmVudChjYWxsYmFjayk7XG4gIH1cbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKCgpID0+IHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgY2FsbCBhbiBldmVudCBoYW5kbGVyIHdoaWxlIHJlbmRlcmluZy5cIik7XG4gIH0pO1xuICBpZiAodHlwZW9mIHVzZVJlYWN0SW5zZXJ0aW9uRWZmZWN0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB1c2VSZWFjdEluc2VydGlvbkVmZmVjdCgoKSA9PiB7XG4gICAgICByZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICAgIH0pO1xuICB9IGVsc2Uge1xuICAgIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICByZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICAgIH0pO1xuICB9XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiByZWYuY3VycmVudD8uKC4uLmFyZ3MpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VFZmZlY3RFdmVudFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcz8yYmEwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1lc2NhcGUta2V5ZG93bi9zcmMvdXNlLWVzY2FwZS1rZXlkb3duLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZlwiO1xuZnVuY3Rpb24gdXNlRXNjYXBlS2V5ZG93bihvbkVzY2FwZUtleURvd25Qcm9wLCBvd25lckRvY3VtZW50ID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQpIHtcbiAgY29uc3Qgb25Fc2NhcGVLZXlEb3duID0gdXNlQ2FsbGJhY2tSZWYob25Fc2NhcGVLZXlEb3duUHJvcCk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LmtleSA9PT0gXCJFc2NhcGVcIikge1xuICAgICAgICBvbkVzY2FwZUtleURvd24oZXZlbnQpO1xuICAgICAgfVxuICAgIH07XG4gICAgb3duZXJEb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gICAgcmV0dXJuICgpID0+IG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICB9LCBbb25Fc2NhcGVLZXlEb3duLCBvd25lckRvY3VtZW50XSk7XG59XG5leHBvcnQge1xuICB1c2VFc2NhcGVLZXlkb3duXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsOENBQThDLGtEQUFxQjtBQUNuRTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QvZGlzdC9pbmRleC5tanM/Yjk5MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtbGF5b3V0LWVmZmVjdC9zcmMvdXNlLWxheW91dC1lZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/use-size.tsx\n\n\nfunction useSize(element) {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUMrQjtBQUN1QjtBQUNkO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLHFCQUFxQiw2Q0FBZ0I7QUFDckM7QUFDQSwyQkFBMkIsc0RBQUc7QUFDOUIsTUFBTSxnRUFBUztBQUNmO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFLRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcz8wOGVmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy92aXN1YWxseS1oaWRkZW4udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlXCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBWSVNVQUxMWV9ISURERU5fU1RZTEVTID0gT2JqZWN0LmZyZWV6ZSh7XG4gIC8vIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL3R3YnMvYm9vdHN0cmFwL2Jsb2IvbWFpbi9zY3NzL21peGlucy9fdmlzdWFsbHktaGlkZGVuLnNjc3NcbiAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcbiAgYm9yZGVyOiAwLFxuICB3aWR0aDogMSxcbiAgaGVpZ2h0OiAxLFxuICBwYWRkaW5nOiAwLFxuICBtYXJnaW46IC0xLFxuICBvdmVyZmxvdzogXCJoaWRkZW5cIixcbiAgY2xpcDogXCJyZWN0KDAsIDAsIDAsIDApXCIsXG4gIHdoaXRlU3BhY2U6IFwibm93cmFwXCIsXG4gIHdvcmRXcmFwOiBcIm5vcm1hbFwiXG59KTtcbnZhciBOQU1FID0gXCJWaXN1YWxseUhpZGRlblwiO1xudmFyIFZpc3VhbGx5SGlkZGVuID0gUmVhY3QuZm9yd2FyZFJlZihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgIFByaW1pdGl2ZS5zcGFuLFxuICAgICAge1xuICAgICAgICAuLi5wcm9wcyxcbiAgICAgICAgcmVmOiBmb3J3YXJkZWRSZWYsXG4gICAgICAgIHN0eWxlOiB7IC4uLlZJU1VBTExZX0hJRERFTl9TVFlMRVMsIC4uLnByb3BzLnN0eWxlIH1cbiAgICAgIH1cbiAgICApO1xuICB9XG4pO1xuVmlzdWFsbHlIaWRkZW4uZGlzcGxheU5hbWUgPSBOQU1FO1xudmFyIFJvb3QgPSBWaXN1YWxseUhpZGRlbjtcbmV4cG9ydCB7XG4gIFJvb3QsXG4gIFZJU1VBTExZX0hJRERFTl9TVFlMRVMsXG4gIFZpc3VhbGx5SGlkZGVuXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;