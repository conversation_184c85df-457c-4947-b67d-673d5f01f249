#!/usr/bin/env python3
"""
Load Testing Setup for Artwork Outreach Web App
Creates comprehensive load testing scenarios, API load tests, database performance tests,
system limits testing, and performance reporting.
"""

import asyncio
import json
import time
import random
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import aiohttp
import asyncpg
import psutil
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path


@dataclass
class LoadTestConfig:
    """Configuration for load testing scenarios."""
    name: str
    duration_seconds: int
    concurrent_users: int
    requests_per_user: int
    ramp_up_seconds: int = 0
    endpoints: List[str] = None
    payload_size_kb: float = 1.0
    think_time_ms: int = 1000
    

@dataclass
class TestResult:
    """Individual test result data."""
    timestamp: float
    endpoint: str
    response_time: float
    status_code: int
    success: bool
    error_message: Optional[str] = None
    payload_size: int = 0


@dataclass
class LoadTestReport:
    """Comprehensive load test report."""
    config: LoadTestConfig
    start_time: datetime
    end_time: datetime
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    p95_response_time: float
    p99_response_time: float
    requests_per_second: float
    errors_by_type: Dict[str, int]
    resource_usage: Dict[str, Any]
    results: List[TestResult]


class LoadTestRunner:
    """Main load testing orchestrator."""
    
    def __init__(self, base_url: str = "http://localhost:8000", 
                 database_url: str = "postgresql://user:password@localhost/test"):
        self.base_url = base_url
        self.database_url = database_url
        self.results: List[TestResult] = []
        self.resource_monitor = ResourceMonitor()
        
    async def run_load_test(self, config: LoadTestConfig) -> LoadTestReport:
        """Run a complete load test scenario."""
        print(f"Starting load test: {config.name}")
        start_time = datetime.now()
        
        # Start resource monitoring
        monitor_task = asyncio.create_task(self.resource_monitor.start_monitoring())
        
        try:
            # Run the actual load test
            await self._execute_load_test(config)
            
            end_time = datetime.now()
            
            # Stop monitoring
            self.resource_monitor.stop_monitoring()
            await monitor_task
            
            # Generate report
            report = self._generate_report(config, start_time, end_time)
            
            # Save results
            await self._save_results(report)
            
            return report
            
        except Exception as e:
            self.resource_monitor.stop_monitoring()
            if not monitor_task.done():
                monitor_task.cancel()
            print(f"Load test failed: {e}")
            raise
            
    async def _execute_load_test(self, config: LoadTestConfig):
        """Execute the load test with configured parameters."""
        semaphore = asyncio.Semaphore(config.concurrent_users)
        
        # Calculate request timing
        total_requests = config.concurrent_users * config.requests_per_user
        request_interval = config.duration_seconds / total_requests if total_requests > 0 else 1
        
        # Create user sessions
        tasks = []
        for user_id in range(config.concurrent_users):
            # Stagger user start times for ramp-up
            start_delay = (user_id / config.concurrent_users) * config.ramp_up_seconds
            task = self._simulate_user_session(
                user_id, config, semaphore, start_delay, request_interval
            )
            tasks.append(task)
            
        # Wait for all users to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
    async def _simulate_user_session(self, user_id: int, config: LoadTestConfig, 
                                   semaphore: asyncio.Semaphore, start_delay: float,
                                   request_interval: float):
        """Simulate a single user's session."""
        if start_delay > 0:
            await asyncio.sleep(start_delay)
            
        async with semaphore:
            connector = aiohttp.TCPConnector(limit=100)
            timeout = aiohttp.ClientTimeout(total=30)
            
            async with aiohttp.ClientSession(
                connector=connector, timeout=timeout
            ) as session:
                
                for request_num in range(config.requests_per_user):
                    # Select endpoint to test
                    endpoint = self._select_endpoint(config.endpoints or ['/health'])
                    
                    # Make request
                    result = await self._make_request(session, endpoint, user_id)
                    self.results.append(result)
                    
                    # Think time between requests
                    if config.think_time_ms > 0 and request_num < config.requests_per_user - 1:
                        think_time = config.think_time_ms / 1000
                        # Add some randomness to think time
                        actual_think_time = think_time * (0.5 + random.random())
                        await asyncio.sleep(actual_think_time)
                        
    def _select_endpoint(self, endpoints: List[str]) -> str:
        """Select an endpoint to test, with realistic distribution."""
        # Weighted distribution to simulate real usage patterns
        endpoint_weights = {
            '/health': 0.05,
            '/api/v1/streamers': 0.25,
            '/api/v1/streamers/available': 0.30,
            '/api/v1/user/status': 0.20,
            '/api/v1/assignments': 0.15,
            '/api/v1/streamers/request-new': 0.05,
        }
        
        available_endpoints = [ep for ep in endpoints if ep in endpoint_weights]
        if not available_endpoints:
            return random.choice(endpoints)
            
        weights = [endpoint_weights.get(ep, 0.1) for ep in available_endpoints]
        return random.choices(available_endpoints, weights=weights)[0]
        
    async def _make_request(self, session: aiohttp.ClientSession, 
                          endpoint: str, user_id: int) -> TestResult:
        """Make a single HTTP request and record the result."""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            # Add realistic headers
            headers = {
                'User-Agent': f'LoadTest-User-{user_id}',
                'Accept': 'application/json',
                'Authorization': f'Bearer mock-token-{user_id}',
            }
            
            # Select HTTP method based on endpoint
            method = self._get_http_method(endpoint)
            payload = self._get_request_payload(endpoint, method)
            
            if method == 'GET':
                async with session.get(url, headers=headers) as response:
                    await response.text()
                    return TestResult(
                        timestamp=start_time,
                        endpoint=endpoint,
                        response_time=time.time() - start_time,
                        status_code=response.status,
                        success=200 <= response.status < 400,
                        payload_size=len(await response.text())
                    )
            elif method == 'POST':
                async with session.post(url, json=payload, headers=headers) as response:
                    await response.text()
                    return TestResult(
                        timestamp=start_time,
                        endpoint=endpoint,
                        response_time=time.time() - start_time,
                        status_code=response.status,
                        success=200 <= response.status < 400,
                        payload_size=len(json.dumps(payload) if payload else "{}")
                    )
            elif method == 'PATCH':
                async with session.patch(url, json=payload, headers=headers) as response:
                    await response.text()
                    return TestResult(
                        timestamp=start_time,
                        endpoint=endpoint,
                        response_time=time.time() - start_time,
                        status_code=response.status,
                        success=200 <= response.status < 400,
                        payload_size=len(json.dumps(payload) if payload else "{}")
                    )
                    
        except Exception as e:
            return TestResult(
                timestamp=start_time,
                endpoint=endpoint,
                response_time=time.time() - start_time,
                status_code=0,
                success=False,
                error_message=str(e)
            )
            
    def _get_http_method(self, endpoint: str) -> str:
        """Determine HTTP method based on endpoint."""
        if 'request-new' in endpoint:
            return 'POST'
        elif '/assignments/' in endpoint:
            return 'PATCH'
        else:
            return 'GET'
            
    def _get_request_payload(self, endpoint: str, method: str) -> Optional[Dict]:
        """Generate appropriate payload for endpoint."""
        if method == 'POST' and 'request-new' in endpoint:
            return {'filters': {'max_followers': 50, 'language': 'en'}}
        elif method == 'PATCH' and '/assignments/' in endpoint:
            return {'status': random.choice(['pending', 'interested', 'contacted'])}
        return None
        
    def _generate_report(self, config: LoadTestConfig, 
                        start_time: datetime, end_time: datetime) -> LoadTestReport:
        """Generate comprehensive test report."""
        successful_results = [r for r in self.results if r.success]
        failed_results = [r for r in self.results if not r.success]
        
        response_times = [r.response_time for r in successful_results]
        
        # Calculate statistics
        avg_response_time = statistics.mean(response_times) if response_times else 0
        p95_response_time = self._percentile(response_times, 95) if response_times else 0
        p99_response_time = self._percentile(response_times, 99) if response_times else 0
        
        total_duration = (end_time - start_time).total_seconds()
        rps = len(successful_results) / total_duration if total_duration > 0 else 0
        
        # Count errors by type
        errors_by_type = {}
        for result in failed_results:
            if result.error_message:
                error_type = result.error_message.split(':')[0]
                errors_by_type[error_type] = errors_by_type.get(error_type, 0) + 1
            else:
                status_error = f"HTTP {result.status_code}"
                errors_by_type[status_error] = errors_by_type.get(status_error, 0) + 1
                
        return LoadTestReport(
            config=config,
            start_time=start_time,
            end_time=end_time,
            total_requests=len(self.results),
            successful_requests=len(successful_results),
            failed_requests=len(failed_results),
            average_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            requests_per_second=rps,
            errors_by_type=errors_by_type,
            resource_usage=self.resource_monitor.get_stats(),
            results=self.results
        )
        
    @staticmethod
    def _percentile(data: List[float], percentile: int) -> float:
        """Calculate percentile of a dataset."""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
        
    async def _save_results(self, report: LoadTestReport):
        """Save test results to files."""
        timestamp = report.start_time.strftime("%Y%m%d_%H%M%S")
        results_dir = Path(f"load_test_results_{timestamp}")
        results_dir.mkdir(exist_ok=True)
        
        # Save JSON report
        json_path = results_dir / "report.json"
        with open(json_path, 'w') as f:
            # Convert report to dict, handling non-serializable objects
            report_dict = asdict(report)
            report_dict['start_time'] = report.start_time.isoformat()
            report_dict['end_time'] = report.end_time.isoformat()
            json.dump(report_dict, f, indent=2)
            
        # Save CSV of individual results
        csv_path = results_dir / "results.csv"
        df = pd.DataFrame([asdict(r) for r in report.results])
        df.to_csv(csv_path, index=False)
        
        # Generate charts
        await self._generate_charts(report, results_dir)
        
        print(f"Results saved to {results_dir}")
        
    async def _generate_charts(self, report: LoadTestReport, output_dir: Path):
        """Generate performance charts."""
        # Response time over time
        plt.figure(figsize=(12, 8))
        
        successful_results = [r for r in report.results if r.success]
        timestamps = [r.timestamp for r in successful_results]
        response_times = [r.response_time * 1000 for r in successful_results]  # Convert to ms
        
        plt.subplot(2, 2, 1)
        plt.plot(timestamps, response_times, alpha=0.6)
        plt.title('Response Time Over Time')
        plt.xlabel('Time')
        plt.ylabel('Response Time (ms)')
        
        # Response time histogram
        plt.subplot(2, 2, 2)
        plt.hist(response_times, bins=50, alpha=0.7)
        plt.title('Response Time Distribution')
        plt.xlabel('Response Time (ms)')
        plt.ylabel('Frequency')
        
        # Requests per second over time
        plt.subplot(2, 2, 3)
        time_buckets = {}
        for result in successful_results:
            bucket = int(result.timestamp) 
            time_buckets[bucket] = time_buckets.get(bucket, 0) + 1
            
        times = sorted(time_buckets.keys())
        rps_values = [time_buckets[t] for t in times]
        
        plt.plot(times, rps_values)
        plt.title('Requests Per Second')
        plt.xlabel('Time')
        plt.ylabel('RPS')
        
        # Error rate by endpoint
        plt.subplot(2, 2, 4)
        endpoint_stats = {}
        for result in report.results:
            endpoint = result.endpoint
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {'total': 0, 'errors': 0}
            endpoint_stats[endpoint]['total'] += 1
            if not result.success:
                endpoint_stats[endpoint]['errors'] += 1
                
        endpoints = list(endpoint_stats.keys())
        error_rates = [
            (endpoint_stats[ep]['errors'] / endpoint_stats[ep]['total']) * 100
            for ep in endpoints
        ]
        
        plt.bar(range(len(endpoints)), error_rates)
        plt.title('Error Rate by Endpoint')
        plt.xlabel('Endpoint')
        plt.ylabel('Error Rate (%)')
        plt.xticks(range(len(endpoints)), [ep.split('/')[-1] for ep in endpoints], rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_dir / "performance_charts.png", dpi=300, bbox_inches='tight')
        plt.close()


class DatabaseLoadTester:
    """Specialized testing for database performance under load."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        
    async def test_database_under_load(self, concurrent_connections: int = 20,
                                     queries_per_connection: int = 100) -> Dict[str, Any]:
        """Test database performance under concurrent load."""
        print(f"Testing database with {concurrent_connections} connections, "
              f"{queries_per_connection} queries each")
              
        semaphore = asyncio.Semaphore(concurrent_connections)
        results = []
        
        async def run_queries():
            async with semaphore:
                conn = await asyncpg.connect(self.database_url)
                try:
                    query_results = []
                    for _ in range(queries_per_connection):
                        query = self._get_random_query()
                        start_time = time.time()
                        
                        try:
                            await conn.fetch(query)
                            execution_time = time.time() - start_time
                            query_results.append({
                                'query': query,
                                'execution_time': execution_time,
                                'success': True
                            })
                        except Exception as e:
                            query_results.append({
                                'query': query,
                                'execution_time': time.time() - start_time,
                                'success': False,
                                'error': str(e)
                            })
                            
                    return query_results
                finally:
                    await conn.close()
                    
        # Run all connections concurrently
        start_time = time.time()
        connection_results = await asyncio.gather(
            *[run_queries() for _ in range(concurrent_connections)],
            return_exceptions=True
        )
        total_time = time.time() - start_time
        
        # Flatten results
        for conn_results in connection_results:
            if isinstance(conn_results, list):
                results.extend(conn_results)
                
        # Calculate statistics
        successful_queries = [r for r in results if r.get('success', False)]
        execution_times = [r['execution_time'] for r in successful_queries]
        
        return {
            'total_queries': len(results),
            'successful_queries': len(successful_queries),
            'failed_queries': len(results) - len(successful_queries),
            'total_time': total_time,
            'queries_per_second': len(successful_queries) / total_time,
            'average_execution_time': statistics.mean(execution_times) if execution_times else 0,
            'p95_execution_time': self._percentile(execution_times, 95) if execution_times else 0,
            'max_execution_time': max(execution_times) if execution_times else 0,
        }
        
    def _get_random_query(self) -> str:
        """Get a random query to simulate realistic database load."""
        queries = [
            "SELECT * FROM streamers WHERE follower_count BETWEEN 0 AND 50 LIMIT 10",
            "SELECT * FROM streamers WHERE is_live = true LIMIT 5",
            "SELECT COUNT(*) FROM streamer_assignments WHERE created_at >= CURRENT_DATE",
            "SELECT s.*, sa.status FROM streamers s LEFT JOIN streamer_assignments sa ON s.twitch_user_id = sa.streamer_twitch_id LIMIT 20",
            "SELECT status, COUNT(*) FROM streamer_assignments GROUP BY status",
            "SELECT * FROM streamers WHERE display_name ILIKE '%art%' LIMIT 5",
            "SELECT * FROM streamer_assignments ORDER BY created_at DESC LIMIT 10",
        ]
        return random.choice(queries)
        
    @staticmethod
    def _percentile(data: List[float], percentile: int) -> float:
        """Calculate percentile of a dataset."""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]


class ResourceMonitor:
    """Monitor system resources during load testing."""
    
    def __init__(self):
        self.monitoring = False
        self.cpu_samples = []
        self.memory_samples = []
        self.disk_io_samples = []
        self.network_io_samples = []
        
    async def start_monitoring(self, interval: float = 1.0):
        """Start monitoring system resources."""
        self.monitoring = True
        
        # Get initial network/disk IO values
        initial_net_io = psutil.net_io_counters()
        initial_disk_io = psutil.disk_io_counters()
        
        while self.monitoring:
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=None)
                self.cpu_samples.append({
                    'timestamp': time.time(),
                    'cpu_percent': cpu_percent
                })
                
                # Memory usage
                memory = psutil.virtual_memory()
                self.memory_samples.append({
                    'timestamp': time.time(),
                    'memory_percent': memory.percent,
                    'memory_used_mb': memory.used / 1024 / 1024,
                    'memory_available_mb': memory.available / 1024 / 1024
                })
                
                # Disk IO
                disk_io = psutil.disk_io_counters()
                if disk_io:
                    self.disk_io_samples.append({
                        'timestamp': time.time(),
                        'read_bytes': disk_io.read_bytes,
                        'write_bytes': disk_io.write_bytes,
                        'read_count': disk_io.read_count,
                        'write_count': disk_io.write_count
                    })
                
                # Network IO
                net_io = psutil.net_io_counters()
                if net_io:
                    self.network_io_samples.append({
                        'timestamp': time.time(),
                        'bytes_sent': net_io.bytes_sent,
                        'bytes_recv': net_io.bytes_recv,
                        'packets_sent': net_io.packets_sent,
                        'packets_recv': net_io.packets_recv
                    })
                    
                await asyncio.sleep(interval)
                
            except Exception as e:
                print(f"Error monitoring resources: {e}")
                await asyncio.sleep(interval)
                
    def stop_monitoring(self):
        """Stop monitoring system resources."""
        self.monitoring = False
        
    def get_stats(self) -> Dict[str, Any]:
        """Get resource usage statistics."""
        stats = {}
        
        if self.cpu_samples:
            cpu_values = [s['cpu_percent'] for s in self.cpu_samples]
            stats['cpu'] = {
                'mean': statistics.mean(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values),
                'samples': len(cpu_values)
            }
            
        if self.memory_samples:
            memory_values = [s['memory_percent'] for s in self.memory_samples]
            memory_mb_values = [s['memory_used_mb'] for s in self.memory_samples]
            stats['memory'] = {
                'mean_percent': statistics.mean(memory_values),
                'max_percent': max(memory_values),
                'mean_mb': statistics.mean(memory_mb_values),
                'max_mb': max(memory_mb_values),
                'samples': len(memory_values)
            }
            
        return stats


# Predefined Load Test Scenarios

def get_smoke_test_config() -> LoadTestConfig:
    """Quick smoke test to verify basic functionality."""
    return LoadTestConfig(
        name="Smoke Test",
        duration_seconds=30,
        concurrent_users=2,
        requests_per_user=5,
        ramp_up_seconds=5,
        endpoints=['/health', '/api/v1/streamers'],
        think_time_ms=1000
    )


def get_normal_load_config() -> LoadTestConfig:
    """Normal expected load scenario."""
    return LoadTestConfig(
        name="Normal Load",
        duration_seconds=300,  # 5 minutes
        concurrent_users=10,
        requests_per_user=30,
        ramp_up_seconds=60,
        endpoints=[
            '/api/v1/streamers',
            '/api/v1/streamers/available',
            '/api/v1/user/status',
            '/api/v1/assignments'
        ],
        think_time_ms=2000
    )


def get_peak_load_config() -> LoadTestConfig:
    """Peak load scenario."""
    return LoadTestConfig(
        name="Peak Load",
        duration_seconds=600,  # 10 minutes
        concurrent_users=50,
        requests_per_user=60,
        ramp_up_seconds=120,
        endpoints=[
            '/api/v1/streamers',
            '/api/v1/streamers/available',
            '/api/v1/user/status',
            '/api/v1/assignments',
            '/api/v1/streamers/request-new'
        ],
        think_time_ms=1000
    )


def get_stress_test_config() -> LoadTestConfig:
    """Stress test to find system limits."""
    return LoadTestConfig(
        name="Stress Test",
        duration_seconds=900,  # 15 minutes
        concurrent_users=100,
        requests_per_user=100,
        ramp_up_seconds=300,
        endpoints=[
            '/api/v1/streamers',
            '/api/v1/streamers/available',
            '/api/v1/user/status',
            '/api/v1/assignments',
            '/api/v1/streamers/request-new'
        ],
        think_time_ms=500
    )


async def run_all_scenarios():
    """Run all predefined load test scenarios."""
    scenarios = [
        get_smoke_test_config(),
        get_normal_load_config(),
        get_peak_load_config(),
        get_stress_test_config()
    ]
    
    runner = LoadTestRunner()
    db_tester = DatabaseLoadTester("postgresql://localhost/test")
    
    results = []
    
    for scenario in scenarios:
        print(f"\n{'='*50}")
        print(f"Running scenario: {scenario.name}")
        print(f"{'='*50}")
        
        try:
            # Run API load test
            api_report = await runner.run_load_test(scenario)
            
            # Run database load test
            db_results = await db_tester.test_database_under_load(
                concurrent_connections=scenario.concurrent_users // 2,
                queries_per_connection=scenario.requests_per_user // 2
            )
            
            results.append({
                'scenario': scenario.name,
                'api_report': api_report,
                'database_results': db_results
            })
            
            # Print summary
            print(f"\nAPI Test Results:")
            print(f"  Total Requests: {api_report.total_requests}")
            print(f"  Success Rate: {api_report.successful_requests/api_report.total_requests*100:.1f}%")
            print(f"  Avg Response Time: {api_report.average_response_time*1000:.1f}ms")
            print(f"  P95 Response Time: {api_report.p95_response_time*1000:.1f}ms")
            print(f"  Requests/Second: {api_report.requests_per_second:.1f}")
            
            print(f"\nDatabase Test Results:")
            print(f"  Total Queries: {db_results['total_queries']}")
            print(f"  Success Rate: {db_results['successful_queries']/db_results['total_queries']*100:.1f}%")
            print(f"  Avg Execution Time: {db_results['average_execution_time']*1000:.1f}ms")
            print(f"  P95 Execution Time: {db_results['p95_execution_time']*1000:.1f}ms")
            print(f"  Queries/Second: {db_results['queries_per_second']:.1f}")
            
        except Exception as e:
            print(f"Scenario {scenario.name} failed: {e}")
            
        # Brief pause between scenarios
        await asyncio.sleep(10)
        
    return results


if __name__ == "__main__":
    # Run all load test scenarios
    print("Starting comprehensive load testing...")
    results = asyncio.run(run_all_scenarios())
    print(f"\nCompleted {len(results)} test scenarios.")
    print("Check the load_test_results_* directories for detailed reports.")