"""
Advanced Scheduler for 24/7 Twitch Scraper Automation
Uses APScheduler for robust background task scheduling
"""

import asyncio
import logging
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import Async<PERSON>Executor

# Add the parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from app.scraper.background_tasks import trigger_scrape_task
from app.config import get_settings

logger = logging.getLogger(__name__)

class TwitchScraperScheduler:
    """Advanced scheduler for automated Twitch scraping"""
    
    def __init__(self):
        self.settings = get_settings()
        self.scheduler = None
        self.is_running = False
        
        # Configure APScheduler
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': True,  # Combine multiple pending executions into one
            'max_instances': 1,  # Only one instance of each job at a time
            'misfire_grace_time': 300  # 5 minutes grace period for missed jobs
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='UTC'
        )
        
        # Add event listeners
        self.scheduler.add_listener(self._job_executed, 'EVENT_JOB_EXECUTED')
        self.scheduler.add_listener(self._job_error, 'EVENT_JOB_ERROR')
        self.scheduler.add_listener(self._job_missed, 'EVENT_JOB_MISSED')
    
    async def start(self):
        """Start the scheduler with default jobs"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        logger.info("🚀 Starting Twitch Scraper Scheduler...")
        
        try:
            # Add default scraping jobs
            await self.add_daily_scrape_job()
            await self.add_health_check_job()
            
            # Start the scheduler
            self.scheduler.start()
            self.is_running = True
            
            logger.info("✅ Scheduler started successfully!")
            logger.info("📋 Active jobs:")
            for job in self.scheduler.get_jobs():
                logger.info(f"   - {job.id}: {job.next_run_time}")
                
        except Exception as e:
            logger.error(f"❌ Failed to start scheduler: {e}")
            raise
    
    async def stop(self):
        """Stop the scheduler"""
        if not self.is_running:
            return
        
        logger.info("🛑 Stopping scheduler...")
        self.scheduler.shutdown(wait=True)
        self.is_running = False
        logger.info("✅ Scheduler stopped")
    
    async def add_daily_scrape_job(self, hour: int = 3, minute: int = 0):
        """Add daily scraping job at specified time (default 3:00 AM UTC)"""
        trigger = CronTrigger(hour=hour, minute=minute, timezone='UTC')
        
        self.scheduler.add_job(
            func=self._run_scrape_with_logging,
            trigger=trigger,
            id='daily_scrape',
            name='Daily Twitch Scraper',
            replace_existing=True
        )
        
        logger.info(f"📅 Daily scrape job scheduled for {hour:02d}:{minute:02d} UTC")
    
    async def add_interval_scrape_job(self, hours: int = 12):
        """Add interval-based scraping job"""
        trigger = IntervalTrigger(hours=hours)
        
        self.scheduler.add_job(
            func=self._run_scrape_with_logging,
            trigger=trigger,
            id='interval_scrape',
            name=f'Interval Scraper ({hours}h)',
            replace_existing=True
        )
        
        logger.info(f"⏰ Interval scrape job scheduled every {hours} hours")
    
    async def add_health_check_job(self):
        """Add health check job every 30 minutes"""
        trigger = IntervalTrigger(minutes=30)
        
        self.scheduler.add_job(
            func=self._health_check,
            trigger=trigger,
            id='health_check',
            name='System Health Check',
            replace_existing=True
        )
        
        logger.info("🏥 Health check job scheduled every 30 minutes")
    
    async def trigger_manual_scrape(self):
        """Manually trigger a scrape job"""
        logger.info("🔧 Manual scrape triggered")
        await self._run_scrape_with_logging()
    
    async def _run_scrape_with_logging(self):
        """Run scrape task with comprehensive logging"""
        start_time = datetime.utcnow()
        logger.info("=" * 60)
        logger.info("🤖 AUTOMATED SCRAPE STARTED")
        logger.info(f"⏰ Start Time: {start_time.isoformat()}")
        logger.info("=" * 60)
        
        try:
            # Run the scraper
            await trigger_scrape_task()
            
            end_time = datetime.utcnow()
            duration = end_time - start_time
            
            logger.info("=" * 60)
            logger.info("✅ AUTOMATED SCRAPE COMPLETED")
            logger.info(f"⏰ End Time: {end_time.isoformat()}")
            logger.info(f"⏱️ Duration: {duration.total_seconds():.2f} seconds")
            logger.info("=" * 60)
            
        except Exception as e:
            end_time = datetime.utcnow()
            duration = end_time - start_time
            
            logger.error("=" * 60)
            logger.error("❌ AUTOMATED SCRAPE FAILED")
            logger.error(f"⏰ End Time: {end_time.isoformat()}")
            logger.error(f"⏱️ Duration: {duration.total_seconds():.2f} seconds")
            logger.error(f"💥 Error: {str(e)}")
            logger.error("=" * 60)
            raise
    
    async def _health_check(self):
        """Perform system health check"""
        try:
            logger.info("🏥 Performing health check...")
            
            # Check if scraper is responsive
            # Add more health checks as needed
            
            logger.info("✅ Health check passed")
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
    
    def _job_executed(self, event):
        """Handle job execution events"""
        logger.info(f"✅ Job '{event.job_id}' executed successfully")
    
    def _job_error(self, event):
        """Handle job error events"""
        logger.error(f"❌ Job '{event.job_id}' failed: {event.exception}")
    
    def _job_missed(self, event):
        """Handle missed job events"""
        logger.warning(f"⚠️ Job '{event.job_id}' missed execution at {event.scheduled_run_time}")
    
    def get_job_status(self):
        """Get status of all scheduled jobs"""
        if not self.is_running:
            return {"status": "stopped", "jobs": []}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "status": "running",
            "jobs": jobs,
            "total_jobs": len(jobs)
        }
    
    async def reschedule_daily_job(self, hour: int, minute: int = 0):
        """Reschedule the daily scraping job"""
        if self.scheduler.get_job('daily_scrape'):
            self.scheduler.remove_job('daily_scrape')
        
        await self.add_daily_scrape_job(hour, minute)
        logger.info(f"🔄 Daily scrape rescheduled to {hour:02d}:{minute:02d} UTC")

# Global scheduler instance
automation_scheduler = TwitchScraperScheduler()

async def start_automation():
    """Start the automation scheduler"""
    await automation_scheduler.start()

async def stop_automation():
    """Stop the automation scheduler"""
    await automation_scheduler.stop()

def get_automation_status():
    """Get automation status"""
    return automation_scheduler.get_job_status()

async def trigger_manual_scrape():
    """Trigger manual scrape"""
    await automation_scheduler.trigger_manual_scrape()

# CLI interface for testing
async def main():
    """Main function for testing the scheduler"""
    import signal
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Start scheduler
    await start_automation()
    
    # Handle shutdown gracefully
    def signal_handler(signum, frame):
        logger.info("Received shutdown signal")
        asyncio.create_task(stop_automation())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Keep running
        while automation_scheduler.is_running:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    finally:
        await stop_automation()

if __name__ == "__main__":
    asyncio.run(main())
