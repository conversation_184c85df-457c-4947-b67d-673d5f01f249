import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, call

def test_update_assignment(client: TestClient):
    """
    Test updating an assignment.
    """
    with patch("app.services.assignment_service.update_assignment") as mock_update:
        mock_update.return_value = {"id": 1, "status": "completed"}
        response = client.patch("/api/v1/assignments/1", json={"status": "completed"})
        assert response.status_code == 200
        assert response.json()["status"] == "completed"
        mock_update.assert_called_with(assignment_id=1, data={"status": "completed"})

def test_delete_assignment(client: TestClient):
    """
    Test deleting an assignment.
    """
    with patch("app.services.assignment_service.delete_assignment") as mock_delete:
        mock_delete.return_value = True
        response = client.delete("/api/v1/assignments/1")
        assert response.status_code == 204
        mock_delete.assert_called_with(assignment_id=1)

def test_assignment_rate_limiting(client: TestClient):
    """
    Test rate limiting on assignment endpoints.
    """
    with patch("app.services.assignment_service.create_assignment") as mock_create:
        mock_create.return_value = {"id": 1, "status": "pending"}
        for i in range(10):
            response = client.post("/api/v1/assignments", json={"streamer_id": i, "user_id": 1})
            assert response.status_code in [200, 429]
        
        assert response.status_code == 429