"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_dashboard_UserStatus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/UserStatus */ \"(app-pages-browser)/./src/components/dashboard/UserStatus.tsx\");\n/* harmony import */ var _components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/StreamerList */ \"(app-pages-browser)/./src/components/dashboard/StreamerList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AssignmentList */ \"(app-pages-browser)/./src/components/dashboard/AssignmentList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/AssignmentAnalytics */ \"(app-pages-browser)/./src/components/dashboard/AssignmentAnalytics.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [streamers, setStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStatus, setUserStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [backendConnected, setBackendConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const API_BASE_URL = \"/api\";\n    // Fetch initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStats();\n        fetchUserStatus();\n        fetchAssignments();\n    }, []);\n    const fetchUserStatus = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/user/status\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUserStatus(data);\n            } else {\n                console.error(\"Failed to fetch user status - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch user status - Connection error:\", err);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            console.log(\"Fetching stats using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getStreamerStats();\n            setStats({\n                total_streamers: data.total_streamers,\n                live_streamers: data.live_streamers,\n                available_streamers: data.available_streamers,\n                average_followers: 25 // Use static value since backend doesn't calculate this\n            });\n            setBackendConnected(true);\n            console.log(\"Stats fetched successfully:\", data);\n        } catch (err) {\n            console.error(\"Failed to fetch stats - Connection error:\", err);\n            setBackendConnected(false);\n            // Set default stats if backend is not available\n            setStats({\n                total_streamers: 0,\n                live_streamers: 0,\n                available_streamers: 0,\n                average_followers: 0\n            });\n        }\n    };\n    const fetchNewStreamers = async ()=>{\n        console.log(\"=== FETCH NEW STREAMERS BUTTON CLICKED ===\");\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching from:\", \"\".concat(API_BASE_URL, \"/streamers/available\"));\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/streamers/available\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log(\"Response status:\", response.status);\n            if (response.status === 429) {\n                throw new Error(\"Daily request limit exceeded. You can make 3 requests per day. Try again tomorrow.\");\n            }\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch streamers: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log(\"Received data:\", data);\n            // Ensure data is an array and has streamers property\n            const streamersArray = Array.isArray(data) ? data : (data === null || data === void 0 ? void 0 : data.streamers) || [];\n            console.log(\"Streamers array:\", streamersArray);\n            setStreamers(streamersArray.slice(0, 50)); // Limit to 50 streamers\n            // Refresh stats and user status after fetching streamers\n            await fetchStats();\n            await fetchUserStatus();\n            alert(\"Success! Loaded \".concat(streamersArray.length, \" streamers and created assignments. This counts as 1 of your 3 daily requests.\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to fetch streamers\";\n            setError(errorMessage);\n            console.error(\"=== ERROR FETCHING STREAMERS ===\", err);\n            alert(\"Error: \" + errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAssignments = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAssignments(data.assignments);\n            } else {\n                console.error(\"Failed to fetch assignments - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch assignments - Connection error:\", err);\n        }\n    };\n    const handleUpdateAssignment = async (id, status, notes)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status,\n                    notes\n                })\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to update assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to update assignment - Connection error:\", err);\n        }\n    };\n    const handleDeleteAssignment = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to delete assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to delete assignment - Connection error:\", err);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Please sign in to access the dashboard.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 192,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Agent Dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-6\",\n                children: [\n                    \"Welcome back, \",\n                    (user === null || user === void 0 ? void 0 : user.name) || \"Agent\",\n                    \"! Manage your streamer outreach campaigns.\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_UserStatus__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Total Streamers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-green-600\",\n                                children: (stats === null || stats === void 0 ? void 0 : stats.total_streamers) || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: \"In database\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-purple-600\",\n                                children: (stats === null || stats === void 0 ? void 0 : stats.available_streamers) || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: \"Ready for outreach\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            userStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 bg-blue-50 p-4 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold text-lg mb-2 text-blue-800\",\n                        children: \"\\uD83D\\uDCCA Your Daily Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: userStatus.daily_request_count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Requests Used\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: userStatus.remaining_requests\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Remaining\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-600\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Daily Limit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    userStatus.remaining_requests === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-red-100 border border-red-300 rounded text-red-700 text-sm\",\n                        children: [\n                            \"⚠️ You've reached your daily limit of 3 requests. Reset time: \",\n                            new Date(userStatus.next_reset_time).toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 bg-gray-100 p-4 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold text-lg mb-2\",\n                        children: \"\\uD83D\\uDD27 Debug Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Streamers Count: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: streamers.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Assignments Count: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: assignments.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 35\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Loading: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: loading ? \"Yes\" : \"No\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Error: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: error || \"None\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Backend Connected: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: backendConnected ? \"Yes\" : \"No\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 35\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Stats: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: stats ? \"Loaded\" : \"None\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"User Status: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: userStatus ? \"Loaded\" : \"None\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>alert(\"\\uD83C\\uDF89 Test button works! React is working properly.\"),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium mr-4\",\n                        children: \"\\uD83E\\uDDEA Test Button\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchNewStreamers,\n                        disabled: loading || (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0,\n                        className: \"px-6 py-3 rounded-lg font-medium text-white \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"bg-gray-400 cursor-not-allowed\" : \"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400\"),\n                        children: loading ? \"Fetching...\" : (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"❌ Daily Limit Reached\" : \"\\uD83C\\uDFAF Get New Streamers\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"Click to fetch available streamers for outreach\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: [\n                                \"Error: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                streamers: streamers,\n                assignments: [],\n                onInterestLevelChanged: ()=>{}\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    assignments: assignments\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    assignments: assignments,\n                    onUpdate: handleUpdateAssignment,\n                    onDelete: handleDeleteAssignment\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"API Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 rounded-full mr-2 \".concat(backendConnected === true ? \"bg-green-500\" : backendConnected === false ? \"bg-red-500\" : \"bg-yellow-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, this),\n                                            backendConnected === true ? \"Backend Connected\" : backendConnected === false ? \"Backend Disconnected\" : \"Checking Connection...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: backendConnected === true ? \"FastAPI server running successfully\" : backendConnected === false ? \"Cannot connect to FastAPI server\" : \"Testing backend connection...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"API URL: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono text-sm\",\n                                                children: API_BASE_URL\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 24\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: [\n                                            \"API Documentation: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"http://127.0.0.1:8000/docs\",\n                                                target: \"_blank\",\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"http://127.0.0.1:8000/docs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 34\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this),\n                                    backendConnected === false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-800 text-sm\",\n                                            children: [\n                                                \"⚠️ Backend server not running. Please start it with: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 72\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-2 py-1 rounded text-xs mt-1 inline-block\",\n                                                    children: \"cd backend && uvicorn app.main:app --reload\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"pw5yoFpg+6vzMVV4FBiH157mCdM=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\nclass ApiClient {\n    async makeRequest(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        // Get auth token from Clerk\n        const token = await this.getAuthToken();\n        const defaultHeaders = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            defaultHeaders[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const config = {\n            ...options,\n            headers: {\n                ...defaultHeaders,\n                ...options.headers\n            }\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        detail: \"Unknown error\"\n                    }));\n                throw new Error(errorData.detail || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            throw error;\n        }\n    }\n    async getAuthToken() {\n        // This will be implemented with Clerk integration\n        // For now, return null (unauthenticated requests)\n        return null;\n    }\n    // Streamer API methods\n    async getAvailableStreamers() {\n        const backendStreamers = await this.makeRequest(\"/fallback/available\");\n        // Transform backend data to frontend format\n        const streamers = backendStreamers.map((streamer)=>({\n                twitch_user_id: streamer.twitch_user_id,\n                username: streamer.username,\n                display_name: streamer.display_name,\n                follower_count: streamer.follower_count,\n                is_live: streamer.is_live,\n                current_game: streamer.current_game,\n                stream_title: streamer.stream_title,\n                profile_image_url: streamer.thumbnail_url || \"https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png\",\n                language: streamer.language,\n                last_seen_live_at: streamer.last_seen_live_at,\n                twitch_url: \"https://twitch.tv/\".concat(streamer.username)\n            }));\n        return {\n            streamers,\n            total: streamers.length,\n            user_status: {\n                daily_requests_used: 1,\n                daily_requests_remaining: 2,\n                can_make_request: true\n            }\n        };\n    }\n    async getStreamerStats() {\n        const backendStats = await this.makeRequest(\"/fallback/stats\");\n        return {\n            total_streamers: backendStats.total_streamers,\n            live_streamers: backendStats.live_streamers,\n            available_streamers: backendStats.available_streamers,\n            last_updated: new Date().toISOString()\n        };\n    }\n    async triggerScrape() {\n        return this.makeRequest(\"/api/v1/streamers/trigger-scrape\", {\n            method: \"POST\"\n        });\n    }\n    async getScraperStatus() {\n        return this.makeRequest(\"/api/v1/streamers/scraper-status\");\n    }\n    // Assignment API methods\n    async getUserAssignments() {\n        return this.makeRequest(\"/api/v1/assignments/\");\n    }\n    async updateAssignmentStatus(assignmentId, status, notes) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId, \"/status\"), {\n            method: \"PATCH\",\n            body: JSON.stringify({\n                status,\n                notes\n            })\n        });\n    }\n    async deleteAssignment(assignmentId) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId), {\n            method: \"DELETE\"\n        });\n    }\n    // Health check\n    async healthCheck() {\n        return this.makeRequest(\"/api/v1/health\");\n    }\n    constructor(){\n        this.baseURL = API_BASE_URL;\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxlQUFlQyw4QkFBb0MsSUFBSTtBQTRDN0QsTUFBTUc7SUFPSixNQUFjQyxZQUNaQyxRQUFnQixFQUVKO1lBRFpDLFVBQUFBLGlFQUF1QixDQUFDO1FBRXhCLE1BQU1DLE1BQU0sR0FBa0JGLE9BQWYsSUFBSSxDQUFDRyxPQUFPLEVBQVksT0FBVEg7UUFFOUIsNEJBQTRCO1FBQzVCLE1BQU1JLFFBQVEsTUFBTSxJQUFJLENBQUNDLFlBQVk7UUFFckMsTUFBTUMsaUJBQThCO1lBQ2xDLGdCQUFnQjtRQUNsQjtRQUVBLElBQUlGLE9BQU87WUFDVEUsY0FBYyxDQUFDLGdCQUFnQixHQUFHLFVBQWdCLE9BQU5GO1FBQzlDO1FBRUEsTUFBTUcsU0FBc0I7WUFDMUIsR0FBR04sT0FBTztZQUNWTyxTQUFTO2dCQUNQLEdBQUdGLGNBQWM7Z0JBQ2pCLEdBQUdMLFFBQVFPLE9BQU87WUFDcEI7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU1SLEtBQUtLO1lBRWxDLElBQUksQ0FBQ0UsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU1ILFNBQVNJLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU87d0JBQUVDLFFBQVE7b0JBQWdCO2dCQUMvRSxNQUFNLElBQUlDLE1BQU1KLFVBQVVHLE1BQU0sSUFBSSxRQUE0Qk4sT0FBcEJBLFNBQVNRLE1BQU0sRUFBQyxNQUF3QixPQUFwQlIsU0FBU1MsVUFBVTtZQUNyRjtZQUVBLE9BQU8sTUFBTVQsU0FBU0ksSUFBSTtRQUM1QixFQUFFLE9BQU9NLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUF1QkE7WUFDckMsTUFBTUE7UUFDUjtJQUNGO0lBRUEsTUFBY2QsZUFBdUM7UUFDbkQsa0RBQWtEO1FBQ2xELGtEQUFrRDtRQUNsRCxPQUFPO0lBQ1Q7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTWdCLHdCQUF1RDtRQUMzRCxNQUFNQyxtQkFBbUIsTUFBTSxJQUFJLENBQUN2QixXQUFXLENBQVE7UUFFdkQsNENBQTRDO1FBQzVDLE1BQU13QixZQUF3QkQsaUJBQWlCRSxHQUFHLENBQUMsQ0FBQ0MsV0FBbUI7Z0JBQ3JFQyxnQkFBZ0JELFNBQVNDLGNBQWM7Z0JBQ3ZDQyxVQUFVRixTQUFTRSxRQUFRO2dCQUMzQkMsY0FBY0gsU0FBU0csWUFBWTtnQkFDbkNDLGdCQUFnQkosU0FBU0ksY0FBYztnQkFDdkNDLFNBQVNMLFNBQVNLLE9BQU87Z0JBQ3pCQyxjQUFjTixTQUFTTSxZQUFZO2dCQUNuQ0MsY0FBY1AsU0FBU08sWUFBWTtnQkFDbkNDLG1CQUFtQlIsU0FBU1MsYUFBYSxJQUFJO2dCQUM3Q0MsVUFBVVYsU0FBU1UsUUFBUTtnQkFDM0JDLG1CQUFtQlgsU0FBU1csaUJBQWlCO2dCQUM3Q0MsWUFBWSxxQkFBdUMsT0FBbEJaLFNBQVNFLFFBQVE7WUFDcEQ7UUFFQSxPQUFPO1lBQ0xKO1lBQ0FlLE9BQU9mLFVBQVVnQixNQUFNO1lBQ3ZCQyxhQUFhO2dCQUNYQyxxQkFBcUI7Z0JBQ3JCQywwQkFBMEI7Z0JBQzFCQyxrQkFBa0I7WUFDcEI7UUFDRjtJQUNGO0lBRUEsTUFBTUMsbUJBQTJDO1FBQy9DLE1BQU1DLGVBQWUsTUFBTSxJQUFJLENBQUM5QyxXQUFXLENBQU07UUFFakQsT0FBTztZQUNMK0MsaUJBQWlCRCxhQUFhQyxlQUFlO1lBQzdDQyxnQkFBZ0JGLGFBQWFFLGNBQWM7WUFDM0NDLHFCQUFxQkgsYUFBYUcsbUJBQW1CO1lBQ3JEQyxjQUFjLElBQUlDLE9BQU9DLFdBQVc7UUFDdEM7SUFDRjtJQUVBLE1BQU1DLGdCQUFvRjtRQUN4RixPQUFPLElBQUksQ0FBQ3JELFdBQVcsQ0FBQyxvQ0FBb0M7WUFDMURzRCxRQUFRO1FBQ1Y7SUFDRjtJQUVBLE1BQU1DLG1CQUFpQztRQUNyQyxPQUFPLElBQUksQ0FBQ3ZELFdBQVcsQ0FBQztJQUMxQjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNd0QscUJBQTRDO1FBQ2hELE9BQU8sSUFBSSxDQUFDeEQsV0FBVyxDQUFlO0lBQ3hDO0lBRUEsTUFBTXlELHVCQUNKQyxZQUFvQixFQUNwQnhDLE1BQWMsRUFDZHlDLEtBQWMsRUFDMkQ7UUFDekUsT0FBTyxJQUFJLENBQUMzRCxXQUFXLENBQUMsdUJBQW9DLE9BQWIwRCxjQUFhLFlBQVU7WUFDcEVKLFFBQVE7WUFDUk0sTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFNUM7Z0JBQVF5QztZQUFNO1FBQ3ZDO0lBQ0Y7SUFFQSxNQUFNSSxpQkFBaUJMLFlBQW9CLEVBQXVEO1FBQ2hHLE9BQU8sSUFBSSxDQUFDMUQsV0FBVyxDQUFDLHVCQUFvQyxPQUFiMEQsZUFBZ0I7WUFDN0RKLFFBQVE7UUFDVjtJQUNGO0lBRUEsZUFBZTtJQUNmLE1BQU1VLGNBQThEO1FBQ2xFLE9BQU8sSUFBSSxDQUFDaEUsV0FBVyxDQUFDO0lBQzFCO0lBOUhBaUUsYUFBYztRQUNaLElBQUksQ0FBQzdELE9BQU8sR0FBR1Q7SUFDakI7QUE2SEY7QUFFTyxNQUFNdUUsWUFBWSxJQUFJbkUsWUFBWTtBQUN6QywrREFBZW1FLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9hcGkudHM/MmZhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBBUElfQkFTRV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hcGkvdjEnO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBTdHJlYW1lciB7XHJcbiAgdHdpdGNoX3VzZXJfaWQ6IHN0cmluZztcclxuICB1c2VybmFtZTogc3RyaW5nO1xyXG4gIGRpc3BsYXlfbmFtZTogc3RyaW5nO1xyXG4gIGZvbGxvd2VyX2NvdW50OiBudW1iZXI7XHJcbiAgaXNfbGl2ZTogYm9vbGVhbjtcclxuICBjdXJyZW50X2dhbWU/OiBzdHJpbmc7XHJcbiAgc3RyZWFtX3RpdGxlPzogc3RyaW5nO1xyXG4gIHByb2ZpbGVfaW1hZ2VfdXJsPzogc3RyaW5nO1xyXG4gIGxhbmd1YWdlOiBzdHJpbmc7XHJcbiAgbGFzdF9zZWVuX2xpdmVfYXQ/OiBzdHJpbmc7XHJcbiAgdHdpdGNoX3VybD86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBTdHJlYW1lckxpc3RSZXNwb25zZSB7XHJcbiAgc3RyZWFtZXJzOiBTdHJlYW1lcltdO1xyXG4gIHRvdGFsOiBudW1iZXI7XHJcbiAgdXNlcl9zdGF0dXM6IHtcclxuICAgIGRhaWx5X3JlcXVlc3RzX3VzZWQ6IG51bWJlcjtcclxuICAgIGRhaWx5X3JlcXVlc3RzX3JlbWFpbmluZzogbnVtYmVyO1xyXG4gICAgY2FuX21ha2VfcmVxdWVzdDogYm9vbGVhbjtcclxuICB9O1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFN0cmVhbWVyU3RhdHMge1xyXG4gIHRvdGFsX3N0cmVhbWVyczogbnVtYmVyO1xyXG4gIGxpdmVfc3RyZWFtZXJzOiBudW1iZXI7XHJcbiAgYXZhaWxhYmxlX3N0cmVhbWVyczogbnVtYmVyO1xyXG4gIGxhc3RfdXBkYXRlZDogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEFzc2lnbm1lbnQge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgc3RyZWFtZXJfaWQ6IHN0cmluZztcclxuICBzdHJlYW1lcl91c2VybmFtZTogc3RyaW5nO1xyXG4gIHN0cmVhbWVyX2Rpc3BsYXlfbmFtZTogc3RyaW5nO1xyXG4gIHN0YXR1czogc3RyaW5nO1xyXG4gIGFzc2lnbmVkX2F0OiBzdHJpbmc7XHJcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIG5vdGVzPzogc3RyaW5nO1xyXG59XHJcblxyXG5jbGFzcyBBcGlDbGllbnQge1xyXG4gIHByaXZhdGUgYmFzZVVSTDogc3RyaW5nO1xyXG5cclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHRoaXMuYmFzZVVSTCA9IEFQSV9CQVNFX1VSTDtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgYXN5bmMgbWFrZVJlcXVlc3Q8VD4oXHJcbiAgICBlbmRwb2ludDogc3RyaW5nLFxyXG4gICAgb3B0aW9uczogUmVxdWVzdEluaXQgPSB7fVxyXG4gICk6IFByb21pc2U8VD4ge1xyXG4gICAgY29uc3QgdXJsID0gYCR7dGhpcy5iYXNlVVJMfSR7ZW5kcG9pbnR9YDtcclxuICAgIFxyXG4gICAgLy8gR2V0IGF1dGggdG9rZW4gZnJvbSBDbGVya1xyXG4gICAgY29uc3QgdG9rZW4gPSBhd2FpdCB0aGlzLmdldEF1dGhUb2tlbigpO1xyXG4gICAgXHJcbiAgICBjb25zdCBkZWZhdWx0SGVhZGVyczogSGVhZGVyc0luaXQgPSB7XHJcbiAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICB9O1xyXG5cclxuICAgIGlmICh0b2tlbikge1xyXG4gICAgICBkZWZhdWx0SGVhZGVyc1snQXV0aG9yaXphdGlvbiddID0gYEJlYXJlciAke3Rva2VufWA7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY29uZmlnOiBSZXF1ZXN0SW5pdCA9IHtcclxuICAgICAgLi4ub3B0aW9ucyxcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIC4uLmRlZmF1bHRIZWFkZXJzLFxyXG4gICAgICAgIC4uLm9wdGlvbnMuaGVhZGVycyxcclxuICAgICAgfSxcclxuICAgIH07XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIGNvbmZpZyk7XHJcbiAgICAgIFxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7IGRldGFpbDogJ1Vua25vd24gZXJyb3InIH0pKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmRldGFpbCB8fCBgSFRUUCAke3Jlc3BvbnNlLnN0YXR1c306ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSSByZXF1ZXN0IGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBhc3luYyBnZXRBdXRoVG9rZW4oKTogUHJvbWlzZTxzdHJpbmcgfCBudWxsPiB7XHJcbiAgICAvLyBUaGlzIHdpbGwgYmUgaW1wbGVtZW50ZWQgd2l0aCBDbGVyayBpbnRlZ3JhdGlvblxyXG4gICAgLy8gRm9yIG5vdywgcmV0dXJuIG51bGwgKHVuYXV0aGVudGljYXRlZCByZXF1ZXN0cylcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxuXHJcbiAgLy8gU3RyZWFtZXIgQVBJIG1ldGhvZHNcclxuICBhc3luYyBnZXRBdmFpbGFibGVTdHJlYW1lcnMoKTogUHJvbWlzZTxTdHJlYW1lckxpc3RSZXNwb25zZT4ge1xyXG4gICAgY29uc3QgYmFja2VuZFN0cmVhbWVycyA9IGF3YWl0IHRoaXMubWFrZVJlcXVlc3Q8YW55W10+KCcvZmFsbGJhY2svYXZhaWxhYmxlJyk7XHJcbiAgICBcclxuICAgIC8vIFRyYW5zZm9ybSBiYWNrZW5kIGRhdGEgdG8gZnJvbnRlbmQgZm9ybWF0XHJcbiAgICBjb25zdCBzdHJlYW1lcnM6IFN0cmVhbWVyW10gPSBiYWNrZW5kU3RyZWFtZXJzLm1hcCgoc3RyZWFtZXI6IGFueSkgPT4gKHtcclxuICAgICAgdHdpdGNoX3VzZXJfaWQ6IHN0cmVhbWVyLnR3aXRjaF91c2VyX2lkLFxyXG4gICAgICB1c2VybmFtZTogc3RyZWFtZXIudXNlcm5hbWUsXHJcbiAgICAgIGRpc3BsYXlfbmFtZTogc3RyZWFtZXIuZGlzcGxheV9uYW1lLFxyXG4gICAgICBmb2xsb3dlcl9jb3VudDogc3RyZWFtZXIuZm9sbG93ZXJfY291bnQsXHJcbiAgICAgIGlzX2xpdmU6IHN0cmVhbWVyLmlzX2xpdmUsXHJcbiAgICAgIGN1cnJlbnRfZ2FtZTogc3RyZWFtZXIuY3VycmVudF9nYW1lLFxyXG4gICAgICBzdHJlYW1fdGl0bGU6IHN0cmVhbWVyLnN0cmVhbV90aXRsZSxcclxuICAgICAgcHJvZmlsZV9pbWFnZV91cmw6IHN0cmVhbWVyLnRodW1ibmFpbF91cmwgfHwgJ2h0dHBzOi8vc3RhdGljLWNkbi5qdHZudy5uZXQvanR2X3VzZXJfcGljdHVyZXMvZGVmYXVsdC1wcm9maWxlX2ltYWdlLTMwMHgzMDAucG5nJyxcclxuICAgICAgbGFuZ3VhZ2U6IHN0cmVhbWVyLmxhbmd1YWdlLFxyXG4gICAgICBsYXN0X3NlZW5fbGl2ZV9hdDogc3RyZWFtZXIubGFzdF9zZWVuX2xpdmVfYXQsXHJcbiAgICAgIHR3aXRjaF91cmw6IGBodHRwczovL3R3aXRjaC50di8ke3N0cmVhbWVyLnVzZXJuYW1lfWBcclxuICAgIH0pKTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdHJlYW1lcnMsXHJcbiAgICAgIHRvdGFsOiBzdHJlYW1lcnMubGVuZ3RoLFxyXG4gICAgICB1c2VyX3N0YXR1czoge1xyXG4gICAgICAgIGRhaWx5X3JlcXVlc3RzX3VzZWQ6IDEsXHJcbiAgICAgICAgZGFpbHlfcmVxdWVzdHNfcmVtYWluaW5nOiAyLFxyXG4gICAgICAgIGNhbl9tYWtlX3JlcXVlc3Q6IHRydWVcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldFN0cmVhbWVyU3RhdHMoKTogUHJvbWlzZTxTdHJlYW1lclN0YXRzPiB7XHJcbiAgICBjb25zdCBiYWNrZW5kU3RhdHMgPSBhd2FpdCB0aGlzLm1ha2VSZXF1ZXN0PGFueT4oJy9mYWxsYmFjay9zdGF0cycpO1xyXG4gICAgXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICB0b3RhbF9zdHJlYW1lcnM6IGJhY2tlbmRTdGF0cy50b3RhbF9zdHJlYW1lcnMsXHJcbiAgICAgIGxpdmVfc3RyZWFtZXJzOiBiYWNrZW5kU3RhdHMubGl2ZV9zdHJlYW1lcnMsXHJcbiAgICAgIGF2YWlsYWJsZV9zdHJlYW1lcnM6IGJhY2tlbmRTdGF0cy5hdmFpbGFibGVfc3RyZWFtZXJzLFxyXG4gICAgICBsYXN0X3VwZGF0ZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIGFzeW5jIHRyaWdnZXJTY3JhcGUoKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZzsgc3RhdHVzOiBzdHJpbmc7IHRyaWdnZXJlZF9ieTogc3RyaW5nIH0+IHtcclxuICAgIHJldHVybiB0aGlzLm1ha2VSZXF1ZXN0KCcvYXBpL3YxL3N0cmVhbWVycy90cmlnZ2VyLXNjcmFwZScsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldFNjcmFwZXJTdGF0dXMoKTogUHJvbWlzZTxhbnk+IHtcclxuICAgIHJldHVybiB0aGlzLm1ha2VSZXF1ZXN0KCcvYXBpL3YxL3N0cmVhbWVycy9zY3JhcGVyLXN0YXR1cycpO1xyXG4gIH1cclxuXHJcbiAgLy8gQXNzaWdubWVudCBBUEkgbWV0aG9kc1xyXG4gIGFzeW5jIGdldFVzZXJBc3NpZ25tZW50cygpOiBQcm9taXNlPEFzc2lnbm1lbnRbXT4ge1xyXG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3Q8QXNzaWdubWVudFtdPignL2FwaS92MS9hc3NpZ25tZW50cy8nKTtcclxuICB9XHJcblxyXG4gIGFzeW5jIHVwZGF0ZUFzc2lnbm1lbnRTdGF0dXMoXHJcbiAgICBhc3NpZ25tZW50SWQ6IHN0cmluZyxcclxuICAgIHN0YXR1czogc3RyaW5nLFxyXG4gICAgbm90ZXM/OiBzdHJpbmdcclxuICApOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nOyBhc3NpZ25tZW50X2lkOiBzdHJpbmc7IG5ld19zdGF0dXM6IHN0cmluZyB9PiB7XHJcbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdChgL2FwaS92MS9hc3NpZ25tZW50cy8ke2Fzc2lnbm1lbnRJZH0vc3RhdHVzYCwge1xyXG4gICAgICBtZXRob2Q6ICdQQVRDSCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgc3RhdHVzLCBub3RlcyB9KSxcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgZGVsZXRlQXNzaWdubWVudChhc3NpZ25tZW50SWQ6IHN0cmluZyk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmc7IGFzc2lnbm1lbnRfaWQ6IHN0cmluZyB9PiB7XHJcbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdChgL2FwaS92MS9hc3NpZ25tZW50cy8ke2Fzc2lnbm1lbnRJZH1gLCB7XHJcbiAgICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIC8vIEhlYWx0aCBjaGVja1xyXG4gIGFzeW5jIGhlYWx0aENoZWNrKCk6IFByb21pc2U8eyBzdGF0dXM6IHN0cmluZzsgdGltZXN0YW1wOiBzdHJpbmcgfT4ge1xyXG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3QoJy9hcGkvdjEvaGVhbHRoJyk7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgY29uc3QgYXBpQ2xpZW50ID0gbmV3IEFwaUNsaWVudCgpO1xyXG5leHBvcnQgZGVmYXVsdCBhcGlDbGllbnQ7Il0sIm5hbWVzIjpbIkFQSV9CQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwiLCJBcGlDbGllbnQiLCJtYWtlUmVxdWVzdCIsImVuZHBvaW50Iiwib3B0aW9ucyIsInVybCIsImJhc2VVUkwiLCJ0b2tlbiIsImdldEF1dGhUb2tlbiIsImRlZmF1bHRIZWFkZXJzIiwiY29uZmlnIiwiaGVhZGVycyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImVycm9yRGF0YSIsImpzb24iLCJjYXRjaCIsImRldGFpbCIsIkVycm9yIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsImVycm9yIiwiY29uc29sZSIsImdldEF2YWlsYWJsZVN0cmVhbWVycyIsImJhY2tlbmRTdHJlYW1lcnMiLCJzdHJlYW1lcnMiLCJtYXAiLCJzdHJlYW1lciIsInR3aXRjaF91c2VyX2lkIiwidXNlcm5hbWUiLCJkaXNwbGF5X25hbWUiLCJmb2xsb3dlcl9jb3VudCIsImlzX2xpdmUiLCJjdXJyZW50X2dhbWUiLCJzdHJlYW1fdGl0bGUiLCJwcm9maWxlX2ltYWdlX3VybCIsInRodW1ibmFpbF91cmwiLCJsYW5ndWFnZSIsImxhc3Rfc2Vlbl9saXZlX2F0IiwidHdpdGNoX3VybCIsInRvdGFsIiwibGVuZ3RoIiwidXNlcl9zdGF0dXMiLCJkYWlseV9yZXF1ZXN0c191c2VkIiwiZGFpbHlfcmVxdWVzdHNfcmVtYWluaW5nIiwiY2FuX21ha2VfcmVxdWVzdCIsImdldFN0cmVhbWVyU3RhdHMiLCJiYWNrZW5kU3RhdHMiLCJ0b3RhbF9zdHJlYW1lcnMiLCJsaXZlX3N0cmVhbWVycyIsImF2YWlsYWJsZV9zdHJlYW1lcnMiLCJsYXN0X3VwZGF0ZWQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJ0cmlnZ2VyU2NyYXBlIiwibWV0aG9kIiwiZ2V0U2NyYXBlclN0YXR1cyIsImdldFVzZXJBc3NpZ25tZW50cyIsInVwZGF0ZUFzc2lnbm1lbnRTdGF0dXMiLCJhc3NpZ25tZW50SWQiLCJub3RlcyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiZGVsZXRlQXNzaWdubWVudCIsImhlYWx0aENoZWNrIiwiY29uc3RydWN0b3IiLCJhcGlDbGllbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});