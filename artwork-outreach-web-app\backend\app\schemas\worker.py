from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class ScraperStatus(BaseModel):
    is_running: bool
    last_run_time: Optional[datetime]
    last_run_result: Optional[str]
    last_run_streamers_found: int


class ScraperRunBase(BaseModel):
    status: str
    streamers_found: int
    details: Optional[str] = None


class ScraperRunCreate(ScraperRunBase):
    pass


class ScraperRunRead(ScraperRunBase):
    id: str
    run_at: datetime

    model_config = {"from_attributes": True}