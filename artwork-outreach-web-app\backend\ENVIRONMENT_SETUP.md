# Environment Setup Guide

This guide explains how to set up the environment variables for the Artwork Outreach backend.

## Required Environment Variables

### 1. Twitch API Credentials
```bash
TWITCH_CLIENT_ID=******************************
TWITCH_CLIENT_SECRET=******************************
```

### 2. Supabase Configuration
```bash
SUPABASE_URL=https://wejiqonfxofwbiubqmpo.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indlamlxb25meG9md2JpdWJxbXBvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNzI5MjEsImV4cCI6MjA2Njk0ODkyMX0.J1JTDKJdxA5yR8gD3XtcUFyNdD3UJ2ofHTR2lZ_BkbQ
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indlamlxb25meG9md2JpdWJxbXBvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTM3MjkyMSwiZXhwIjoyMDY2OTQ4OTIxfQ.YNXeWA1bj9oHLgrfPqp3le8ajlikYatPOxGcB-iseks
```

### 3. Database URL
To get your database password:
1. Go to https://supabase.com/dashboard
2. Select your project: `wejiqonfxofwbiubqmpo`
3. Navigate to Settings → Database
4. Copy the connection string and replace `[YOUR_DB_PASSWORD]` with your actual password

```bash
DATABASE_URL=postgresql://postgres.wejiqonfxofwbiubqmpo:[YOUR_DB_PASSWORD]@aws-0-us-west-1.pooler.supabase.com:6543/postgres
```

### 4. Clerk Authentication
```bash
CLERK_SECRET_KEY=sk_test_Bl0KN8EsRbBnC5XUDGOEER0YNfI458pcixLlZBYN9Z
CLERK_PUBLISHABLE_KEY=pk_test_ZW5kbGVzcy1waXBlZmlzaC01OC5jbGVyay5hY2NvdW50cy5kZXYk
```

### 5. Additional Configuration
```bash
ENVIRONMENT=development
LOG_LEVEL=INFO
CORS_ORIGINS='["http://localhost:3000", "http://127.0.0.1:3000"]'
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key-for-jwt-signing-change-in-production
```

## Setup Instructions

1. Copy the provided `.env` file to your backend directory
2. Update the `DATABASE_URL` with your actual Supabase database password
3. Ensure Redis is running locally or update `REDIS_URL` to point to your Redis instance
4. Change the `SECRET_KEY` to a secure random string for production

## Security Notes

⚠️ **Important Security Considerations:**

1. **Never commit real credentials to git**
2. **Change default secret keys in production**
3. **Use environment-specific credentials**
4. **Rotate API keys regularly**
5. **Use service role keys only for backend operations**

## Testing the Configuration

Run this command to test if your environment is properly configured:

```bash
python3 -c "from app.config import get_settings; settings = get_settings(); print('✅ Configuration loaded successfully')"
```

## Troubleshooting

### Common Issues:

1. **Database Connection Failed**
   - Verify your database password is correct
   - Check if your IP is allowlisted in Supabase
   - Ensure the connection string format is correct

2. **Clerk Authentication Failed**
   - Verify your Clerk secret key is correct
   - Check if the publishable key matches your Clerk project

3. **Twitch API Failed**
   - Verify your Twitch client ID and secret
   - Check if your Twitch app is properly configured

4. **Redis Connection Failed**
   - Ensure Redis is running locally
   - Check the Redis URL format

### Getting Help

If you encounter issues:
1. Check the application logs for detailed error messages
2. Verify all environment variables are correctly set
3. Test each service individually (database, Redis, external APIs)