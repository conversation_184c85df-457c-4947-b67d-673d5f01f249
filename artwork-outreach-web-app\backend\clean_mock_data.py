#!/usr/bin/env python3
"""
Clean mock data from Supabase database.
Removes the 3 fake streamers and keeps only real scraper data.
"""

import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_SERVICE_ROLE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

headers = {
    "apikey": SUPABASE_SERVICE_ROLE_KEY,
    "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
    "Content-Type": "application/json"
}

def delete_mock_streamers():
    """Delete the 3 mock streamers by their usernames"""
    
    mock_usernames = [
        "pixel_artist_jane",
        "indie_dev_alex", 
        "art_streamer_mike"
    ]
    
    print("🧹 Cleaning mock data from Supabase...")
    
    for username in mock_usernames:
        try:
            # Delete by username
            url = f"{SUPABASE_URL}/rest/v1/streamers?username=eq.{username}"
            
            response = requests.delete(url, headers=headers, timeout=10)
            
            if response.status_code in [200, 204]:
                print(f"✅ Deleted mock streamer: {username}")
            else:
                print(f"❌ Failed to delete {username}: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Error deleting {username}: {e}")
    
    print("\n📊 Checking remaining streamers...")
    
    # Get all remaining streamers
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/streamers?select=username,display_name,follower_count,current_game&order=username",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            streamers = response.json()
            print(f"✅ Total streamers remaining: {len(streamers)}")
            
            # Show first 10 for verification
            print("\n🎮 First 10 streamers (should all be real data):")
            for i, streamer in enumerate(streamers[:10]):
                print(f"{i+1:2d}. {streamer['username']:20} | {streamer['display_name']:20} | {streamer['follower_count']:3d} followers | {streamer['current_game']}")
                
        else:
            print(f"❌ Failed to get streamers: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting streamers: {e}")

if __name__ == "__main__":
    delete_mock_streamers()
    print("\n🎉 Mock data cleanup complete!")
    print("💡 Restart your frontend to see only real scraper data!")