'use client';

import { useState } from 'react';
import { Streamer } from '@/types/streamer';
import { Assignment, UpdateAssignmentPayload, AssignmentStatus } from '@/types/assignment';
import { useApi } from '@/hooks/useApiClient';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/Tooltip';
import { Share2Icon, StarIcon, VideoIcon } from 'lucide-react';
import Image from 'next/image';

interface StreamerCardProps {
  streamer: Streamer;
  assignment: Assignment | null;
  onInterestLevelChanged: (streamerId: string, newStatus: AssignmentStatus) => void;
}

export function StreamerCard({ streamer, assignment, onInterestLevelChanged }: StreamerCardProps) {
  const [isInterested, setIsInterested] = useState(assignment?.status === AssignmentStatus.INTERESTED);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const api = useApi();

  const handleMarkAsInterested = async () => {
    if (!assignment) {
      setError('No assignment found for this streamer.');
      return;
    }
    
    setError(null);
    setIsLoading(true);
    try {
      const payload: UpdateAssignmentPayload = { status: AssignmentStatus.INTERESTED };
      await api.patch(`/assignments/${assignment.id}`, payload);
      setIsInterested(true);
      onInterestLevelChanged(streamer.id, AssignmentStatus.INTERESTED);
    } catch (err) {
      setError('Failed to mark as interested. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUndo = async () => {
    if (!assignment) {
      setError('No assignment found for this streamer.');
      return;
    }
    
    setError(null);
    setIsLoading(true);
    try {
      const payload: UpdateAssignmentPayload = { status: AssignmentStatus.PENDING };
      await api.patch(`/assignments/${assignment.id}`, payload);
      setIsInterested(false);
      onInterestLevelChanged(streamer.id, AssignmentStatus.PENDING);
    } catch (err) {
      setError('Failed to undo. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = () => {
    const url = `https://twitch.tv/${streamer.username}`;
    navigator.clipboard.writeText(url);
    // You can add a toast notification here to confirm the copy
  };

  return (
    <Card className="flex flex-col h-full transition-all duration-300 ease-in-out transform hover:scale-[1.02] hover:shadow-xl border-0 shadow-sm bg-white overflow-hidden">
      <CardHeader className="relative p-0">
        <a href={`https://twitch.tv/${streamer.username}`} target="_blank" rel="noopener noreferrer" className="block">
          <div className="relative w-full h-48 bg-gradient-to-br from-purple-400 to-blue-500">
            <Image
              src={streamer.profile_image_url || '/default-avatar.png'}
              alt={streamer.display_name || streamer.username}
              width={400}
              height={192}
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png';
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>

          {/* Live indicator */}
          {streamer.is_live && (
            <div className="absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center shadow-lg">
              <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
              LIVE
            </div>
          )}

          {/* Follower count badge */}
          <div className="absolute bottom-3 right-3 bg-black/70 text-white px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm">
            👥 {streamer.formatted_follower_count}
          </div>
        </a>
      </CardHeader>
      <CardContent className="flex-grow p-6">
        <div className="space-y-3">
          {/* Streamer name and username */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <a
                href={`https://twitch.tv/${streamer.username}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-lg font-bold text-gray-900 hover:text-purple-600 transition-colors"
              >
                {streamer.display_name}
              </a>
              <p className="text-sm text-gray-500">@{streamer.username}</p>
            </div>
            <Badge variant="secondary" className="text-xs">{streamer.language?.toUpperCase()}</Badge>
          </div>

          {/* Game and stream info */}
          {streamer.current_game && (
            <div className="flex items-center text-sm">
              <span className="text-purple-600 font-medium">🎮 {streamer.current_game}</span>
            </div>
          )}

          {streamer.stream_title && (
            <p className="text-gray-700 text-sm line-clamp-2 italic">
              "{streamer.stream_title}"
            </p>
          )}

          {/* Last seen live */}
          {streamer.time_since_live && !streamer.is_live && (
            <p className="text-xs text-gray-400">
              📅 Last live: {streamer.time_since_live}
            </p>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between p-4 bg-gray-50">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" onClick={handleShare}>
                <Share2Icon className="w-5 h-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Share Profile</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        {assignment ? (
          isInterested ? (
            <Button onClick={handleUndo} disabled={isLoading} variant="secondary">
              <StarIcon className="w-5 h-5 mr-2 text-yellow-400" />
              Interested
            </Button>
          ) : (
            <Button onClick={handleMarkAsInterested} disabled={isLoading}>
              <StarIcon className="w-5 h-5 mr-2" />
              Mark as Interested
            </Button>
          )
        ) : (
          <Button disabled className="bg-gray-300">
            <StarIcon className="w-5 h-5 mr-2" />
            Available Streamer
          </Button>
        )}
      </CardFooter>
      {error && <p className="p-4 text-sm text-red-500">{error}</p>}
    </Card>
  );
}