#!/usr/bin/env python3
"""
Startup script for Artwork Outreach backend.
This script checks configuration and starts the FastAPI server.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_redis():
    """Check if Redis is running."""
    try:
        import redis
        r = redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379/0'))
        r.ping()
        print("✅ Redis is running")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("💡 Start Redis with: redis-server")
        return False

def check_dependencies():
    """Check if required Python packages are installed."""
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic_settings',
        'sqlalchemy',
        'redis',
        'supabase'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install -r requirements.txt")
        return False
    
    print("✅ All required packages are installed")
    return True

def main():
    """Main startup function."""
    print("🚀 Starting Artwork Outreach Backend...\n")
    
    # Check if we're in the right directory
    if not Path('.env').exists():
        print("❌ .env file not found. Make sure you're in the backend directory.")
        sys.exit(1)
    
    # Verify configuration
    print("🔧 Verifying configuration...")
    result = subprocess.run([sys.executable, 'verify_config.py'], capture_output=True, text=True)
    
    if result.returncode != 0:
        print("❌ Configuration verification failed")
        print(result.stdout)
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check Redis (optional but recommended)
    redis_ok = check_redis()
    if not redis_ok:
        print("⚠️  Redis is not running. Some features may not work properly.")
        choice = input("Continue anyway? (y/N): ").lower().strip()
        if choice != 'y':
            sys.exit(1)
    
    print("\n🎉 All checks passed! Starting FastAPI server...")
    print("📖 API Documentation will be available at: http://localhost:8000/docs")
    print("🔧 Health Check: http://localhost:8000/api/v1/health")
    print("⏹️  Press Ctrl+C to stop the server\n")
    
    # Start the server
    try:
        subprocess.run([
            'uvicorn', 
            'app.main:app', 
            '--reload', 
            '--host', '0.0.0.0', 
            '--port', '8000'
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()