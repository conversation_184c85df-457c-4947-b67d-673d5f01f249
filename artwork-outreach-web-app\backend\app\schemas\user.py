from pydantic import BaseModel, Field
from uuid import UUID
from datetime import date, datetime, timedelta

class UserProfileResponse(BaseModel):
    id: UUID
    email: str
    full_name: str | None = None
    daily_request_count: int
    last_request_date: date | None = None
    role: str

    model_config = {"from_attributes": True}

class UserStatusResponse(BaseModel):
    daily_request_count: int
    last_request_date: date | None = None
    remaining_requests: int
    next_reset_time: datetime

    @classmethod
    def from_user(cls, user):
        # Daily limit of 3 requests per agent per day (PRD requirement)
        daily_limit = 3
        remaining = max(0, daily_limit - user.daily_request_count)
        
        now = datetime.utcnow()
        reset_time = (now + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)

        return cls(
            daily_request_count=user.daily_request_count,
            last_request_date=user.last_request_date,
            remaining_requests=remaining,
            next_reset_time=reset_time,
        )

class UpdateUserProfileRequest(BaseModel):
    full_name: str | None = None
    email: str | None = None

    model_config = {"from_attributes": True}
