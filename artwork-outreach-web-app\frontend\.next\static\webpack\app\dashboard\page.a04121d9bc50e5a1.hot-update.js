"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/StreamerList */ \"(app-pages-browser)/./src/components/dashboard/StreamerList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/AssignmentList */ \"(app-pages-browser)/./src/components/dashboard/AssignmentList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AssignmentAnalytics */ \"(app-pages-browser)/./src/components/dashboard/AssignmentAnalytics.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [streamers, setStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredStreamers, setFilteredStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStatus, setUserStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [backendConnected, setBackendConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filter states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showLiveOnly, setShowLiveOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedGame, setSelectedGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [minFollowers, setMinFollowers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Live verification states - always use verified live only\n    const useVerifiedLive = true;\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [batchSize, setBatchSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100); // Default batch size\n    const API_BASE_URL = \"/api\";\n    // Filter streamers based on current filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = streamers;\n        if (searchQuery) {\n            filtered = filtered.filter((streamer)=>{\n                var _streamer_current_game;\n                return streamer.display_name.toLowerCase().includes(searchQuery.toLowerCase()) || streamer.username.toLowerCase().includes(searchQuery.toLowerCase()) || ((_streamer_current_game = streamer.current_game) === null || _streamer_current_game === void 0 ? void 0 : _streamer_current_game.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        if (showLiveOnly) {\n            filtered = filtered.filter((streamer)=>streamer.is_live);\n        }\n        if (selectedGame) {\n            filtered = filtered.filter((streamer)=>streamer.current_game === selectedGame);\n        }\n        if (minFollowers > 0) {\n            filtered = filtered.filter((streamer)=>streamer.follower_count >= minFollowers);\n        }\n        setFilteredStreamers(filtered);\n    }, [\n        streamers,\n        searchQuery,\n        showLiveOnly,\n        selectedGame,\n        minFollowers\n    ]);\n    // Fetch initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStats();\n        fetchUserStatus();\n        fetchAssignments();\n        // Auto-load streamers on page load\n        fetchNewStreamers();\n    }, []);\n    const fetchUserStatus = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/user/status\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUserStatus(data);\n            } else {\n                console.error(\"Failed to fetch user status - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch user status - Connection error:\", err);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            console.log(\"Fetching stats using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getStreamerStats();\n            setStats({\n                total_streamers: data.total_streamers,\n                live_streamers: data.live_streamers,\n                available_streamers: data.available_streamers,\n                average_followers: 25 // Use static value since backend doesn't calculate this\n            });\n            setBackendConnected(true);\n            console.log(\"Stats fetched successfully:\", data);\n        } catch (err) {\n            console.error(\"Failed to fetch stats - Connection error:\", err);\n            setBackendConnected(false);\n            // Set default stats if backend is not available\n            setStats({\n                total_streamers: 0,\n                live_streamers: 0,\n                available_streamers: 0,\n                average_followers: 0\n            });\n        }\n    };\n    const fetchNewStreamers = async ()=>{\n        console.log(\"=== FETCH NEW STREAMERS BUTTON CLICKED ===\");\n        setLoading(true);\n        setError(null);\n        setVerificationStatus(\"\");\n        try {\n            if (useVerifiedLive) {\n                console.log(\"Fetching VERIFIED LIVE streamers using real-time API...\");\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getLiveVerifiedStreamers(batchSize);\n                console.log(\"Received verified live data:\", data);\n                const streamersArray = data.streamers || [];\n                setStreamers(streamersArray);\n                setVerificationStatus(\"\".concat(data.status, \" (checked \").concat(data.checked_count, \" streamers)\"));\n                console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" VERIFIED LIVE streamers!\"));\n            } else {\n                console.log(\"Fetching streamers using database API...\");\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getAvailableStreamers(50);\n                console.log(\"Received database data:\", data);\n                const streamersArray = data.streamers || [];\n                setStreamers(streamersArray);\n                setVerificationStatus(\"Using database data (may include offline streamers)\");\n                // Update user status from response\n                if (data.user_status) {\n                    setUserStatus({\n                        daily_request_count: data.user_status.daily_requests_used,\n                        remaining_requests: data.user_status.daily_requests_remaining,\n                        next_reset_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now\n                    });\n                }\n                console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" streamers from database!\"));\n            }\n            // Refresh stats after fetching streamers\n            await fetchStats();\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to fetch streamers\";\n            setError(errorMessage);\n            console.error(\"=== ERROR FETCHING STREAMERS ===\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAssignments = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAssignments(data.assignments);\n            } else {\n                console.error(\"Failed to fetch assignments - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch assignments - Connection error:\", err);\n        }\n    };\n    const handleUpdateAssignment = async (id, status, notes)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status,\n                    notes\n                })\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to update assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to update assignment - Connection error:\", err);\n        }\n    };\n    const handleDeleteAssignment = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to delete assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to delete assignment - Connection error:\", err);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Please sign in to access the dashboard.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"\\uD83C\\uDFAF Streamer Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.name) || \"Agent\",\n                                \"! Discover and manage your streamer outreach campaigns.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Total Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.total_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-green-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-green-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Live Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.live_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-purple-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.available_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-yellow-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-yellow-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Loaded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: filteredStreamers.length > 0 && filteredStreamers.length !== streamers.length ? \"\".concat(filteredStreamers.length, \"/\").concat(streamers.length) : streamers.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                children: \"Streamer Discovery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: useVerifiedLive ? \"Get real-time verified live streamers (recommended for outreach)\" : \"Load streamers from database (may include offline streamers)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this),\n                                            verificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-purple-600 mt-1\",\n                                                children: [\n                                                    \"\\uD83D\\uDCCA \",\n                                                    verificationStatus\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center bg-green-50 rounded-lg p-2 border border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-green-700\",\n                                                    children: \"✅ Live Verified Mode Only\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center bg-blue-50 rounded-lg p-2 border border-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-blue-700 mr-2\",\n                                                        children: \"Batch Size:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: batchSize,\n                                                        onChange: (e)=>setBatchSize(Number(e.target.value)),\n                                                        className: \"text-sm bg-white border border-blue-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 10,\n                                                                children: \"10 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 25,\n                                                                children: \"25 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 50,\n                                                                children: \"50 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 100,\n                                                                children: \"100 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 200,\n                                                                children: \"200 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 300,\n                                                                children: \"300 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 500,\n                                                                children: \"500 streamers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: fetchNewStreamers,\n                                                disabled: loading || (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0,\n                                                className: \"px-6 py-3 rounded-lg font-medium text-white transition-all \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"bg-gray-400 cursor-not-allowed\" : loading ? \"bg-blue-400 cursor-wait\" : useVerifiedLive ? \"bg-green-600 hover:bg-green-700 hover:shadow-lg\" : \"bg-blue-600 hover:bg-blue-700 hover:shadow-lg\"),\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        useVerifiedLive ? \"Verifying...\" : \"Loading...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 21\n                                                }, this) : (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"❌ Daily Limit Reached\" : useVerifiedLive ? \"\\uD83D\\uDD34 Get \".concat(batchSize, \" Live Streamers\") : \"\\uD83D\\uDD04 Refresh Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            userStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"Daily requests: \",\n                                                userStatus.daily_request_count,\n                                                \"/3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full mr-2 \".concat(backendConnected === true ? \"bg-green-500\" : backendConnected === false ? \"bg-red-500\" : \"bg-yellow-500\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: backendConnected === true ? \"Backend Connected\" : backendConnected === false ? \"Backend Disconnected\" : \"Checking...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: [\n                                        \"⚠️ \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, this),\n                streamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"\\uD83D\\uDD0D Filter Streamers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search by name or game...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: showLiveOnly,\n                                                        onChange: (e)=>setShowLiveOnly(e.target.checked),\n                                                        className: \"rounded border-gray-300 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                        children: \"Live only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Game\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedGame,\n                                                onChange: (e)=>setSelectedGame(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All games\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    Array.from(new Set(streamers.map((s)=>s.current_game).filter(Boolean))).map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: game,\n                                                            children: game\n                                                        }, game, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Min Followers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"0\",\n                                                value: minFollowers || \"\",\n                                                onChange: (e)=>setMinFollowers(Number(e.target.value) || 0),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Showing \",\n                                            filteredStreamers.length,\n                                            \" of \",\n                                            streamers.length,\n                                            \" streamers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setSearchQuery(\"\");\n                                            setShowLiveOnly(false);\n                                            setSelectedGame(\"\");\n                                            setMinFollowers(0);\n                                        },\n                                        className: \"text-sm text-purple-600 hover:text-purple-700 font-medium\",\n                                        children: \"Clear filters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        \"\\uD83C\\uDFAE Available Streamers \",\n                                        filteredStreamers.length > 0 && \"(\".concat(filteredStreamers.length, \")\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this),\n                                filteredStreamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredStreamers.length,\n                                        \" streamers\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(8)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-48 bg-gray-200 rounded-lg mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-2/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this) : filteredStreamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            streamers: filteredStreamers,\n                            assignments: assignments,\n                            onInterestLevelChanged: ()=>{}\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 13\n                        }, this) : streamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers match your filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Try adjusting your search criteria or clear the filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers loaded yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'Click \"Refresh Streamers\" to load available streamers from your database'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 496,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        assignments: assignments\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 544,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        assignments: assignments,\n                        onUpdate: handleUpdateAssignment,\n                        onDelete: handleDeleteAssignment\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 549,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"8MlTuSi6kx+cDPP2wYKzOXP7QIk=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});