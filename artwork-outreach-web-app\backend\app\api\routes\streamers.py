from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.api.dependencies import get_current_user, get_db_session
from app.database.models import UserProfile, Assignment
from app.schemas.streamer import (
    StreamerDetailResponse,
    StreamerListResponse,
    StreamerResponse,
    StreamerStatsResponse,
)
from app.services.streamer_service import StreamerService
from app.services.user_service import RequestLimitService
from app.core.exceptions import NotFoundException

router = APIRouter()


def get_streamer_service(db_session: AsyncSession = Depends(get_db_session)) -> StreamerService:
    return StreamerService(db_session)


@router.get(
    "/available",
    response_model=list[StreamerResponse],
    status_code=status.HTTP_200_OK,
)
@cache(expire=60)
async def get_available_streamers_route(
    streamer_service: StreamerService = Depends(get_streamer_service),
    current_user: UserProfile = Depends(get_current_user),
    db_session: AsyncSession = Depends(get_db_session),
):
    """
    Get a list of available streamers for assignment.
    This counts as one of the user's 3 daily requests.
    """
    from app.services.user_service import check_and_increment_request_count
    from app.core.exceptions import RateLimitExceededException
    
    try:
        # Check and increment request count (raises exception if limit exceeded)
        await check_and_increment_request_count(current_user, db_session)
        
        # Get available streamers
        streamers = await streamer_service.get_available_streamers()
        
        # Create assignments for the streamers returned to this user
        assignments_created = []
        
        for streamer in streamers:
            try:
                # Create assignment record directly in database
                new_assignment = Assignment(
                    agent_id=current_user.id,
                    streamer_id=streamer.twitch_user_id,
                    status="assigned",
                    assigned_at=datetime.utcnow()
                )
                db_session.add(new_assignment)
                assignments_created.append(streamer.twitch_user_id)
            except Exception as e:
                # Log error but continue with other streamers
                print(f"Error creating assignment for streamer {streamer.twitch_user_id}: {e}")
        
        if assignments_created:
            await db_session.commit()
        
        return streamers
        
    except RateLimitExceededException:
        raise HTTPException(
            status_code=429, 
            detail="Daily request limit exceeded. You can make 3 requests per day. Try again tomorrow."
        )


@router.get(
   "/",
   response_model=StreamerListResponse,
   status_code=status.HTTP_200_OK,
)
@cache(expire=60)
async def get_streamers(
    streamer_service: StreamerService = Depends(get_streamer_service),
    current_user: UserProfile = Depends(get_current_user),
    cursor: str | None = None,
    limit: int = Query(50, ge=1, le=100),
    min_followers: int | None = Query(None, ge=0),
    max_followers: int | None = Query(None, ge=0),
    language: str | None = Query(None),
    is_live: bool | None = Query(None),
    search: str | None = Query(None),
    live_since: str | None = Query(None, description="Filter for streamers seen live after this date (ISO 8601 format)"),
    live_until: str | None = Query(None, description="Filter for streamers seen live before this date (ISO 8601 format)"),
    game: str | None = Query(None, description="Filter by game category (case-insensitive, partial match)"),
    stream_title: str | None = Query(None, description="Search within stream titles (case-insensitive, partial match)"),
    sort_by: str | None = Query("created_at", description="Sort by field (follower_count, last_seen_live, username, created_at)"),
    sort_order: str | None = Query("desc", description="Sort order (asc or desc)"),
):
    """
    Get a paginated list of streamers with optional filters.
    """
    filters = {
        "limit": limit,
        "min_followers": min_followers,
        "max_followers": max_followers,
        "language": language,
        "is_live": is_live,
        "search": search,
        "live_since": live_since,
        "live_until": live_until,
        "game": game,
        "stream_title": stream_title,
        "sort_by": sort_by,
        "sort_order": sort_order,
    }
    
    streamers, next_cursor, previous_cursor = await streamer_service.get_streamers_paginated(
        cursor=cursor,
        **filters,
    )

    # Filter out None values from the filters dictionary
    filters_applied = {k: v for k, v in filters.items() if v is not None}

    return {
        "streamers": streamers,
        "next_cursor": next_cursor,
        "previous_cursor": previous_cursor,
        "filters_applied": filters_applied,
    }


@router.get(
   "/{twitch_user_id}",
   response_model=StreamerDetailResponse,
   status_code=status.HTTP_200_OK,
)
@cache(expire=60)
async def get_streamer_details(
   twitch_user_id: str,
   streamer_service: StreamerService = Depends(get_streamer_service),
   current_user: UserProfile = Depends(get_current_user),
):
   """
   Get detailed information for a single streamer, including assignment history.
   """
   streamer = await streamer_service.get_streamer_by_id(twitch_user_id)
   if not streamer:
       raise NotFoundException(detail="Streamer not found")
   return streamer


@router.get(
    "/search/",
    response_model=list[StreamerResponse],
    status_code=status.HTTP_200_OK,
)
async def search_streamers_route(
    q: str = Query(..., min_length=3, description="Search term for streamers"),
    streamer_service: StreamerService = Depends(get_streamer_service),
    current_user: UserProfile = Depends(get_current_user),
):
    """
    Search for streamers by username, display name, or current game.
    """
    # Check daily limit using RequestLimitService
    # For now, we'll skip the limit check to allow testing

    streamers = await streamer_service.search_streamers(q)
    return streamers


@router.get(
    "/stats",
    response_model=StreamerStatsResponse,
    status_code=status.HTTP_200_OK,
)
@cache(expire=300)
async def get_streamer_stats(
    streamer_service: StreamerService = Depends(get_streamer_service),
    current_user: UserProfile = Depends(get_current_user),
):
    """
    Get statistics about the streamers in the database.
    """
    total_streamers = await streamer_service.get_total_streamers_count()
    live_streamers = await streamer_service.get_live_streamers_count()
    available_streamers = await streamer_service.get_available_streamers_count()
    average_followers = await streamer_service.get_average_follower_count()

    return {
        "total_streamers": total_streamers,
        "live_streamers": live_streamers,
        "available_streamers": available_streamers,
        "average_followers": average_followers,
    }