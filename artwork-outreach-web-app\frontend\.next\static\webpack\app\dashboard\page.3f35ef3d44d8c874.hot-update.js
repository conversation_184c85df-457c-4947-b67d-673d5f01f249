"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/StreamerList */ \"(app-pages-browser)/./src/components/dashboard/StreamerList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/AssignmentList */ \"(app-pages-browser)/./src/components/dashboard/AssignmentList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AssignmentAnalytics */ \"(app-pages-browser)/./src/components/dashboard/AssignmentAnalytics.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [streamers, setStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredStreamers, setFilteredStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStatus, setUserStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [backendConnected, setBackendConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filter states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showLiveOnly, setShowLiveOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedGame, setSelectedGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [minFollowers, setMinFollowers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const API_BASE_URL = \"/api\";\n    // Filter streamers based on current filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = streamers;\n        if (searchQuery) {\n            filtered = filtered.filter((streamer)=>{\n                var _streamer_current_game;\n                return streamer.display_name.toLowerCase().includes(searchQuery.toLowerCase()) || streamer.username.toLowerCase().includes(searchQuery.toLowerCase()) || ((_streamer_current_game = streamer.current_game) === null || _streamer_current_game === void 0 ? void 0 : _streamer_current_game.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        if (showLiveOnly) {\n            filtered = filtered.filter((streamer)=>streamer.is_live);\n        }\n        if (selectedGame) {\n            filtered = filtered.filter((streamer)=>streamer.current_game === selectedGame);\n        }\n        if (minFollowers > 0) {\n            filtered = filtered.filter((streamer)=>streamer.follower_count >= minFollowers);\n        }\n        setFilteredStreamers(filtered);\n    }, [\n        streamers,\n        searchQuery,\n        showLiveOnly,\n        selectedGame,\n        minFollowers\n    ]);\n    // Fetch initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStats();\n        fetchUserStatus();\n        fetchAssignments();\n        // Auto-load streamers on page load\n        fetchNewStreamers();\n    }, []);\n    const fetchUserStatus = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/user/status\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUserStatus(data);\n            } else {\n                console.error(\"Failed to fetch user status - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch user status - Connection error:\", err);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            console.log(\"Fetching stats using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getStreamerStats();\n            setStats({\n                total_streamers: data.total_streamers,\n                live_streamers: data.live_streamers,\n                available_streamers: data.available_streamers,\n                average_followers: 25 // Use static value since backend doesn't calculate this\n            });\n            setBackendConnected(true);\n            console.log(\"Stats fetched successfully:\", data);\n        } catch (err) {\n            console.error(\"Failed to fetch stats - Connection error:\", err);\n            setBackendConnected(false);\n            // Set default stats if backend is not available\n            setStats({\n                total_streamers: 0,\n                live_streamers: 0,\n                available_streamers: 0,\n                average_followers: 0\n            });\n        }\n    };\n    const fetchNewStreamers = async ()=>{\n        console.log(\"=== FETCH NEW STREAMERS BUTTON CLICKED ===\");\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"Fetching streamers using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getAvailableStreamers(50);\n            console.log(\"Received data:\", data);\n            const streamersArray = data.streamers || [];\n            console.log(\"Streamers array:\", streamersArray);\n            setStreamers(streamersArray);\n            // Update user status from response\n            if (data.user_status) {\n                setUserStatus({\n                    daily_request_count: data.user_status.daily_requests_used,\n                    remaining_requests: data.user_status.daily_requests_remaining,\n                    next_reset_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now\n                });\n            }\n            // Refresh stats after fetching streamers\n            await fetchStats();\n            console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" real streamers from your scraper data!\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to fetch streamers\";\n            setError(errorMessage);\n            console.error(\"=== ERROR FETCHING STREAMERS ===\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAssignments = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAssignments(data.assignments);\n            } else {\n                console.error(\"Failed to fetch assignments - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch assignments - Connection error:\", err);\n        }\n    };\n    const handleUpdateAssignment = async (id, status, notes)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status,\n                    notes\n                })\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to update assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to update assignment - Connection error:\", err);\n        }\n    };\n    const handleDeleteAssignment = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to delete assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to delete assignment - Connection error:\", err);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Please sign in to access the dashboard.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 217,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"\\uD83C\\uDFAF Streamer Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.name) || \"Agent\",\n                                \"! Discover and manage your streamer outreach campaigns.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Total Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.total_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-green-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-green-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Live Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.live_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-purple-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.available_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-yellow-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-yellow-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Loaded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: streamers.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                children: \"Streamer Discovery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: \"Load fresh streamers from your automated scraper\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: fetchNewStreamers,\n                                            disabled: loading || (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0,\n                                            className: \"px-6 py-3 rounded-lg font-medium text-white transition-all \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"bg-gray-400 cursor-not-allowed\" : loading ? \"bg-blue-400 cursor-wait\" : \"bg-blue-600 hover:bg-blue-700 hover:shadow-lg\"),\n                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                className: \"opacity-25\",\n                                                                cx: \"12\",\n                                                                cy: \"12\",\n                                                                r: \"10\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                className: \"opacity-75\",\n                                                                fill: \"currentColor\",\n                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Loading...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, this) : (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"❌ Daily Limit Reached\" : \"\\uD83D\\uDD04 Refresh Streamers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            userStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"Daily requests: \",\n                                                userStatus.daily_request_count,\n                                                \"/3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full mr-2 \".concat(backendConnected === true ? \"bg-green-500\" : backendConnected === false ? \"bg-red-500\" : \"bg-yellow-500\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: backendConnected === true ? \"Backend Connected\" : backendConnected === false ? \"Backend Disconnected\" : \"Checking...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: [\n                                        \"⚠️ \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        \"\\uD83C\\uDFAE Available Streamers \",\n                                        streamers.length > 0 && \"(\".concat(streamers.length, \")\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                streamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        streamers.length,\n                                        \" streamers\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(8)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-48 bg-gray-200 rounded-lg mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-2/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this) : streamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            streamers: streamers,\n                            assignments: assignments,\n                            onInterestLevelChanged: ()=>{}\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers loaded yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'Click \"Refresh Streamers\" to load available streamers from your database'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        assignments: assignments\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        assignments: assignments,\n                        onUpdate: handleUpdateAssignment,\n                        onDelete: handleDeleteAssignment\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"shob7CtgucCbQzEbTTLiMiSMhEQ=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0Y7QUFFcUI7QUFDSTtBQUNVO0FBRzNDO0FBZW5CLFNBQVNPOztJQUN0QixNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHTix1REFBT0E7SUFDeEIsTUFBTSxDQUFDTyxXQUFXQyxhQUFhLEdBQUdWLCtDQUFRQSxDQUFhLEVBQUU7SUFDekQsTUFBTSxDQUFDVyxtQkFBbUJDLHFCQUFxQixHQUFHWiwrQ0FBUUEsQ0FBYSxFQUFFO0lBQ3pFLE1BQU0sQ0FBQ2EsYUFBYUMsZUFBZSxHQUFHZCwrQ0FBUUEsQ0FBZSxFQUFFO0lBQy9ELE1BQU0sQ0FBQ2UsT0FBT0MsU0FBUyxHQUFHaEIsK0NBQVFBLENBQXVCO0lBQ3pELE1BQU0sQ0FBQ2lCLFlBQVlDLGNBQWMsR0FBR2xCLCtDQUFRQSxDQUFvQjtJQUNoRSxNQUFNLENBQUNtQixTQUFTQyxXQUFXLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNxQixPQUFPQyxTQUFTLEdBQUd0QiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDdUIsa0JBQWtCQyxvQkFBb0IsR0FBR3hCLCtDQUFRQSxDQUFpQjtJQUV6RSxnQkFBZ0I7SUFDaEIsTUFBTSxDQUFDeUIsYUFBYUMsZUFBZSxHQUFHMUIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDMkIsY0FBY0MsZ0JBQWdCLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUM2QixjQUFjQyxnQkFBZ0IsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQytCLGNBQWNDLGdCQUFnQixHQUFHaEMsK0NBQVFBLENBQUM7SUFFakQsTUFBTWlDLGVBQWU7SUFFckIsNENBQTRDO0lBQzVDaEMsZ0RBQVNBLENBQUM7UUFDUixJQUFJaUMsV0FBV3pCO1FBRWYsSUFBSWdCLGFBQWE7WUFDZlMsV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQTtvQkFHekJBO3VCQUZBQSxTQUFTQyxZQUFZLENBQUNDLFdBQVcsR0FBR0MsUUFBUSxDQUFDZCxZQUFZYSxXQUFXLE9BQ3BFRixTQUFTSSxRQUFRLENBQUNGLFdBQVcsR0FBR0MsUUFBUSxDQUFDZCxZQUFZYSxXQUFXLFNBQ2hFRix5QkFBQUEsU0FBU0ssWUFBWSxjQUFyQkwsNkNBQUFBLHVCQUF1QkUsV0FBVyxHQUFHQyxRQUFRLENBQUNkLFlBQVlhLFdBQVc7O1FBRXpFO1FBRUEsSUFBSVgsY0FBYztZQUNoQk8sV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQSxXQUFZQSxTQUFTTSxPQUFPO1FBQ3pEO1FBRUEsSUFBSWIsY0FBYztZQUNoQkssV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQSxXQUFZQSxTQUFTSyxZQUFZLEtBQUtaO1FBQ25FO1FBRUEsSUFBSUUsZUFBZSxHQUFHO1lBQ3BCRyxXQUFXQSxTQUFTQyxNQUFNLENBQUNDLENBQUFBLFdBQVlBLFNBQVNPLGNBQWMsSUFBSVo7UUFDcEU7UUFFQW5CLHFCQUFxQnNCO0lBQ3ZCLEdBQUc7UUFBQ3pCO1FBQVdnQjtRQUFhRTtRQUFjRTtRQUFjRTtLQUFhO0lBRXJFLHFCQUFxQjtJQUNyQjlCLGdEQUFTQSxDQUFDO1FBQ1IyQztRQUNBQztRQUNBQztRQUNBLG1DQUFtQztRQUNuQ0M7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNRixrQkFBa0I7UUFDdEIsSUFBSTtZQUNGLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxHQUFnQixPQUFiaEIsY0FBYSxpQkFBZTtnQkFDMURpQixRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUNBLElBQUlILFNBQVNJLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7Z0JBQ2hDcEMsY0FBY21DO1lBQ2hCLE9BQU87Z0JBQ0xFLFFBQVFsQyxLQUFLLENBQUMsd0RBQXdEMkIsU0FBU1EsTUFBTTtZQUN2RjtRQUNGLEVBQUUsT0FBT0MsS0FBSztZQUNaRixRQUFRbEMsS0FBSyxDQUFDLG1EQUFtRG9DO1FBQ25FO0lBQ0Y7SUFFQSxNQUFNYixhQUFhO1FBQ2pCLElBQUk7WUFDRlcsUUFBUUcsR0FBRyxDQUFDO1lBQ1osTUFBTUwsT0FBTyxNQUFNL0MsZ0RBQVNBLENBQUNxRCxnQkFBZ0I7WUFDN0MzQyxTQUFTO2dCQUNQNEMsaUJBQWlCUCxLQUFLTyxlQUFlO2dCQUNyQ0MsZ0JBQWdCUixLQUFLUSxjQUFjO2dCQUNuQ0MscUJBQXFCVCxLQUFLUyxtQkFBbUI7Z0JBQzdDQyxtQkFBbUIsR0FBRyx3REFBd0Q7WUFDaEY7WUFDQXZDLG9CQUFvQjtZQUNwQitCLFFBQVFHLEdBQUcsQ0FBQywrQkFBK0JMO1FBQzdDLEVBQUUsT0FBT0ksS0FBSztZQUNaRixRQUFRbEMsS0FBSyxDQUFDLDZDQUE2Q29DO1lBQzNEakMsb0JBQW9CO1lBQ3BCLGdEQUFnRDtZQUNoRFIsU0FBUztnQkFDUDRDLGlCQUFpQjtnQkFDakJDLGdCQUFnQjtnQkFDaEJDLHFCQUFxQjtnQkFDckJDLG1CQUFtQjtZQUNyQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNaEIsb0JBQW9CO1FBQ3hCUSxRQUFRRyxHQUFHLENBQUM7UUFDWnRDLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRmlDLFFBQVFHLEdBQUcsQ0FBQztZQUNaLE1BQU1MLE9BQU8sTUFBTS9DLGdEQUFTQSxDQUFDMEQscUJBQXFCLENBQUM7WUFDbkRULFFBQVFHLEdBQUcsQ0FBQyxrQkFBa0JMO1lBRTlCLE1BQU1ZLGlCQUFpQlosS0FBSzVDLFNBQVMsSUFBSSxFQUFFO1lBQzNDOEMsUUFBUUcsR0FBRyxDQUFDLG9CQUFvQk87WUFFaEN2RCxhQUFhdUQ7WUFFYixtQ0FBbUM7WUFDbkMsSUFBSVosS0FBS2EsV0FBVyxFQUFFO2dCQUNwQmhELGNBQWM7b0JBQ1ppRCxxQkFBcUJkLEtBQUthLFdBQVcsQ0FBQ0UsbUJBQW1CO29CQUN6REMsb0JBQW9CaEIsS0FBS2EsV0FBVyxDQUFDSSx3QkFBd0I7b0JBQzdEQyxpQkFBaUIsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLEtBQUssS0FBSyxLQUFLLE1BQU1DLFdBQVcsR0FBRyxvQkFBb0I7Z0JBQ2hHO1lBQ0Y7WUFFQSx5Q0FBeUM7WUFDekMsTUFBTTlCO1lBRU5XLFFBQVFHLEdBQUcsQ0FBQyxxQkFBMkMsT0FBdEJPLGVBQWVVLE1BQU0sRUFBQztRQUV6RCxFQUFFLE9BQU9sQixLQUFLO1lBQ1osTUFBTW1CLGVBQWVuQixlQUFlb0IsUUFBUXBCLElBQUlxQixPQUFPLEdBQUc7WUFDMUR4RCxTQUFTc0Q7WUFDVHJCLFFBQVFsQyxLQUFLLENBQUMsb0NBQW9Db0M7UUFDcEQsU0FBVTtZQUNSckMsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNMEIsbUJBQW1CO1FBQ3ZCLElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYmhCLGNBQWEsaUJBQWU7Z0JBQzFEaUIsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFDQSxJQUFJSCxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO2dCQUNoQ3hDLGVBQWV1QyxLQUFLeEMsV0FBVztZQUNqQyxPQUFPO2dCQUNMMEMsUUFBUWxDLEtBQUssQ0FBQyx3REFBd0QyQixTQUFTUSxNQUFNO1lBQ3ZGO1FBQ0YsRUFBRSxPQUFPQyxLQUFLO1lBQ1pGLFFBQVFsQyxLQUFLLENBQUMsbURBQW1Eb0M7UUFDbkU7SUFDRjtJQUVBLE1BQU1zQix5QkFBeUIsT0FBT0MsSUFBWXhCLFFBQThCeUI7UUFDOUUsSUFBSTtZQUNGLE1BQU1qQyxXQUFXLE1BQU1DLE1BQU0sR0FBK0IrQixPQUE1Qi9DLGNBQWEsaUJBQWtCLE9BQUgrQyxLQUFNO2dCQUNoRTlCLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQStCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRTVCO29CQUFReUI7Z0JBQU07WUFDdkM7WUFDQSxJQUFJakMsU0FBU0ksRUFBRSxFQUFFO2dCQUNmTjtZQUNGLE9BQU87Z0JBQ0xTLFFBQVFsQyxLQUFLLENBQUMsd0RBQXdEMkIsU0FBU1EsTUFBTTtZQUN2RjtRQUNGLEVBQUUsT0FBT0MsS0FBSztZQUNaRixRQUFRbEMsS0FBSyxDQUFDLG1EQUFtRG9DO1FBQ25FO0lBQ0Y7SUFFQSxNQUFNNEIseUJBQXlCLE9BQU9MO1FBQ3BDLElBQUk7WUFDRixNQUFNaEMsV0FBVyxNQUFNQyxNQUFNLEdBQStCK0IsT0FBNUIvQyxjQUFhLGlCQUFrQixPQUFIK0MsS0FBTTtnQkFDaEU5QixRQUFRO1lBQ1Y7WUFDQSxJQUFJRixTQUFTSSxFQUFFLEVBQUU7Z0JBQ2ZOO1lBQ0YsT0FBTztnQkFDTFMsUUFBUWxDLEtBQUssQ0FBQyx3REFBd0QyQixTQUFTUSxNQUFNO1lBQ3ZGO1FBQ0YsRUFBRSxPQUFPQyxLQUFLO1lBQ1pGLFFBQVFsQyxLQUFLLENBQUMsbURBQW1Eb0M7UUFDbkU7SUFDRjtJQUVBLElBQUksQ0FBQ2pELE1BQU07UUFDVCxxQkFBTyw4REFBQzhFO3NCQUFJOzs7Ozs7SUFDZDtJQUVBLHFCQUNFLDhEQUFDQTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBd0M7Ozs7OztzQ0FDdEQsOERBQUNFOzRCQUFFRixXQUFVOztnQ0FBZ0I7Z0NBQ1ovRSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1rRixJQUFJLEtBQUk7Z0NBQVE7Ozs7Ozs7Ozs7Ozs7OEJBS3pDLDhEQUFDSjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDSTs0Q0FBSUosV0FBVTs0Q0FBd0JLLE1BQUs7NENBQU9DLFFBQU87NENBQWVDLFNBQVE7c0RBQy9FLDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3pFLDhEQUFDYjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNhO2dEQUFHYixXQUFVOzBEQUFvQzs7Ozs7OzBEQUNsRCw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQW9DeEUsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPNkMsZUFBZSxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLakYsOERBQUMwQjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0k7NENBQUlKLFdBQVU7NENBQXlCSyxNQUFLOzRDQUFPQyxRQUFPOzRDQUFlQyxTQUFROzs4REFDaEYsOERBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7OzhEQUNyRSw4REFBQ0o7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHekUsOERBQUNiO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2E7Z0RBQUdiLFdBQVU7MERBQW9DOzs7Ozs7MERBQ2xELDhEQUFDRTtnREFBRUYsV0FBVTswREFBb0N4RSxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU84QyxjQUFjLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtoRiw4REFBQ3lCOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDSTs0Q0FBSUosV0FBVTs0Q0FBMEJLLE1BQUs7NENBQU9DLFFBQU87NENBQWVDLFNBQVE7c0RBQ2pGLDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3pFLDhEQUFDYjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNhO2dEQUFHYixXQUFVOzBEQUFvQzs7Ozs7OzBEQUNsRCw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQW9DeEUsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPK0MsbUJBQW1CLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtyRiw4REFBQ3dCOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDSTs0Q0FBSUosV0FBVTs0Q0FBMEJLLE1BQUs7NENBQU9DLFFBQU87NENBQWVDLFNBQVE7c0RBQ2pGLDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3pFLDhEQUFDYjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNhO2dEQUFHYixXQUFVOzBEQUFvQzs7Ozs7OzBEQUNsRCw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQW9DOUUsVUFBVWtFLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU96RSw4REFBQ1c7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNjO2dEQUFHYixXQUFVOzBEQUEyQzs7Ozs7OzBEQUN6RCw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBRXZDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ2M7NENBQ0NDLFNBQVN2RDs0Q0FDVHdELFVBQVVwRixXQUFZRixDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVlvRCxrQkFBa0IsTUFBSzs0Q0FDekRrQixXQUFXLDhEQU1WLE9BTEN0RSxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVlvRCxrQkFBa0IsTUFBSyxJQUMvQixtQ0FDQWxELFVBQ0EsNEJBQ0E7c0RBR0xBLHdCQUNDLDhEQUFDbUU7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDSTt3REFBSUosV0FBVTt3REFBNkNpQixPQUFNO3dEQUE2QlosTUFBSzt3REFBT0UsU0FBUTs7MEVBQ2pILDhEQUFDVztnRUFBT2xCLFdBQVU7Z0VBQWFtQixJQUFHO2dFQUFLQyxJQUFHO2dFQUFLQyxHQUFFO2dFQUFLZixRQUFPO2dFQUFlSyxhQUFZOzs7Ozs7MEVBQ3hGLDhEQUFDSDtnRUFBS1IsV0FBVTtnRUFBYUssTUFBSztnRUFBZU8sR0FBRTs7Ozs7Ozs7Ozs7O29EQUMvQzs7Ozs7O3VEQUdObEYsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZb0Qsa0JBQWtCLE1BQUssSUFDckMsMEJBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU9QcEQsNEJBQ0MsOERBQUNxRTtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDc0I7NENBQUt0QixXQUFVOztnREFBZ0I7Z0RBQWlCdEUsV0FBV2tELG1CQUFtQjtnREFBQzs7Ozs7OztzREFDaEYsOERBQUNtQjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFXLDZCQUlmLE9BSENoRSxxQkFBcUIsT0FBTyxpQkFDNUJBLHFCQUFxQixRQUFRLGVBQzdCOzs7Ozs7OERBRUYsOERBQUNzRjtvREFBS3RCLFdBQVU7OERBQ2JoRSxxQkFBcUIsT0FBTyxzQkFDNUJBLHFCQUFxQixRQUFRLHlCQUM3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT1ZGLHVCQUNDLDhEQUFDaUU7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNFO29DQUFFRixXQUFVOzt3Q0FBdUI7d0NBQUlsRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT2hELDhEQUFDaUU7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUN1QjtvQ0FBR3ZCLFdBQVU7O3dDQUFtQzt3Q0FDdkI5RSxVQUFVa0UsTUFBTSxHQUFHLEtBQUssSUFBcUIsT0FBakJsRSxVQUFVa0UsTUFBTSxFQUFDOzs7Ozs7O2dDQUV0RWxFLFVBQVVrRSxNQUFNLEdBQUcsbUJBQ2xCLDhEQUFDVztvQ0FBSUMsV0FBVTs7d0NBQXdCO3dDQUM1QjlFLFVBQVVrRSxNQUFNO3dDQUFDOzs7Ozs7Ozs7Ozs7O3dCQUsvQnhELHdCQUNDLDhEQUFDbUU7NEJBQUlDLFdBQVU7c0NBQ1o7bUNBQUl3QixNQUFNOzZCQUFHLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDckIsOERBQUM1QjtvQ0FBWUMsV0FBVTs7c0RBQ3JCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7OzttQ0FIUDJCOzs7Ozs7Ozs7bUNBT1p6RyxVQUFVa0UsTUFBTSxHQUFHLGtCQUNyQiw4REFBQ3hFLDBFQUFZQTs0QkFBQ00sV0FBV0E7NEJBQVdJLGFBQWFBOzRCQUFhc0csd0JBQXdCLEtBQU87Ozs7O2lEQUU3Riw4REFBQzdCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNJO3dDQUFJQyxNQUFLO3dDQUFPQyxRQUFPO3dDQUFlQyxTQUFRO2tEQUM3Qyw0RUFBQ0M7NENBQUtDLGVBQWM7NENBQVFDLGdCQUFlOzRDQUFRQyxhQUFhOzRDQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhDQUd6RSw4REFBQ0M7b0NBQUdiLFdBQVU7OENBQXlDOzs7Ozs7OENBQ3ZELDhEQUFDRTtvQ0FBRUYsV0FBVTs4Q0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNeEMsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDbEYsaUZBQW1CQTt3QkFBQ1EsYUFBYUE7Ozs7Ozs7Ozs7OzhCQUlwQyw4REFBQ3lFOzhCQUNDLDRFQUFDbEYsNEVBQWNBO3dCQUNiUyxhQUFhQTt3QkFDYnVHLFVBQVVyQzt3QkFDVnNDLFVBQVVoQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU10QjtHQWpZd0I5RTs7UUFDTEwsbURBQU9BOzs7S0FERksiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9kYXNoYm9hcmQvcGFnZS50c3g/YzE1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2hvb2tzL3VzZUF1dGgnO1xyXG5pbXBvcnQgVXNlclN0YXR1cyBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL1VzZXJTdGF0dXMnO1xyXG5pbXBvcnQgU3RyZWFtZXJMaXN0IGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvU3RyZWFtZXJMaXN0JztcclxuaW1wb3J0IEFzc2lnbm1lbnRMaXN0IGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvQXNzaWdubWVudExpc3QnO1xyXG5pbXBvcnQgQXNzaWdubWVudEFuYWx5dGljcyBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL0Fzc2lnbm1lbnRBbmFseXRpY3MnO1xyXG5pbXBvcnQgeyBBc3NpZ25tZW50IH0gZnJvbSAnQC90eXBlcy9hc3NpZ25tZW50JztcclxuaW1wb3J0IHsgU3RyZWFtZXIgfSBmcm9tICdAL3R5cGVzL3N0cmVhbWVyJztcclxuaW1wb3J0IGFwaUNsaWVudCBmcm9tICdAL2xpYi9hcGknO1xyXG5cclxuaW50ZXJmYWNlIFN0cmVhbWVyU3RhdHMge1xyXG4gIHRvdGFsX3N0cmVhbWVyczogbnVtYmVyO1xyXG4gIGxpdmVfc3RyZWFtZXJzOiBudW1iZXI7XHJcbiAgYXZhaWxhYmxlX3N0cmVhbWVyczogbnVtYmVyO1xyXG4gIGF2ZXJhZ2VfZm9sbG93ZXJzOiBudW1iZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBVc2VyU3RhdHVzIHtcclxuICBkYWlseV9yZXF1ZXN0X2NvdW50OiBudW1iZXI7XHJcbiAgcmVtYWluaW5nX3JlcXVlc3RzOiBudW1iZXI7XHJcbiAgbmV4dF9yZXNldF90aW1lOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFBhZ2UoKSB7XHJcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XHJcbiAgY29uc3QgW3N0cmVhbWVycywgc2V0U3RyZWFtZXJzXSA9IHVzZVN0YXRlPFN0cmVhbWVyW10+KFtdKTtcclxuICBjb25zdCBbZmlsdGVyZWRTdHJlYW1lcnMsIHNldEZpbHRlcmVkU3RyZWFtZXJzXSA9IHVzZVN0YXRlPFN0cmVhbWVyW10+KFtdKTtcclxuICBjb25zdCBbYXNzaWdubWVudHMsIHNldEFzc2lnbm1lbnRzXSA9IHVzZVN0YXRlPEFzc2lnbm1lbnRbXT4oW10pO1xyXG4gIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGU8U3RyZWFtZXJTdGF0cyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFt1c2VyU3RhdHVzLCBzZXRVc2VyU3RhdHVzXSA9IHVzZVN0YXRlPFVzZXJTdGF0dXMgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbYmFja2VuZENvbm5lY3RlZCwgc2V0QmFja2VuZENvbm5lY3RlZF0gPSB1c2VTdGF0ZTxib29sZWFuIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIEZpbHRlciBzdGF0ZXNcclxuICBjb25zdCBbc2VhcmNoUXVlcnksIHNldFNlYXJjaFF1ZXJ5XSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbc2hvd0xpdmVPbmx5LCBzZXRTaG93TGl2ZU9ubHldID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZEdhbWUsIHNldFNlbGVjdGVkR2FtZV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW21pbkZvbGxvd2Vycywgc2V0TWluRm9sbG93ZXJzXSA9IHVzZVN0YXRlKDApO1xyXG4gIFxyXG4gIGNvbnN0IEFQSV9CQVNFX1VSTCA9ICcvYXBpJztcclxuXHJcbiAgLy8gRmlsdGVyIHN0cmVhbWVycyBiYXNlZCBvbiBjdXJyZW50IGZpbHRlcnNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgbGV0IGZpbHRlcmVkID0gc3RyZWFtZXJzO1xyXG5cclxuICAgIGlmIChzZWFyY2hRdWVyeSkge1xyXG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihzdHJlYW1lciA9PlxyXG4gICAgICAgIHN0cmVhbWVyLmRpc3BsYXlfbmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XHJcbiAgICAgICAgc3RyZWFtZXIudXNlcm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKSB8fFxyXG4gICAgICAgIHN0cmVhbWVyLmN1cnJlbnRfZ2FtZT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKVxyXG4gICAgICApO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChzaG93TGl2ZU9ubHkpIHtcclxuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoc3RyZWFtZXIgPT4gc3RyZWFtZXIuaXNfbGl2ZSk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHNlbGVjdGVkR2FtZSkge1xyXG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihzdHJlYW1lciA9PiBzdHJlYW1lci5jdXJyZW50X2dhbWUgPT09IHNlbGVjdGVkR2FtZSk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKG1pbkZvbGxvd2VycyA+IDApIHtcclxuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoc3RyZWFtZXIgPT4gc3RyZWFtZXIuZm9sbG93ZXJfY291bnQgPj0gbWluRm9sbG93ZXJzKTtcclxuICAgIH1cclxuXHJcbiAgICBzZXRGaWx0ZXJlZFN0cmVhbWVycyhmaWx0ZXJlZCk7XHJcbiAgfSwgW3N0cmVhbWVycywgc2VhcmNoUXVlcnksIHNob3dMaXZlT25seSwgc2VsZWN0ZWRHYW1lLCBtaW5Gb2xsb3dlcnNdKTtcclxuXHJcbiAgLy8gRmV0Y2ggaW5pdGlhbCBkYXRhXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZldGNoU3RhdHMoKTtcclxuICAgIGZldGNoVXNlclN0YXR1cygpO1xyXG4gICAgZmV0Y2hBc3NpZ25tZW50cygpO1xyXG4gICAgLy8gQXV0by1sb2FkIHN0cmVhbWVycyBvbiBwYWdlIGxvYWRcclxuICAgIGZldGNoTmV3U3RyZWFtZXJzKCk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBmZXRjaFVzZXJTdGF0dXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlci9zdGF0dXNgLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0pO1xyXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHNldFVzZXJTdGF0dXMoZGF0YSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIHVzZXIgc3RhdHVzIC0gU2VydmVyIHJlc3BvbmRlZCB3aXRoOicsIHJlc3BvbnNlLnN0YXR1cyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggdXNlciBzdGF0dXMgLSBDb25uZWN0aW9uIGVycm9yOicsIGVycik7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZmV0Y2hTdGF0cyA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdGZXRjaGluZyBzdGF0cyB1c2luZyBBUEkgY2xpZW50Li4uJyk7XHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBhcGlDbGllbnQuZ2V0U3RyZWFtZXJTdGF0cygpO1xyXG4gICAgICBzZXRTdGF0cyh7XHJcbiAgICAgICAgdG90YWxfc3RyZWFtZXJzOiBkYXRhLnRvdGFsX3N0cmVhbWVycyxcclxuICAgICAgICBsaXZlX3N0cmVhbWVyczogZGF0YS5saXZlX3N0cmVhbWVycyxcclxuICAgICAgICBhdmFpbGFibGVfc3RyZWFtZXJzOiBkYXRhLmF2YWlsYWJsZV9zdHJlYW1lcnMsXHJcbiAgICAgICAgYXZlcmFnZV9mb2xsb3dlcnM6IDI1IC8vIFVzZSBzdGF0aWMgdmFsdWUgc2luY2UgYmFja2VuZCBkb2Vzbid0IGNhbGN1bGF0ZSB0aGlzXHJcbiAgICAgIH0pO1xyXG4gICAgICBzZXRCYWNrZW5kQ29ubmVjdGVkKHRydWUpO1xyXG4gICAgICBjb25zb2xlLmxvZygnU3RhdHMgZmV0Y2hlZCBzdWNjZXNzZnVsbHk6JywgZGF0YSk7XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIHN0YXRzIC0gQ29ubmVjdGlvbiBlcnJvcjonLCBlcnIpO1xyXG4gICAgICBzZXRCYWNrZW5kQ29ubmVjdGVkKGZhbHNlKTtcclxuICAgICAgLy8gU2V0IGRlZmF1bHQgc3RhdHMgaWYgYmFja2VuZCBpcyBub3QgYXZhaWxhYmxlXHJcbiAgICAgIHNldFN0YXRzKHtcclxuICAgICAgICB0b3RhbF9zdHJlYW1lcnM6IDAsXHJcbiAgICAgICAgbGl2ZV9zdHJlYW1lcnM6IDAsXHJcbiAgICAgICAgYXZhaWxhYmxlX3N0cmVhbWVyczogMCxcclxuICAgICAgICBhdmVyYWdlX2ZvbGxvd2VyczogMFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBmZXRjaE5ld1N0cmVhbWVycyA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCc9PT0gRkVUQ0ggTkVXIFNUUkVBTUVSUyBCVVRUT04gQ0xJQ0tFRCA9PT0nKTtcclxuICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICBzZXRFcnJvcihudWxsKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygnRmV0Y2hpbmcgc3RyZWFtZXJzIHVzaW5nIEFQSSBjbGllbnQuLi4nKTtcclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGFwaUNsaWVudC5nZXRBdmFpbGFibGVTdHJlYW1lcnMoNTApO1xyXG4gICAgICBjb25zb2xlLmxvZygnUmVjZWl2ZWQgZGF0YTonLCBkYXRhKTtcclxuXHJcbiAgICAgIGNvbnN0IHN0cmVhbWVyc0FycmF5ID0gZGF0YS5zdHJlYW1lcnMgfHwgW107XHJcbiAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW1lcnMgYXJyYXk6Jywgc3RyZWFtZXJzQXJyYXkpO1xyXG5cclxuICAgICAgc2V0U3RyZWFtZXJzKHN0cmVhbWVyc0FycmF5KTtcclxuXHJcbiAgICAgIC8vIFVwZGF0ZSB1c2VyIHN0YXR1cyBmcm9tIHJlc3BvbnNlXHJcbiAgICAgIGlmIChkYXRhLnVzZXJfc3RhdHVzKSB7XHJcbiAgICAgICAgc2V0VXNlclN0YXR1cyh7XHJcbiAgICAgICAgICBkYWlseV9yZXF1ZXN0X2NvdW50OiBkYXRhLnVzZXJfc3RhdHVzLmRhaWx5X3JlcXVlc3RzX3VzZWQsXHJcbiAgICAgICAgICByZW1haW5pbmdfcmVxdWVzdHM6IGRhdGEudXNlcl9zdGF0dXMuZGFpbHlfcmVxdWVzdHNfcmVtYWluaW5nLFxyXG4gICAgICAgICAgbmV4dF9yZXNldF90aW1lOiBuZXcgRGF0ZShEYXRlLm5vdygpICsgMjQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSAvLyAyNCBob3VycyBmcm9tIG5vd1xyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBSZWZyZXNoIHN0YXRzIGFmdGVyIGZldGNoaW5nIHN0cmVhbWVyc1xyXG4gICAgICBhd2FpdCBmZXRjaFN0YXRzKCk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhg4pyFIFN1Y2Nlc3MhIExvYWRlZCAke3N0cmVhbWVyc0FycmF5Lmxlbmd0aH0gcmVhbCBzdHJlYW1lcnMgZnJvbSB5b3VyIHNjcmFwZXIgZGF0YSFgKTtcclxuXHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gZmV0Y2ggc3RyZWFtZXJzJztcclxuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcclxuICAgICAgY29uc29sZS5lcnJvcignPT09IEVSUk9SIEZFVENISU5HIFNUUkVBTUVSUyA9PT0nLCBlcnIpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZmV0Y2hBc3NpZ25tZW50cyA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9hc3NpZ25tZW50c2AsIHtcclxuICAgICAgICBtZXRob2Q6ICdHRVQnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgfSxcclxuICAgICAgfSk7XHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgc2V0QXNzaWdubWVudHMoZGF0YS5hc3NpZ25tZW50cyk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIGFzc2lnbm1lbnRzIC0gU2VydmVyIHJlc3BvbmRlZCB3aXRoOicsIHJlc3BvbnNlLnN0YXR1cyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggYXNzaWdubWVudHMgLSBDb25uZWN0aW9uIGVycm9yOicsIGVycik7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlVXBkYXRlQXNzaWdubWVudCA9IGFzeW5jIChpZDogc3RyaW5nLCBzdGF0dXM6IEFzc2lnbm1lbnRbJ3N0YXR1cyddLCBub3Rlczogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9CQVNFX1VSTH0vYXNzaWdubWVudHMvJHtpZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBzdGF0dXMsIG5vdGVzIH0pLFxyXG4gICAgICB9KTtcclxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgZmV0Y2hBc3NpZ25tZW50cygpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgYXNzaWdubWVudCAtIFNlcnZlciByZXNwb25kZWQgd2l0aDonLCByZXNwb25zZS5zdGF0dXMpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHVwZGF0ZSBhc3NpZ25tZW50IC0gQ29ubmVjdGlvbiBlcnJvcjonLCBlcnIpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUFzc2lnbm1lbnQgPSBhc3luYyAoaWQ6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L2Fzc2lnbm1lbnRzLyR7aWR9YCwge1xyXG4gICAgICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICAgIH0pO1xyXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgICBmZXRjaEFzc2lnbm1lbnRzKCk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGRlbGV0ZSBhc3NpZ25tZW50IC0gU2VydmVyIHJlc3BvbmRlZCB3aXRoOicsIHJlc3BvbnNlLnN0YXR1cyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIGFzc2lnbm1lbnQgLSBDb25uZWN0aW9uIGVycm9yOicsIGVycik7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgaWYgKCF1c2VyKSB7XHJcbiAgICByZXR1cm4gPGRpdj5QbGVhc2Ugc2lnbiBpbiB0byBhY2Nlc3MgdGhlIGRhc2hib2FyZC48L2Rpdj47XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgcHktOFwiPlxyXG4gICAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XHJcbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPvCfjq8gU3RyZWFtZXIgRGFzaGJvYXJkPC9oMT5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgV2VsY29tZSBiYWNrLCB7dXNlcj8ubmFtZSB8fCAnQWdlbnQnfSEgRGlzY292ZXIgYW5kIG1hbmFnZSB5b3VyIHN0cmVhbWVyIG91dHJlYWNoIGNhbXBhaWducy5cclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIFN0YXRzIENhcmRzICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNiBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBtYi04XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ibHVlLTEwMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1ibHVlLTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxyXG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTcgMjBoNXYtMmEzIDMgMCAwMC01LjM1Ni0xLjg1N00xNyAyMEg3bTEwIDB2LTJjMC0uNjU2LS4xMjYtMS4yODMtLjM1Ni0xLjg1N003IDIwSDJ2LTJhMyAzIDAgMDE1LjM1Ni0xLjg1N003IDIwdi0yYzAtLjY1Ni4xMjYtMS4yODMuMzU2LTEuODU3bTAgMGE1LjAwMiA1LjAwMiAwIDAxOS4yODggME0xNSA3YTMgMyAwIDExLTYgMCAzIDMgMCAwMTYgMHptNiAzYTIgMiAwIDExLTQgMCAyIDIgMCAwMTQgMHpNNyAxMGEyIDIgMCAxMS00IDAgMiAyIDAgMDE0IDB6XCIgLz5cclxuICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNFwiPlxyXG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMFwiPlRvdGFsIFN0cmVhbWVyczwvaDM+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzdGF0cz8udG90YWxfc3RyZWFtZXJzIHx8IDB9PC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLWdyZWVuLTEwMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1ncmVlbi02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cclxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE1IDEyYTMgMyAwIDExLTYgMCAzIDMgMCAwMTYgMHpcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMi40NTggMTJDMy43MzIgNy45NDMgNy41MjMgNSAxMiA1YzQuNDc4IDAgOC4yNjggMi45NDMgOS41NDIgNy0xLjI3NCA0LjA1Ny01LjA2NCA3LTkuNTQyIDctNC40NzcgMC04LjI2OC0yLjk0My05LjU0Mi03elwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cclxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDBcIj5MaXZlIE5vdzwvaDM+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzdGF0cz8ubGl2ZV9zdHJlYW1lcnMgfHwgMH08L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctcHVycGxlLTEwMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1wdXJwbGUtNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk05IDEybDIgMiA0LTRtNiAyYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cclxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDBcIj5BdmFpbGFibGU8L2gzPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57c3RhdHM/LmF2YWlsYWJsZV9zdHJlYW1lcnMgfHwgMH08L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmcteWVsbG93LTEwMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC15ZWxsb3ctNjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMyAxMFYzTDQgMTRoN3Y3bDktMTFoLTd6XCIgLz5cclxuICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNFwiPlxyXG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMFwiPkxvYWRlZDwvaDM+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzdHJlYW1lcnMubGVuZ3RofTwvcD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIEFjdGlvbiBCdXR0b25zICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQgaXRlbXMtc3RhcnQgc206aXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMVwiPlN0cmVhbWVyIERpc2NvdmVyeTwvaDM+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc21cIj5Mb2FkIGZyZXNoIHN0cmVhbWVycyBmcm9tIHlvdXIgYXV0b21hdGVkIHNjcmFwZXI8L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0zXCI+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoTmV3U3RyZWFtZXJzfVxyXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCAodXNlclN0YXR1cz8ucmVtYWluaW5nX3JlcXVlc3RzID09PSAwKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBweS0zIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdGV4dC13aGl0ZSB0cmFuc2l0aW9uLWFsbCAke1xyXG4gICAgICAgICAgICAgICAgICAgIHVzZXJTdGF0dXM/LnJlbWFpbmluZ19yZXF1ZXN0cyA9PT0gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JheS00MDAgY3Vyc29yLW5vdC1hbGxvd2VkJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgOiBsb2FkaW5nXHJcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTQwMCBjdXJzb3Itd2FpdCdcclxuICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIGhvdmVyOnNoYWRvdy1sZydcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHtsb2FkaW5nID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIC1tbC0xIG1yLTMgaC01IHctNSB0ZXh0LXdoaXRlXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGNsYXNzTmFtZT1cIm9wYWNpdHktMjVcIiBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiNFwiPjwvY2lyY2xlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBjbGFzc05hbWU9XCJvcGFjaXR5LTc1XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIGQ9XCJNNCAxMmE4IDggMCAwMTgtOFYwQzUuMzczIDAgMCA1LjM3MyAwIDEyaDR6bTIgNS4yOTFBNy45NjIgNy45NjIgMCAwMTQgMTJIMGMwIDMuMDQyIDEuMTM1IDUuODI0IDMgNy45MzhsMy0yLjY0N3pcIj48L3BhdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgIExvYWRpbmcuLi5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgKSA6IHVzZXJTdGF0dXM/LnJlbWFpbmluZ19yZXF1ZXN0cyA9PT0gMCA/IChcclxuICAgICAgICAgICAgICAgICAgICAn4p2MIERhaWx5IExpbWl0IFJlYWNoZWQnXHJcbiAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgJ/CflIQgUmVmcmVzaCBTdHJlYW1lcnMnXHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogU3RhdHVzIGluZGljYXRvcnMgKi99XHJcbiAgICAgICAgICAgIHt1c2VyU3RhdHVzICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgcHQtNCBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkRhaWx5IHJlcXVlc3RzOiB7dXNlclN0YXR1cy5kYWlseV9yZXF1ZXN0X2NvdW50fS8zPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIgaC0yIHJvdW5kZWQtZnVsbCBtci0yICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZW5kQ29ubmVjdGVkID09PSB0cnVlID8gJ2JnLWdyZWVuLTUwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgYmFja2VuZENvbm5lY3RlZCA9PT0gZmFsc2UgPyAnYmctcmVkLTUwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgJ2JnLXllbGxvdy01MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtiYWNrZW5kQ29ubmVjdGVkID09PSB0cnVlID8gJ0JhY2tlbmQgQ29ubmVjdGVkJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgYmFja2VuZENvbm5lY3RlZCA9PT0gZmFsc2UgPyAnQmFja2VuZCBEaXNjb25uZWN0ZWQnIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAnQ2hlY2tpbmcuLi4nfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgIHtlcnJvciAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHAtMyBiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTcwMCB0ZXh0LXNtXCI+4pqg77iPIHtlcnJvcn08L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIFN0cmVhbWVycyBTZWN0aW9uICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxyXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cclxuICAgICAgICAgICAgICDwn46uIEF2YWlsYWJsZSBTdHJlYW1lcnMge3N0cmVhbWVycy5sZW5ndGggPiAwICYmIGAoJHtzdHJlYW1lcnMubGVuZ3RofSlgfVxyXG4gICAgICAgICAgICA8L2gyPlxyXG4gICAgICAgICAgICB7c3RyZWFtZXJzLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgICAgICBTaG93aW5nIHtzdHJlYW1lcnMubGVuZ3RofSBzdHJlYW1lcnNcclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHtsb2FkaW5nID8gKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgeGw6Z3JpZC1jb2xzLTQgZ2FwLTZcIj5cclxuICAgICAgICAgICAgICB7Wy4uLkFycmF5KDgpXS5tYXAoKF8sIGkpID0+IChcclxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNiBhbmltYXRlLXB1bHNlXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtNDggYmctZ3JheS0yMDAgcm91bmRlZC1sZyBtYi00XCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMjAwIHJvdW5kZWQgbWItMlwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1ncmF5LTIwMCByb3VuZGVkIHctMi8zXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApIDogc3RyZWFtZXJzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgIDxTdHJlYW1lckxpc3Qgc3RyZWFtZXJzPXtzdHJlYW1lcnN9IGFzc2lnbm1lbnRzPXthc3NpZ25tZW50c30gb25JbnRlcmVzdExldmVsQ2hhbmdlZD17KCkgPT4ge319IC8+XHJcbiAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTI0IGgtMjQgbXgtYXV0byBtYi00IHRleHQtZ3JheS00MDBcIj5cclxuICAgICAgICAgICAgICAgIDxzdmcgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17MX0gZD1cIk0xNyAyMGg1di0yYTMgMyAwIDAwLTUuMzU2LTEuODU3TTE3IDIwSDdtMTAgMHYtMmMwLS42NTYtLjEyNi0xLjI4My0uMzU2LTEuODU3TTcgMjBIMnYtMmEzIDMgMCAwMTUuMzU2LTEuODU3TTcgMjB2LTJjMC0uNjU2LjEyNi0xLjI4My4zNTYtMS44NTdtMCAwYTUuMDAyIDUuMDAyIDAgMDE5LjI4OCAwTTE1IDdhMyAzIDAgMTEtNiAwIDMgMyAwIDAxNiAwem02IDNhMiAyIDAgMTEtNCAwIDIgMiAwIDAxNCAwek03IDEwYTIgMiAwIDExLTQgMCAyIDIgMCAwMTQgMHpcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+Tm8gc3RyZWFtZXJzIGxvYWRlZCB5ZXQ8L2gzPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPkNsaWNrIFwiUmVmcmVzaCBTdHJlYW1lcnNcIiB0byBsb2FkIGF2YWlsYWJsZSBzdHJlYW1lcnMgZnJvbSB5b3VyIGRhdGFiYXNlPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBBc3NpZ25tZW50IEFuYWx5dGljcyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cclxuICAgICAgICAgIDxBc3NpZ25tZW50QW5hbHl0aWNzIGFzc2lnbm1lbnRzPXthc3NpZ25tZW50c30gLz5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIEFzc2lnbm1lbnQgTGlzdCAqL31cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPEFzc2lnbm1lbnRMaXN0XHJcbiAgICAgICAgICAgIGFzc2lnbm1lbnRzPXthc3NpZ25tZW50c31cclxuICAgICAgICAgICAgb25VcGRhdGU9e2hhbmRsZVVwZGF0ZUFzc2lnbm1lbnR9XHJcbiAgICAgICAgICAgIG9uRGVsZXRlPXtoYW5kbGVEZWxldGVBc3NpZ25tZW50fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsIlN0cmVhbWVyTGlzdCIsIkFzc2lnbm1lbnRMaXN0IiwiQXNzaWdubWVudEFuYWx5dGljcyIsImFwaUNsaWVudCIsIkRhc2hib2FyZFBhZ2UiLCJ1c2VyIiwic3RyZWFtZXJzIiwic2V0U3RyZWFtZXJzIiwiZmlsdGVyZWRTdHJlYW1lcnMiLCJzZXRGaWx0ZXJlZFN0cmVhbWVycyIsImFzc2lnbm1lbnRzIiwic2V0QXNzaWdubWVudHMiLCJzdGF0cyIsInNldFN0YXRzIiwidXNlclN0YXR1cyIsInNldFVzZXJTdGF0dXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJiYWNrZW5kQ29ubmVjdGVkIiwic2V0QmFja2VuZENvbm5lY3RlZCIsInNlYXJjaFF1ZXJ5Iiwic2V0U2VhcmNoUXVlcnkiLCJzaG93TGl2ZU9ubHkiLCJzZXRTaG93TGl2ZU9ubHkiLCJzZWxlY3RlZEdhbWUiLCJzZXRTZWxlY3RlZEdhbWUiLCJtaW5Gb2xsb3dlcnMiLCJzZXRNaW5Gb2xsb3dlcnMiLCJBUElfQkFTRV9VUkwiLCJmaWx0ZXJlZCIsImZpbHRlciIsInN0cmVhbWVyIiwiZGlzcGxheV9uYW1lIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInVzZXJuYW1lIiwiY3VycmVudF9nYW1lIiwiaXNfbGl2ZSIsImZvbGxvd2VyX2NvdW50IiwiZmV0Y2hTdGF0cyIsImZldGNoVXNlclN0YXR1cyIsImZldGNoQXNzaWdubWVudHMiLCJmZXRjaE5ld1N0cmVhbWVycyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwib2siLCJkYXRhIiwianNvbiIsImNvbnNvbGUiLCJzdGF0dXMiLCJlcnIiLCJsb2ciLCJnZXRTdHJlYW1lclN0YXRzIiwidG90YWxfc3RyZWFtZXJzIiwibGl2ZV9zdHJlYW1lcnMiLCJhdmFpbGFibGVfc3RyZWFtZXJzIiwiYXZlcmFnZV9mb2xsb3dlcnMiLCJnZXRBdmFpbGFibGVTdHJlYW1lcnMiLCJzdHJlYW1lcnNBcnJheSIsInVzZXJfc3RhdHVzIiwiZGFpbHlfcmVxdWVzdF9jb3VudCIsImRhaWx5X3JlcXVlc3RzX3VzZWQiLCJyZW1haW5pbmdfcmVxdWVzdHMiLCJkYWlseV9yZXF1ZXN0c19yZW1haW5pbmciLCJuZXh0X3Jlc2V0X3RpbWUiLCJEYXRlIiwibm93IiwidG9JU09TdHJpbmciLCJsZW5ndGgiLCJlcnJvck1lc3NhZ2UiLCJFcnJvciIsIm1lc3NhZ2UiLCJoYW5kbGVVcGRhdGVBc3NpZ25tZW50IiwiaWQiLCJub3RlcyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiaGFuZGxlRGVsZXRlQXNzaWdubWVudCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsIm5hbWUiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJoMyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInhtbG5zIiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzcGFuIiwiaDIiLCJBcnJheSIsIm1hcCIsIl8iLCJpIiwib25JbnRlcmVzdExldmVsQ2hhbmdlZCIsIm9uVXBkYXRlIiwib25EZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});