#!/usr/bin/env python3
"""
Automated Twitch Scraper for 24/7 Operation
Runs scraper, loads data to database, and provides comprehensive logging
"""

import os
import sys
import logging
import traceback
from datetime import datetime, timedelta
import pandas as pd
import requests
import json
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from the backend .env file
backend_env_path = Path(__file__).parent / "artwork-outreach-web-app" / "backend" / ".env"
if backend_env_path.exists():
    load_dotenv(backend_env_path)
    print(f"✅ Loaded environment variables from: {backend_env_path}")
else:
    print(f"⚠️ Environment file not found at: {backend_env_path}")

# Add the artwork-outreach-web-app/backend to Python path for imports
backend_path = Path(__file__).parent / "artwork-outreach-web-app" / "backend"
sys.path.insert(0, str(backend_path))

def setup_logging():
    """Setup comprehensive logging for automation"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Create log filename with timestamp
    log_file = log_dir / f"scraper_automation_{datetime.now().strftime('%Y%m%d')}.log"
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def run_scraper(logger):
    """Run the main Twitch scraper"""
    logger.info("🤖 Starting Twitch scraper...")
    
    try:
        # Import and run the scraper
        from twitch_scraper import main as run_twitch_scraper
        
        # Run the scraper
        result = run_twitch_scraper()
        
        # Check if CSV file was created
        csv_file = Path("twitch_streams_filtered.csv")
        if csv_file.exists():
            df = pd.read_csv(csv_file)
            logger.info(f"✅ Scraper completed successfully! Found {len(df)} qualifying streamers")
            return True, len(df)
        else:
            logger.error("❌ Scraper completed but no CSV file found")
            return False, 0
            
    except Exception as e:
        logger.error(f"❌ Scraper failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False, 0

def load_data_to_supabase(logger):
    """Load scraped data to Supabase database"""
    logger.info("📤 Loading data to Supabase...")
    
    try:
        # Import configuration
        from app.config import get_settings
        settings = get_settings()
        
        # Supabase configuration
        base_url = settings.SUPABASE_URL
        headers = {
            "apikey": settings.SUPABASE_SERVICE_ROLE_KEY,
            "Authorization": f"Bearer {settings.SUPABASE_SERVICE_ROLE_KEY}",
            "Content-Type": "application/json"
        }
        
        # Read CSV file
        csv_file = Path("twitch_streams_filtered.csv")
        if not csv_file.exists():
            logger.error("❌ No CSV file found to load")
            return False, 0
        
        df = pd.read_csv(csv_file)
        logger.info(f"📊 Found {len(df)} streamers in CSV")
        
        success_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                # Prepare streamer data
                streamer_data = {
                    "twitch_user_id": str(row['username']),
                    "username": str(row['username']),
                    "display_name": str(row['display_name']),
                    "follower_count": int(row['follower_count']),
                    "is_live": True,
                    "current_game": str(row['game_name']),
                    "stream_title": str(row['title']),
                    "language": str(row['language']),
                    "thumbnail_url": str(row.get('thumbnail_url', '')),
                    "profile_image_url": str(row.get('thumbnail_url', '')),
                    "last_seen_live_at": datetime.now().isoformat()
                }
                
                # Try to insert/update streamer
                response = requests.post(
                    f"{base_url}/rest/v1/streamers",
                    headers=headers,
                    json=streamer_data,
                    timeout=10
                )
                
                if response.status_code in [200, 201]:
                    success_count += 1
                else:
                    # Try update if insert failed
                    update_response = requests.patch(
                        f"{base_url}/rest/v1/streamers?twitch_user_id=eq.{row['username']}",
                        headers=headers,
                        json=streamer_data,
                        timeout=10
                    )
                    
                    if update_response.status_code in [200, 204]:
                        success_count += 1
                    else:
                        error_count += 1
                        logger.warning(f"⚠️ Failed to load {row['username']}: {response.status_code}")
                        
            except Exception as e:
                error_count += 1
                logger.warning(f"⚠️ Exception loading {row['username']}: {e}")
        
        logger.info(f"📈 Data loading completed: {success_count} successful, {error_count} errors")
        return True, success_count
        
    except Exception as e:
        logger.error(f"❌ Data loading failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False, 0

def cleanup_old_files(logger, days_to_keep=7):
    """Clean up old CSV files and logs"""
    logger.info(f"🧹 Cleaning up files older than {days_to_keep} days...")
    
    try:
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # Clean up old CSV files
        for csv_file in Path(".").glob("twitch_streams_*.csv"):
            if csv_file.stat().st_mtime < cutoff_date.timestamp():
                csv_file.unlink()
                logger.info(f"🗑️ Deleted old CSV: {csv_file}")
        
        # Clean up old log files
        log_dir = Path("logs")
        if log_dir.exists():
            for log_file in log_dir.glob("*.log"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    logger.info(f"🗑️ Deleted old log: {log_file}")
        
        logger.info("✅ Cleanup completed")
        
    except Exception as e:
        logger.warning(f"⚠️ Cleanup failed: {e}")

def main():
    """Main automation function"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("🚀 AUTOMATED TWITCH SCRAPER STARTED")
    logger.info(f"⏰ Timestamp: {datetime.now().isoformat()}")
    logger.info("=" * 60)
    
    try:
        # Step 1: Run the scraper
        scraper_success, streamers_found = run_scraper(logger)
        
        if scraper_success and streamers_found > 0:
            # Step 2: Load data to database
            load_success, streamers_loaded = load_data_to_supabase(logger)
            
            if load_success:
                logger.info(f"🎉 Automation completed successfully!")
                logger.info(f"📊 Streamers found: {streamers_found}")
                logger.info(f"📤 Streamers loaded: {streamers_loaded}")
            else:
                logger.error("❌ Data loading failed")
        else:
            logger.error("❌ Scraper failed or found no streamers")
        
        # Step 3: Cleanup old files
        cleanup_old_files(logger)
        
        logger.info("=" * 60)
        logger.info("✅ AUTOMATED SCRAPER COMPLETED")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"💥 Critical error in automation: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
