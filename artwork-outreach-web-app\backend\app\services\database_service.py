from sqlalchemy.orm import Session
from app.database.models import Streamer
from app.schemas.streamer import StreamerCreate
import logging
import threading
from typing import List
from app.utils.database import monitor_db_performance

logger = logging.getLogger(__name__)

class DatabaseService:
    def __init__(self, db: Session):
        self.db = db
        self._lock = threading.Lock()

    @monitor_db_performance
    def upsert_streamer(self, streamer_data: StreamerCreate):
        """
        Update a streamer if it exists, or create it if it doesn't.
        This operation is thread-safe.
        """
        with self._lock:
            existing_streamer = self.db.query(Streamer).filter(Streamer.twitch_user_id == streamer_data.twitch_user_id).first()

            if existing_streamer:
                # Update existing streamer
                for key, value in streamer_data.dict().items():
                    setattr(existing_streamer, key, value)
                logger.info(f"Updating streamer: {streamer_data.username}")
            else:
                # Create new streamer
                new_streamer = Streamer(**streamer_data.dict())
                self.db.add(new_streamer)
                logger.info(f"Creating new streamer: {streamer_data.username}")
            
            self.db.commit()

    @monitor_db_performance
    def bulk_upsert_streamers(self, streamers_data: List[StreamerCreate]):
        """
        Upserts a list of streamers in a single transaction.
        """
        with self.db.begin():
            for streamer_data in streamers_data:
                self.db.merge(Streamer(**streamer_data.dict()))
        logger.info(f"Bulk upserted {len(streamers_data)} streamers.")
