from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import UJSONResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from .config import get_settings
from .api.routes import health, users, streamers, assignments, worker, admin, fallback, automation
from app.api.routes.streamers_new import router as streamers_new_router
from .core.exceptions import UserNotFoundException, InvalidRequestException, AuthenticationException, RateLimitExceededException
from .api.middleware.rate_limiting import RateLimitingMiddleware
from .api.middleware.performance_monitoring import PerformanceMonitoringMiddleware
from .core.cache import init_cache
# Remove the problematic import for now
from app.api.dependencies import get_current_user
from tests.test_dependencies import get_test_user
 
settings = get_settings()
 
app = FastAPI(
    title="Artwork Outreach API",
    description="API for the Artwork Outreach platform.",
    version="0.1.0",
    default_response_class=UJSONResponse
)

if settings.ENVIRONMENT == "testing":
    app.dependency_overrides[get_current_user] = get_test_user

@app.on_event("startup")
async def startup_event():
    """
    Actions to be performed on application startup.
    """
    init_cache()
    print("✅ Artwork Outreach API started successfully!")
    print("📖 API Documentation: http://127.0.0.1:8000/docs")
    print("🔧 Health Check: http://127.0.0.1:8000/api/v1/health")

@app.exception_handler(UserNotFoundException)
async def user_not_found_exception_handler(request: Request, exc: UserNotFoundException):
    return JSONResponse(
        status_code=404,
        content={"message": "User not found"},
    )

@app.exception_handler(InvalidRequestException)
async def invalid_request_exception_handler(request: Request, exc: InvalidRequestException):
    return JSONResponse(
        status_code=400,
        content={"message": "Invalid request"},
    )

@app.exception_handler(AuthenticationException)
async def authentication_exception_handler(request: Request, exc: AuthenticationException):
    return JSONResponse(
        status_code=401,
        content={"message": "Authentication failed"},
    )

@app.exception_handler(RateLimitExceededException)
async def rate_limit_exceeded_exception_handler(request: Request, exc: RateLimitExceededException):
    return JSONResponse(
        status_code=429,
        content={"message": "Rate limit exceeded"},
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors()},
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
    )

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

app.add_middleware(RateLimitingMiddleware)

app.add_middleware(PerformanceMonitoringMiddleware)

api_router = APIRouter(prefix="/api/v1")

api_router.include_router(health.router, tags=["Health"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(streamers.router, prefix="/streamers-old", tags=["Streamers-Old"])
app.include_router(streamers_new_router, tags=["Streamers"])
api_router.include_router(assignments.router, prefix="/assignments", tags=["Assignments"])
api_router.include_router(worker.router, prefix="/worker", tags=["Worker"])
api_router.include_router(admin.router, prefix="/admin", tags=["Admin"])
api_router.include_router(fallback.router, prefix="/fallback", tags=["Fallback"])
api_router.include_router(automation.router, prefix="/automation", tags=["Automation"])


app.include_router(api_router)