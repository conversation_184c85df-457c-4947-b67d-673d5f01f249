#!/usr/bin/env python3
"""
Simple script to load existing Twitch scraper CSV data into the web application database.
Run this if you already have scraper output and just want to load it into the web app.
"""

import asyncio
import pandas as pd
from datetime import datetime
from pathlib import Path
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
from app.database.models import Base, Streamer, ScraperRun
from app.config import get_settings

settings = get_settings()

# Create async engine for the web app database
engine = create_async_engine(settings.DATABASE_URL)
AsyncSessionLocal = async_sessionmaker(autocommit=False, autoflush=False, bind=engine)


async def create_tables():
    """Create database tables if they don't exist."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("✅ Database tables created/verified")


async def load_csv_data(csv_path: str):
    """Load streamer data from CSV file into database."""
    csv_file = Path(csv_path)
    if not csv_file.exists():
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    # Read the CSV data
    df = pd.read_csv(csv_file)
    print(f"📊 Found {len(df)} streamers in CSV file")
    
    # Create a scraper run record
    async with AsyncSessionLocal() as session:
        scraper_run = ScraperRun(
            status="started",
            details=f"Loading data from {csv_path}"
        )
        session.add(scraper_run)
        await session.commit()
        await session.refresh(scraper_run)
        run_id = scraper_run.id
    
    try:
        async with AsyncSessionLocal() as session:
            streamers_added = 0
            streamers_updated = 0
            
            for _, row in df.iterrows():
                # Extract data from CSV (handle different possible column names)
                username = row.get('username', '')
                if not username:
                    continue
                
                # Check if streamer already exists (by twitch_user_id)
                twitch_user_id = str(row.get('twitch_user_id', username))
                
                # Try to find existing streamer
                from sqlalchemy import select
                stmt = select(Streamer).where(Streamer.twitch_user_id == twitch_user_id)
                result = await session.execute(stmt)
                existing_streamer = result.scalar_one_or_none()
                
                if existing_streamer:
                    # Update existing streamer
                    existing_streamer.username = username
                    existing_streamer.display_name = row.get('display_name', username)
                    existing_streamer.follower_count = int(row.get('follower_count', 0))
                    existing_streamer.is_live = True  # They were live when scraped
                    existing_streamer.current_game = row.get('game_name', '')
                    existing_streamer.stream_title = row.get('title', '')
                    existing_streamer.thumbnail_url = row.get('thumbnail_url', '')
                    existing_streamer.language = row.get('language', 'en')
                    existing_streamer.last_seen_live_at = datetime.utcnow()
                    existing_streamer.updated_at = datetime.utcnow()
                    streamers_updated += 1
                else:
                    # Create new streamer
                    streamer = Streamer(
                        twitch_user_id=twitch_user_id,
                        username=username,
                        display_name=row.get('display_name', username),
                        follower_count=int(row.get('follower_count', 0)),
                        is_live=True,  # They were live when scraped
                        current_game=row.get('game_name', ''),
                        stream_title=row.get('title', ''),
                        thumbnail_url=row.get('thumbnail_url', ''),
                        language=row.get('language', 'en'),
                        last_seen_live_at=datetime.utcnow()
                    )
                    session.add(streamer)
                    streamers_added += 1
            
            await session.commit()
            
            # Update scraper run record
            scraper_run = await session.get(ScraperRun, run_id)
            scraper_run.status = "completed"
            scraper_run.streamers_found = streamers_added + streamers_updated
            scraper_run.details = f"Loaded from CSV: {streamers_added} added, {streamers_updated} updated"
            await session.commit()
            
            print(f"✅ Database updated: {streamers_added} new streamers, {streamers_updated} updated")
            return streamers_added + streamers_updated
            
    except Exception as e:
        # Update scraper run record with error
        async with AsyncSessionLocal() as session:
            scraper_run = await session.get(ScraperRun, run_id)
            scraper_run.status = "failed"
            scraper_run.details = f"Error loading CSV: {str(e)}"
            await session.commit()
        
        print(f"❌ Failed to load CSV data: {e}")
        raise


async def get_database_stats():
    """Get current database statistics."""
    async with AsyncSessionLocal() as session:
        from sqlalchemy import text
        
        # Count total streamers
        total_result = await session.execute(text("SELECT COUNT(*) FROM streamers"))
        total_streamers = total_result.scalar()
        
        # Count live streamers
        live_result = await session.execute(text("SELECT COUNT(*) FROM streamers WHERE is_live = 1"))
        live_streamers = live_result.scalar()
        
        # Count available streamers (0-50 followers, English, live)
        available_result = await session.execute(text("""
            SELECT COUNT(*) FROM streamers 
            WHERE is_live = 1 
            AND follower_count BETWEEN 0 AND 50 
            AND language = 'en'
        """))
        available_streamers = available_result.scalar()
        
        print(f"\n📈 Database Statistics:")
        print(f"   Total streamers: {total_streamers}")
        print(f"   Live streamers: {live_streamers}")
        print(f"   Available for outreach: {available_streamers}")


async def main():
    """Main function to load CSV data into database."""
    # Look for CSV files in the parent directory (where scraper runs)
    possible_files = [
        "../twitch_streams_filtered.csv",
        "twitch_streams_filtered.csv", 
        "../twitch_scraper_results.csv"
    ]
    
    csv_file = None
    for file_path in possible_files:
        if Path(file_path).exists():
            csv_file = file_path
            break
    
    if not csv_file:
        print("❌ No CSV file found. Please run the Twitch scraper first or specify a CSV file.")
        print("   Looking for: twitch_streams_filtered.csv")
        return
    
    print(f"🚀 Loading real Twitch data from {csv_file}...")
    
    try:
        # Create tables
        await create_tables()
        
        # Show current stats
        await get_database_stats()
        
        # Load CSV data
        total_loaded = await load_csv_data(csv_file)
        
        # Show updated stats
        await get_database_stats()
        
        print(f"\n🎉 Successfully loaded {total_loaded} streamers into the database!")
        print("   Your web application now has real Twitch data.")
        print("   Restart your FastAPI server and refresh the frontend to see the changes.")
        
    except Exception as e:
        print(f"\n💥 Error: {e}")
        return


if __name__ == "__main__":
    asyncio.run(main())