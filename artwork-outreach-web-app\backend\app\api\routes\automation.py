"""
Automation API endpoints for 24/7 scraper management
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import logging

from app.scheduler import (
    start_automation,
    stop_automation, 
    get_automation_status,
    trigger_manual_scrape,
    automation_scheduler
)
from app.api.dependencies import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/status")
async def get_scheduler_status() -> Dict[str, Any]:
    """Get the current status of the automation scheduler"""
    try:
        status = get_automation_status()
        return {
            "scheduler": status,
            "timestamp": "2025-07-04T19:00:00Z"
        }
    except Exception as e:
        logger.error(f"Error getting scheduler status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting status: {str(e)}")

@router.post("/start")
async def start_scheduler(current_user: dict = Depends(get_current_user)) -> Dict[str, str]:
    """Start the automation scheduler"""
    try:
        await start_automation()
        return {
            "message": "Automation scheduler started successfully",
            "status": "running"
        }
    except Exception as e:
        logger.error(f"Error starting scheduler: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting scheduler: {str(e)}")

@router.post("/stop")
async def stop_scheduler(current_user: dict = Depends(get_current_user)) -> Dict[str, str]:
    """Stop the automation scheduler"""
    try:
        await stop_automation()
        return {
            "message": "Automation scheduler stopped successfully", 
            "status": "stopped"
        }
    except Exception as e:
        logger.error(f"Error stopping scheduler: {e}")
        raise HTTPException(status_code=500, detail=f"Error stopping scheduler: {str(e)}")

@router.post("/trigger-scrape")
async def trigger_manual_scrape_endpoint(current_user: dict = Depends(get_current_user)) -> Dict[str, str]:
    """Manually trigger a scrape job"""
    try:
        await trigger_manual_scrape()
        return {
            "message": "Manual scrape triggered successfully",
            "status": "triggered",
            "triggered_by": current_user.get("email", "unknown")
        }
    except Exception as e:
        logger.error(f"Error triggering manual scrape: {e}")
        raise HTTPException(status_code=500, detail=f"Error triggering scrape: {str(e)}")

@router.post("/schedule/daily")
async def schedule_daily_scrape(
    hour: int = 3, 
    minute: int = 0,
    current_user: dict = Depends(get_current_user)
) -> Dict[str, str]:
    """Schedule daily scraping at specified time (UTC)"""
    try:
        if not (0 <= hour <= 23):
            raise HTTPException(status_code=400, detail="Hour must be between 0 and 23")
        if not (0 <= minute <= 59):
            raise HTTPException(status_code=400, detail="Minute must be between 0 and 59")
        
        await automation_scheduler.reschedule_daily_job(hour, minute)
        return {
            "message": f"Daily scrape scheduled for {hour:02d}:{minute:02d} UTC",
            "status": "scheduled"
        }
    except Exception as e:
        logger.error(f"Error scheduling daily scrape: {e}")
        raise HTTPException(status_code=500, detail=f"Error scheduling: {str(e)}")

@router.post("/schedule/interval")
async def schedule_interval_scrape(
    hours: int = 12,
    current_user: dict = Depends(get_current_user)
) -> Dict[str, str]:
    """Schedule interval-based scraping"""
    try:
        if not (1 <= hours <= 168):  # 1 hour to 1 week
            raise HTTPException(status_code=400, detail="Hours must be between 1 and 168")
        
        await automation_scheduler.add_interval_scrape_job(hours)
        return {
            "message": f"Interval scrape scheduled every {hours} hours",
            "status": "scheduled"
        }
    except Exception as e:
        logger.error(f"Error scheduling interval scrape: {e}")
        raise HTTPException(status_code=500, detail=f"Error scheduling: {str(e)}")

@router.get("/logs/recent")
async def get_recent_logs(lines: int = 50) -> Dict[str, Any]:
    """Get recent automation logs"""
    try:
        # This is a simplified version - in production you'd read from actual log files
        return {
            "message": "Log retrieval not implemented yet",
            "lines_requested": lines,
            "logs": [
                "2025-07-04 19:00:00 - INFO - Scheduler started successfully",
                "2025-07-04 19:00:00 - INFO - Daily scrape job scheduled for 03:00 UTC",
                "2025-07-04 19:00:00 - INFO - Health check job scheduled every 30 minutes"
            ]
        }
    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting logs: {str(e)}")

@router.get("/health")
async def automation_health_check() -> Dict[str, Any]:
    """Health check for automation system"""
    try:
        status = get_automation_status()
        
        health_status = "healthy" if status["status"] == "running" else "unhealthy"
        
        return {
            "status": health_status,
            "scheduler_running": status["status"] == "running",
            "active_jobs": status.get("total_jobs", 0),
            "timestamp": "2025-07-04T19:00:00Z"
        }
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2025-07-04T19:00:00Z"
        }
