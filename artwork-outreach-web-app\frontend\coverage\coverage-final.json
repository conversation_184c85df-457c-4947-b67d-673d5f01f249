{"/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/layout.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/layout.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 14}, "end": {"line": 4, "column": 43}}, "1": {"start": {"line": 6, "column": 24}, "end": {"line": 9, "column": 1}}, "2": {"start": {"line": 16, "column": 2}, "end": {"line": 22, "column": 3}}}, "fnMap": {"0": {"name": "RootLayout", "decl": {"start": {"line": 11, "column": 24}, "end": {"line": 11, "column": 34}}, "loc": {"start": {"line": 15, "column": 3}, "end": {"line": 23, "column": 1}}, "line": 15}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/page.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/page.tsx", "statementMap": {"0": {"start": {"line": 2, "column": 2}, "end": {"line": 2, "column": 29}}}, "fnMap": {"0": {"name": "Page", "decl": {"start": {"line": 1, "column": 24}, "end": {"line": 1, "column": 28}}, "loc": {"start": {"line": 1, "column": 31}, "end": {"line": 3, "column": 1}}, "line": 1}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-in/page.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-in/page.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 2}, "end": {"line": 8, "column": 4}}}, "fnMap": {"0": {"name": "SignInPage", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 34}}, "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 9, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 1}, "f": {"0": 1}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "f7002810cea928666ac27e57fac15f6b7805509b"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-out/page.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-out/page.tsx", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-up/page.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/auth/sign-up/page.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 20}}}, "fnMap": {"0": {"name": "Page", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 28}}, "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 5, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 1}, "f": {"0": 1}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "203d475dbe1c9ea886b3a0f0ffae45b5cdd6ffb7"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/dashboard/layout.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/dashboard/layout.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 53}}}, "fnMap": {"0": {"name": "DashboardLayout", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 39}}, "loc": {"start": {"line": 7, "column": 3}, "end": {"line": 9, "column": 1}}, "line": 7}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/dashboard/page.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/dashboard/page.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 2}, "end": {"line": 9, "column": 4}}}, "fnMap": {"0": {"name": "DashboardPage", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 37}}, "loc": {"start": {"line": 3, "column": 40}, "end": {"line": 10, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/user-profile/page.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/app/user-profile/page.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 24}, "end": {"line": 7, "column": 1}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 6, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 25}}, "loc": {"start": {"line": 4, "column": 2}, "end": {"line": 6, "column": 8}}, "line": 4}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/ProtectedRoute.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/ProtectedRoute.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 23}, "end": {"line": 26, "column": 1}}, "1": {"start": {"line": 8, "column": 41}, "end": {"line": 8, "column": 50}}, "2": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 28}}, "3": {"start": {"line": 11, "column": 2}, "end": {"line": 15, "column": 43}}, "4": {"start": {"line": 12, "column": 4}, "end": {"line": 14, "column": 5}}, "5": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 35}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, "7": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 33}}, "8": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "9": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 16}}, "10": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 25}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 24}}, "loc": {"start": {"line": 7, "column": 72}, "end": {"line": 26, "column": 1}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 13}}, "loc": {"start": {"line": 11, "column": 18}, "end": {"line": 15, "column": 3}}, "line": 11}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 4}, "end": {"line": 14, "column": 5}}, "type": "if", "locations": [{"start": {"line": 12, "column": 4}, "end": {"line": 14, "column": 5}}, {"start": {}, "end": {}}], "line": 12}, "1": {"loc": {"start": {"line": 12, "column": 8}, "end": {"line": 12, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 8}, "end": {"line": 12, "column": 18}}, {"start": {"line": 12, "column": 22}, "end": {"line": 12, "column": 38}}], "line": 12}, "2": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, "type": "if", "locations": [{"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, {"start": {}, "end": {}}], "line": 17}, "3": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, {"start": {}, "end": {}}], "line": 21}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/SignInButton.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/SignInButton.tsx", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/UserButton.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/UserButton.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 26}, "end": {"line": 11, "column": 1}}, "1": {"start": {"line": 6, "column": 2}, "end": {"line": 10, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 26}, "end": {"line": 5, "column": 27}}, "loc": {"start": {"line": 5, "column": 32}, "end": {"line": 11, "column": 1}}, "line": 5}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/UserProfile.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/auth/UserProfile.tsx", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/Dashboard.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/Dashboard.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 69}, "end": {"line": 13, "column": 84}}, "1": {"start": {"line": 14, "column": 36}, "end": {"line": 14, "column": 60}}, "2": {"start": {"line": 15, "column": 40}, "end": {"line": 15, "column": 66}}, "3": {"start": {"line": 16, "column": 38}, "end": {"line": 16, "column": 53}}, "4": {"start": {"line": 17, "column": 28}, "end": {"line": 17, "column": 57}}, "5": {"start": {"line": 19, "column": 32}, "end": {"line": 31, "column": 3}}, "6": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 24}}, "7": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 19}}, "8": {"start": {"line": 22, "column": 4}, "end": {"line": 30, "column": 5}}, "9": {"start": {"line": 23, "column": 71}, "end": {"line": 23, "column": 104}}, "10": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 33}}, "11": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 37}}, "12": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 81}}, "13": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 27}}, "14": {"start": {"line": 33, "column": 30}, "end": {"line": 33, "column": 93}}, "15": {"start": {"line": 35, "column": 2}, "end": {"line": 52, "column": 4}}}, "fnMap": {"0": {"name": "Dashboard", "decl": {"start": {"line": 12, "column": 16}, "end": {"line": 12, "column": 25}}, "loc": {"start": {"line": 12, "column": 28}, "end": {"line": 53, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 32}, "end": {"line": 19, "column": 33}}, "loc": {"start": {"line": 19, "column": 44}, "end": {"line": 31, "column": 3}}, "line": 19}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 15}, "end": {"line": 27, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 27, "column": 38}, "end": {"line": 27, "column": 49}}, {"start": {"line": 27, "column": 52}, "end": {"line": 27, "column": 79}}], "line": 27}, "1": {"loc": {"start": {"line": 33, "column": 30}, "end": {"line": 33, "column": 93}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 30}, "end": {"line": 33, "column": 46}}, {"start": {"line": 33, "column": 50}, "end": {"line": 33, "column": 93}}], "line": 33}, "2": {"loc": {"start": {"line": 33, "column": 51}, "end": {"line": 33, "column": 88}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 51}, "end": {"line": 33, "column": 83}}, {"start": {"line": 33, "column": 87}, "end": {"line": 33, "column": 88}}], "line": 33}, "3": {"loc": {"start": {"line": 41, "column": 20}, "end": {"line": 41, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 20}, "end": {"line": 41, "column": 40}}, {"start": {"line": 41, "column": 44}, "end": {"line": 41, "column": 54}}], "line": 41}, "4": {"loc": {"start": {"line": 43, "column": 11}, "end": {"line": 43, "column": 68}}, "type": "cond-expr", "locations": [{"start": {"line": 43, "column": 24}, "end": {"line": 43, "column": 46}}, {"start": {"line": 43, "column": 49}, "end": {"line": 43, "column": 68}}], "line": 43}, "5": {"loc": {"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 20}}, {"start": {"line": 45, "column": 24}, "end": {"line": 45, "column": 69}}], "line": 45}, "6": {"loc": {"start": {"line": 46, "column": 9}, "end": {"line": 46, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 9}, "end": {"line": 46, "column": 14}}, {"start": {"line": 46, "column": 18}, "end": {"line": 46, "column": 57}}], "line": 46}, "7": {"loc": {"start": {"line": 48, "column": 7}, "end": {"line": 50, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 7}, "end": {"line": 48, "column": 27}}, {"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 72}}], "line": 48}}, "s": {"0": 3, "1": 3, "2": 3, "3": 3, "4": 3, "5": 3, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 0, "13": 1, "14": 3, "15": 3}, "f": {"0": 3, "1": 1}, "b": {"0": [0, 0], "1": [3, 3], "2": [3, 0], "3": [3, 3], "4": [1, 2], "5": [3, 0], "6": [3, 0], "7": [3, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e6b3dca7575b071dbcf6730a31d42732d339bf56"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/StreamerCard.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/StreamerCard.tsx", "statementMap": {"0": {"start": {"line": 15, "column": 42}, "end": {"line": 15, "column": 86}}, "1": {"start": {"line": 16, "column": 28}, "end": {"line": 16, "column": 57}}, "2": {"start": {"line": 17, "column": 20}, "end": {"line": 17, "column": 28}}, "3": {"start": {"line": 19, "column": 33}, "end": {"line": 29, "column": 3}}, "4": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 19}}, "5": {"start": {"line": 21, "column": 4}, "end": {"line": 28, "column": 5}}, "6": {"start": {"line": 22, "column": 47}, "end": {"line": 22, "column": 71}}, "7": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 60}}, "8": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 28}}, "9": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 66}}, "10": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 25}}, "11": {"start": {"line": 31, "column": 2}, "end": {"line": 70, "column": 4}}}, "fnMap": {"0": {"name": "StreamerCard", "decl": {"start": {"line": 14, "column": 16}, "end": {"line": 14, "column": 28}}, "loc": {"start": {"line": 14, "column": 74}, "end": {"line": 71, "column": 1}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 33}, "end": {"line": 19, "column": 34}}, "loc": {"start": {"line": 19, "column": 45}, "end": {"line": 29, "column": 3}}, "line": 19}}, "branchMap": {"0": {"loc": {"start": {"line": 35, "column": 15}, "end": {"line": 35, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 15}, "end": {"line": 35, "column": 41}}, {"start": {"line": 35, "column": 45}, "end": {"line": 35, "column": 47}}], "line": 35}, "1": {"loc": {"start": {"line": 36, "column": 15}, "end": {"line": 36, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 15}, "end": {"line": 36, "column": 36}}, {"start": {"line": 36, "column": 40}, "end": {"line": 36, "column": 57}}], "line": 36}, "2": {"loc": {"start": {"line": 53, "column": 34}, "end": {"line": 53, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 34}, "end": {"line": 53, "column": 55}}, {"start": {"line": 53, "column": 59}, "end": {"line": 53, "column": 64}}], "line": 53}, "3": {"loc": {"start": {"line": 63, "column": 21}, "end": {"line": 63, "column": 55}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 36}, "end": {"line": 63, "column": 50}}, {"start": {"line": 63, "column": 53}, "end": {"line": 63, "column": 55}}], "line": 63}, "4": {"loc": {"start": {"line": 65, "column": 11}, "end": {"line": 65, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 26}, "end": {"line": 65, "column": 38}}, {"start": {"line": 65, "column": 41}, "end": {"line": 65, "column": 61}}], "line": 65}, "5": {"loc": {"start": {"line": 67, "column": 9}, "end": {"line": 67, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 9}, "end": {"line": 67, "column": 14}}, {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 70}}], "line": 67}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 2}, "f": {"0": 2, "1": 0}, "b": {"0": [2, 2], "1": [2, 0], "2": [2, 0], "3": [0, 2], "4": [0, 2], "5": [2, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "2fa70d206634f8f185c0f12db1cc5ccdee1a2417"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/StreamerList.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/StreamerList.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 35}, "end": {"line": 15, "column": 3}}, "1": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 70}}, "2": {"start": {"line": 14, "column": 33}, "end": {"line": 14, "column": 68}}, "3": {"start": {"line": 17, "column": 2}, "end": {"line": 27, "column": 4}}, "4": {"start": {"line": 20, "column": 27}, "end": {"line": 20, "column": 76}}, "5": {"start": {"line": 21, "column": 8}, "end": {"line": 23, "column": 9}}, "6": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 22}}, "7": {"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 94}}}, "fnMap": {"0": {"name": "StreamerList", "decl": {"start": {"line": 12, "column": 16}, "end": {"line": 12, "column": 28}}, "loc": {"start": {"line": 12, "column": 76}, "end": {"line": 28, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 35}, "end": {"line": 13, "column": 36}}, "loc": {"start": {"line": 13, "column": 59}, "end": {"line": 15, "column": 3}}, "line": 13}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 29}}, "loc": {"start": {"line": 14, "column": 33}, "end": {"line": 14, "column": 68}}, "line": 14}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 19, "column": 21}, "end": {"line": 19, "column": 22}}, "loc": {"start": {"line": 19, "column": 33}, "end": {"line": 25, "column": 7}}, "line": 19}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 8}, "end": {"line": 23, "column": 9}}, "type": "if", "locations": [{"start": {"line": 21, "column": 8}, "end": {"line": 23, "column": 9}}, {"start": {}, "end": {}}], "line": 21}}, "s": {"0": 1, "1": 2, "2": 3, "3": 1, "4": 2, "5": 2, "6": 0, "7": 2}, "f": {"0": 1, "1": 2, "2": 3, "3": 2}, "b": {"0": [0, 2]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a966a917e8d0f10e591fbc1182a0c30f2b76cbdc"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/UserStatus.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/dashboard/UserStatus.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 30}, "end": {"line": 9, "column": 67}}, "1": {"start": {"line": 10, "column": 36}, "end": {"line": 10, "column": 50}}, "2": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 57}}, "3": {"start": {"line": 13, "column": 2}, "end": {"line": 27, "column": 9}}, "4": {"start": {"line": 14, "column": 24}, "end": {"line": 24, "column": 5}}, "5": {"start": {"line": 15, "column": 6}, "end": {"line": 23, "column": 7}}, "6": {"start": {"line": 16, "column": 8}, "end": {"line": 16, "column": 27}}, "7": {"start": {"line": 17, "column": 27}, "end": {"line": 17, "column": 58}}, "8": {"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 30}}, "9": {"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 85}}, "10": {"start": {"line": 22, "column": 8}, "end": {"line": 22, "column": 28}}, "11": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 18}}, "12": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, "13": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 30}}, "14": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, "15": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 62}}, "16": {"start": {"line": 37, "column": 2}, "end": {"line": 42, "column": 4}}}, "fnMap": {"0": {"name": "UserStatus", "decl": {"start": {"line": 8, "column": 16}, "end": {"line": 8, "column": 26}}, "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 43, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 13}}, "loc": {"start": {"line": 13, "column": 18}, "end": {"line": 27, "column": 3}}, "line": 13}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 14, "column": 24}, "end": {"line": 14, "column": 25}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 24, "column": 5}}, "line": 14}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 83}}, "type": "cond-expr", "locations": [{"start": {"line": 20, "column": 40}, "end": {"line": 20, "column": 51}}, {"start": {"line": 20, "column": 54}, "end": {"line": 20, "column": 83}}], "line": 20}, "1": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, "type": "if", "locations": [{"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, {"start": {}, "end": {}}], "line": 29}, "2": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, "type": "if", "locations": [{"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, {"start": {}, "end": {}}], "line": 33}, "3": {"loc": {"start": {"line": 40, "column": 36}, "end": {"line": 40, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 36}, "end": {"line": 40, "column": 68}}, {"start": {"line": 40, "column": 72}, "end": {"line": 40, "column": 77}}], "line": 40}}, "s": {"0": 4, "1": 4, "2": 4, "3": 4, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 0, "10": 1, "11": 1, "12": 4, "13": 3, "14": 1, "15": 0, "16": 1}, "f": {"0": 4, "1": 1, "2": 1}, "b": {"0": [0, 0], "1": [3, 1], "2": [0, 1], "3": [1, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "68443253dbd4a92a8782374bfbe2063da50b4ca9"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Footer.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Footer.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 20}, "end": {"line": 20, "column": 1}}, "1": {"start": {"line": 22, "column": 20}, "end": {"line": 26, "column": 1}}, "2": {"start": {"line": 28, "column": 25}, "end": {"line": 85, "column": 1}}, "3": {"start": {"line": 29, "column": 2}, "end": {"line": 84, "column": 4}}, "4": {"start": {"line": 37, "column": 16}, "end": {"line": 41, "column": 21}}, "5": {"start": {"line": 49, "column": 16}, "end": {"line": 53, "column": 21}}, "6": {"start": {"line": 61, "column": 16}, "end": {"line": 65, "column": 21}}, "7": {"start": {"line": 76, "column": 14}, "end": {"line": 78, "column": 18}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 28, "column": 25}, "end": {"line": 28, "column": 26}}, "loc": {"start": {"line": 28, "column": 31}, "end": {"line": 85, "column": 1}}, "line": 28}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 39}, "end": {"line": 36, "column": 40}}, "loc": {"start": {"line": 37, "column": 16}, "end": {"line": 41, "column": 21}}, "line": 37}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 48, "column": 39}, "end": {"line": 48, "column": 40}}, "loc": {"start": {"line": 49, "column": 16}, "end": {"line": 53, "column": 21}}, "line": 49}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 60, "column": 37}, "end": {"line": 60, "column": 38}}, "loc": {"start": {"line": 61, "column": 16}, "end": {"line": 65, "column": 21}}, "line": 61}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 75, "column": 29}, "end": {"line": 75, "column": 30}}, "loc": {"start": {"line": 76, "column": 14}, "end": {"line": 78, "column": 18}}, "line": 76}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Grid.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Grid.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 51}, "end": {"line": 15, "column": 1}}, "1": {"start": {"line": 10, "column": 2}, "end": {"line": 14, "column": 4}}, "2": {"start": {"line": 22, "column": 41}, "end": {"line": 28, "column": 1}}, "3": {"start": {"line": 23, "column": 2}, "end": {"line": 27, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 51}, "end": {"line": 9, "column": 52}}, "loc": {"start": {"line": 9, "column": 80}, "end": {"line": 15, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 41}, "end": {"line": 22, "column": 42}}, "loc": {"start": {"line": 22, "column": 70}, "end": {"line": 28, "column": 1}}, "line": 22}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Header.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Header.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 25}, "end": {"line": 59, "column": 1}}, "1": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 39}}, "2": {"start": {"line": 14, "column": 2}, "end": {"line": 58, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 26}}, "loc": {"start": {"line": 11, "column": 31}, "end": {"line": 59, "column": 1}}, "line": 11}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Layout.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Layout.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 63}, "end": {"line": 23, "column": 1}}, "1": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 39}}, "2": {"start": {"line": 11, "column": 2}, "end": {"line": 22, "column": 4}}, "3": {"start": {"line": 25, "column": 56}, "end": {"line": 31, "column": 1}}, "4": {"start": {"line": 26, "column": 2}, "end": {"line": 30, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 63}, "end": {"line": 8, "column": 64}}, "loc": {"start": {"line": 8, "column": 81}, "end": {"line": 23, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 25, "column": 56}, "end": {"line": 25, "column": 57}}, "loc": {"start": {"line": 25, "column": 74}, "end": {"line": 31, "column": 1}}, "line": 25}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 67}, "end": {"line": 16, "column": 99}}, "type": "cond-expr", "locations": [{"start": {"line": 16, "column": 83}, "end": {"line": 16, "column": 90}}, {"start": {"line": 16, "column": 93}, "end": {"line": 16, "column": 99}}], "line": 16}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Sidebar.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/layout/Sidebar.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 17}, "end": {"line": 13, "column": 1}}, "1": {"start": {"line": 15, "column": 26}, "end": {"line": 45, "column": 1}}, "2": {"start": {"line": 16, "column": 28}, "end": {"line": 16, "column": 39}}, "3": {"start": {"line": 18, "column": 2}, "end": {"line": 44, "column": 4}}, "4": {"start": {"line": 32, "column": 12}, "end": {"line": 39, "column": 17}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 26}, "end": {"line": 15, "column": 27}}, "loc": {"start": {"line": 15, "column": 32}, "end": {"line": 45, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 31, "column": 24}, "end": {"line": 31, "column": 25}}, "loc": {"start": {"line": 32, "column": 12}, "end": {"line": 39, "column": 17}}, "line": 32}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Button.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Button.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 23}, "end": {"line": 37, "column": 1}}, "1": {"start": {"line": 46, "column": 15}, "end": {"line": 71, "column": 1}}, "2": {"start": {"line": 59, "column": 17}, "end": {"line": 59, "column": 42}}, "3": {"start": {"line": 60, "column": 4}, "end": {"line": 69, "column": 6}}, "4": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 3}}, "loc": {"start": {"line": 58, "column": 7}, "end": {"line": 70, "column": 3}}, "line": 58}}, "branchMap": {"0": {"loc": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 21}}], "line": 52}, "1": {"loc": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 53, "column": 16}, "end": {"line": 53, "column": 21}}], "line": 53}, "2": {"loc": {"start": {"line": 59, "column": 17}, "end": {"line": 59, "column": 42}}, "type": "cond-expr", "locations": [{"start": {"line": 59, "column": 27}, "end": {"line": 59, "column": 31}}, {"start": {"line": 59, "column": 34}, "end": {"line": 59, "column": 42}}], "line": 59}, "3": {"loc": {"start": {"line": 67, "column": 9}, "end": {"line": 67, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 67, "column": 19}, "end": {"line": 67, "column": 47}}, {"start": {"line": 67, "column": 50}, "end": {"line": 67, "column": 58}}], "line": 67}}, "s": {"0": 2, "1": 2, "2": 11, "3": 11, "4": 2}, "f": {"0": 11}, "b": {"0": [11], "1": [9], "2": [0, 11], "3": [2, 9]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "1e1044e248dd4348e34ea60a4d92fc17936c3b0a"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Card.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Card.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 13}, "end": {"line": 17, "column": 2}}, "1": {"start": {"line": 9, "column": 2}, "end": {"line": 16, "column": 4}}, "2": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 26}}, "3": {"start": {"line": 20, "column": 19}, "end": {"line": 29, "column": 2}}, "4": {"start": {"line": 24, "column": 2}, "end": {"line": 28, "column": 4}}, "5": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 38}}, "6": {"start": {"line": 32, "column": 18}, "end": {"line": 44, "column": 2}}, "7": {"start": {"line": 36, "column": 2}, "end": {"line": 43, "column": 4}}, "8": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 36}}, "9": {"start": {"line": 47, "column": 24}, "end": {"line": 56, "column": 2}}, "10": {"start": {"line": 51, "column": 2}, "end": {"line": 55, "column": 4}}, "11": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 48}}, "12": {"start": {"line": 59, "column": 20}, "end": {"line": 64, "column": 2}}, "13": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 68}}, "14": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 40}}, "15": {"start": {"line": 67, "column": 19}, "end": {"line": 76, "column": 2}}, "16": {"start": {"line": 71, "column": 2}, "end": {"line": 75, "column": 4}}, "17": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 38}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 3}}, "loc": {"start": {"line": 9, "column": 2}, "end": {"line": 16, "column": 4}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 3}}, "loc": {"start": {"line": 24, "column": 2}, "end": {"line": 28, "column": 4}}, "line": 24}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 3}}, "loc": {"start": {"line": 36, "column": 2}, "end": {"line": 43, "column": 4}}, "line": 36}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 3}}, "loc": {"start": {"line": 51, "column": 2}, "end": {"line": 55, "column": 4}}, "line": 51}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 3}}, "loc": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 68}}, "line": 63}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 3}}, "loc": {"start": {"line": 71, "column": 2}, "end": {"line": 75, "column": 4}}, "line": 71}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 1, "4": 2, "5": 1, "6": 1, "7": 2, "8": 1, "9": 1, "10": 2, "11": 1, "12": 1, "13": 2, "14": 1, "15": 1, "16": 2, "17": 1}, "f": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "86a195aeac7c4eb4b78f411cdefaad33e32dbf02"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/EmptyState.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/EmptyState.tsx", "statementMap": {"0": {"start": {"line": 20, "column": 2}, "end": {"line": 29, "column": 4}}}, "fnMap": {"0": {"name": "EmptyState", "decl": {"start": {"line": 14, "column": 16}, "end": {"line": 14, "column": 26}}, "loc": {"start": {"line": 19, "column": 20}, "end": {"line": 30, "column": 1}}, "line": 19}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 7}, "end": {"line": 22, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 22, "column": 7}, "end": {"line": 22, "column": 11}}, {"start": {"line": 22, "column": 15}, "end": {"line": 22, "column": 75}}], "line": 22}, "1": {"loc": {"start": {"line": 25, "column": 7}, "end": {"line": 27, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 7}, "end": {"line": 25, "column": 13}}, {"start": {"line": 26, "column": 8}, "end": {"line": 26, "column": 64}}], "line": 25}}, "s": {"0": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/ErrorState.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/ErrorState.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 2}, "end": {"line": 17, "column": 4}}}, "fnMap": {"0": {"name": "ErrorState", "decl": {"start": {"line": 10, "column": 16}, "end": {"line": 10, "column": 26}}, "loc": {"start": {"line": 10, "column": 73}, "end": {"line": 18, "column": 1}}, "line": 10}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 7}, "end": {"line": 15, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 7}, "end": {"line": 15, "column": 14}}, {"start": {"line": 15, "column": 18}, "end": {"line": 15, "column": 80}}], "line": 15}}, "s": {"0": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Input.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Input.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 14}, "end": {"line": 22, "column": 1}}, "1": {"start": {"line": 10, "column": 4}, "end": {"line": 20, "column": 6}}, "2": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 3}}, "loc": {"start": {"line": 9, "column": 42}, "end": {"line": 21, "column": 3}}, "line": 9}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Label.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Label.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 22}, "end": {"line": 11, "column": 1}}, "1": {"start": {"line": 13, "column": 14}, "end": {"line": 23, "column": 2}}, "2": {"start": {"line": 18, "column": 2}, "end": {"line": 22, "column": 4}}, "3": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 52}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 3}}, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 22, "column": 4}}, "line": 18}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/LoadingSpinner.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/LoadingSpinner.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 31}, "end": {"line": 20, "column": 1}}, "1": {"start": {"line": 26, "column": 23}, "end": {"line": 41, "column": 1}}, "2": {"start": {"line": 28, "column": 4}, "end": {"line": 39, "column": 6}}, "3": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 46}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 27, "column": 42}, "end": {"line": 40, "column": 3}}, "line": 27}}, "branchMap": {}, "s": {"0": 2, "1": 2, "2": 4, "3": 2}, "f": {"0": 4}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "78bd7d1538993e4ba9dbc07aace2a3127a05c0e7"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Skeleton.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Skeleton.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 2}, "end": {"line": 12, "column": 4}}}, "fnMap": {"0": {"name": "Skeleton", "decl": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 17}}, "loc": {"start": {"line": 6, "column": 41}, "end": {"line": 13, "column": 1}}, "line": 6}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Table.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Table.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 14}, "end": {"line": 16, "column": 2}}, "1": {"start": {"line": 9, "column": 2}, "end": {"line": 15, "column": 8}}, "2": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 28}}, "3": {"start": {"line": 19, "column": 20}, "end": {"line": 24, "column": 2}}, "4": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 77}}, "5": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 40}}, "6": {"start": {"line": 27, "column": 18}, "end": {"line": 36, "column": 2}}, "7": {"start": {"line": 31, "column": 2}, "end": {"line": 35, "column": 4}}, "8": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 36}}, "9": {"start": {"line": 39, "column": 20}, "end": {"line": 51, "column": 2}}, "10": {"start": {"line": 43, "column": 2}, "end": {"line": 50, "column": 4}}, "11": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 40}}, "12": {"start": {"line": 54, "column": 17}, "end": {"line": 66, "column": 2}}, "13": {"start": {"line": 58, "column": 2}, "end": {"line": 65, "column": 4}}, "14": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 34}}, "15": {"start": {"line": 69, "column": 18}, "end": {"line": 81, "column": 2}}, "16": {"start": {"line": 73, "column": 2}, "end": {"line": 80, "column": 4}}, "17": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 36}}, "18": {"start": {"line": 84, "column": 18}, "end": {"line": 93, "column": 2}}, "19": {"start": {"line": 88, "column": 2}, "end": {"line": 92, "column": 4}}, "20": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 36}}, "21": {"start": {"line": 96, "column": 21}, "end": {"line": 105, "column": 2}}, "22": {"start": {"line": 100, "column": 2}, "end": {"line": 104, "column": 4}}, "23": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 42}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 3}}, "loc": {"start": {"line": 9, "column": 2}, "end": {"line": 15, "column": 8}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 3}}, "loc": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 77}}, "line": 23}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 3}}, "loc": {"start": {"line": 31, "column": 2}, "end": {"line": 35, "column": 4}}, "line": 31}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 3}}, "loc": {"start": {"line": 43, "column": 2}, "end": {"line": 50, "column": 4}}, "line": 43}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 3}}, "loc": {"start": {"line": 58, "column": 2}, "end": {"line": 65, "column": 4}}, "line": 58}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 3}}, "loc": {"start": {"line": 73, "column": 2}, "end": {"line": 80, "column": 4}}, "line": 73}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 3}}, "loc": {"start": {"line": 88, "column": 2}, "end": {"line": 92, "column": 4}}, "line": 88}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 3}}, "loc": {"start": {"line": 100, "column": 2}, "end": {"line": 104, "column": 4}}, "line": 100}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Toast.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/components/ui/Toast.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 16}, "end": {"line": 27, "column": 1}}, "1": {"start": {"line": 9, "column": 2}, "end": {"line": 26, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 16}, "end": {"line": 8, "column": 17}}, "loc": {"start": {"line": 8, "column": 48}, "end": {"line": 27, "column": 1}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/context/LayoutContext.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/context/LayoutContext.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 78}}, "1": {"start": {"line": 12, "column": 65}, "end": {"line": 24, "column": 1}}, "2": {"start": {"line": 13, "column": 42}, "end": {"line": 13, "column": 57}}, "3": {"start": {"line": 15, "column": 24}, "end": {"line": 17, "column": 3}}, "4": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 34}}, "5": {"start": {"line": 16, "column": 27}, "end": {"line": 16, "column": 32}}, "6": {"start": {"line": 19, "column": 2}, "end": {"line": 23, "column": 4}}, "7": {"start": {"line": 26, "column": 25}, "end": {"line": 32, "column": 1}}, "8": {"start": {"line": 27, "column": 18}, "end": {"line": 27, "column": 43}}, "9": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, "10": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 70}}, "11": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 17}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 65}, "end": {"line": 12, "column": 66}}, "loc": {"start": {"line": 12, "column": 83}, "end": {"line": 24, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": 25}}, "loc": {"start": {"line": 15, "column": 30}, "end": {"line": 17, "column": 3}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 19}, "end": {"line": 16, "column": 20}}, "loc": {"start": {"line": 16, "column": 27}, "end": {"line": 16, "column": 32}}, "line": 16}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 26, "column": 25}, "end": {"line": 26, "column": 26}}, "loc": {"start": {"line": 26, "column": 31}, "end": {"line": 32, "column": 1}}, "line": 26}}, "branchMap": {"0": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, "type": "if", "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, {"start": {}, "end": {}}], "line": 28}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0]}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useApi.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useApi.tsx", "statementMap": {"0": {"start": {"line": 15, "column": 32}, "end": {"line": 15, "column": 47}}, "1": {"start": {"line": 16, "column": 28}, "end": {"line": 16, "column": 57}}, "2": {"start": {"line": 18, "column": 22}, "end": {"line": 44, "column": 8}}, "3": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 21}}, "4": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 19}}, "5": {"start": {"line": 22, "column": 4}, "end": {"line": 43, "column": 5}}, "6": {"start": {"line": 23, "column": 23}, "end": {"line": 29, "column": 8}}, "7": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, "8": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 66}}, "9": {"start": {"line": 35, "column": 19}, "end": {"line": 35, "column": 40}}, "10": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 18}}, "11": {"start": {"line": 38, "column": 27}, "end": {"line": 38, "column": 88}}, "12": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 29}}, "13": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 16}}, "14": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 24}}, "15": {"start": {"line": 46, "column": 14}, "end": {"line": 48, "column": 19}}, "16": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 47}}, "17": {"start": {"line": 50, "column": 15}, "end": {"line": 55, "column": 19}}, "18": {"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": 7}}, "19": {"start": {"line": 57, "column": 16}, "end": {"line": 62, "column": 19}}, "20": {"start": {"line": 58, "column": 4}, "end": {"line": 61, "column": 7}}, "21": {"start": {"line": 64, "column": 24}, "end": {"line": 66, "column": 19}}, "22": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 50}}, "23": {"start": {"line": 68, "column": 2}, "end": {"line": 75, "column": 4}}}, "fnMap": {"0": {"name": "useApi", "decl": {"start": {"line": 14, "column": 16}, "end": {"line": 14, "column": 22}}, "loc": {"start": {"line": 14, "column": 39}, "end": {"line": 76, "column": 1}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 18, "column": 34}, "end": {"line": 18, "column": 35}}, "loc": {"start": {"line": 18, "column": 84}, "end": {"line": 44, "column": 3}}, "line": 18}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 46, "column": 26}, "end": {"line": 46, "column": 27}}, "loc": {"start": {"line": 46, "column": 43}, "end": {"line": 48, "column": 3}}, "line": 46}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 50, "column": 27}, "end": {"line": 50, "column": 28}}, "loc": {"start": {"line": 50, "column": 56}, "end": {"line": 55, "column": 3}}, "line": 50}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 57, "column": 28}, "end": {"line": 57, "column": 29}}, "loc": {"start": {"line": 57, "column": 57}, "end": {"line": 62, "column": 3}}, "line": 57}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 64, "column": 36}, "end": {"line": 64, "column": 37}}, "loc": {"start": {"line": 64, "column": 53}, "end": {"line": 66, "column": 3}}, "line": 64}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 54}, "end": {"line": 18, "column": 79}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 77}, "end": {"line": 18, "column": 79}}], "line": 18}, "1": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, "type": "if", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, {"start": {}, "end": {}}], "line": 31}, "2": {"loc": {"start": {"line": 38, "column": 27}, "end": {"line": 38, "column": 88}}, "type": "cond-expr", "locations": [{"start": {"line": 38, "column": 50}, "end": {"line": 38, "column": 61}}, {"start": {"line": 38, "column": 64}, "end": {"line": 38, "column": 88}}], "line": 38}, "3": {"loc": {"start": {"line": 53, "column": 12}, "end": {"line": 53, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 53, "column": 19}, "end": {"line": 53, "column": 39}}, {"start": {"line": 53, "column": 42}, "end": {"line": 53, "column": 51}}], "line": 53}, "4": {"loc": {"start": {"line": 60, "column": 12}, "end": {"line": 60, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 60, "column": 19}, "end": {"line": 60, "column": 39}}, {"start": {"line": 60, "column": 42}, "end": {"line": 60, "column": 51}}], "line": 60}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useAuth.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useAuth.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 23}, "end": {"line": 13, "column": 1}}, "1": {"start": {"line": 6, "column": 41}, "end": {"line": 6, "column": 50}}, "2": {"start": {"line": 8, "column": 2}, "end": {"line": 12, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 24}}, "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 13, "column": 1}}, "line": 5}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useLocalStorage.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useLocalStorage.tsx", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useStreamers.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useStreamers.tsx", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useUserStatus.tsx": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/hooks/useUserStatus.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 53}, "end": {"line": 15, "column": 3}}, "1": {"start": {"line": 10, "column": 10}, "end": {"line": 10, "column": 35}}, "2": {"start": {"line": 17, "column": 2}, "end": {"line": 22, "column": 4}}}, "fnMap": {"0": {"name": "useUserStatus", "decl": {"start": {"line": 7, "column": 16}, "end": {"line": 7, "column": 29}}, "loc": {"start": {"line": 7, "column": 32}, "end": {"line": 23, "column": 1}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 5}}, "loc": {"start": {"line": 10, "column": 10}, "end": {"line": 10, "column": 35}}, "line": 10}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 11}, "end": {"line": 20, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 11}, "end": {"line": 20, "column": 25}}, {"start": {"line": 20, "column": 29}, "end": {"line": 20, "column": 33}}], "line": 20}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/api.ts": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/api.ts", "statementMap": {"0": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 91}}, "1": {"start": {"line": 9, "column": 2}, "end": {"line": 14, "column": 3}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 35}}, "3": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 54}}, "4": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 16}}, "5": {"start": {"line": 18, "column": 16}, "end": {"line": 18, "column": 36}}, "6": {"start": {"line": 19, "column": 18}, "end": {"line": 23, "column": 3}}, "7": {"start": {"line": 25, "column": 19}, "end": {"line": 28, "column": 4}}, "8": {"start": {"line": 30, "column": 2}, "end": {"line": 39, "column": 3}}, "9": {"start": {"line": 31, "column": 23}, "end": {"line": 31, "column": 43}}, "10": {"start": {"line": 32, "column": 4}, "end": {"line": 37, "column": 5}}, "11": {"start": {"line": 33, "column": 24}, "end": {"line": 33, "column": 45}}, "12": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 54}}, "13": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 71}}, "14": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 34}}, "15": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 25}}, "16": {"start": {"line": 44, "column": 25}, "end": {"line": 59, "column": 1}}, "17": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 48}}, "18": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 105}}, "19": {"start": {"line": 54, "column": 4}, "end": {"line": 57, "column": 7}}}, "fnMap": {"0": {"name": "getAuthToken", "decl": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 27}}, "loc": {"start": {"line": 8, "column": 30}, "end": {"line": 15, "column": 1}}, "line": 8}, "1": {"name": "request", "decl": {"start": {"line": 17, "column": 15}, "end": {"line": 17, "column": 22}}, "loc": {"start": {"line": 17, "column": 83}, "end": {"line": 42, "column": 1}}, "line": 17}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 17}, "end": {"line": 45, "column": 18}}, "loc": {"start": {"line": 45, "column": 44}, "end": {"line": 47, "column": 3}}, "line": 45}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 20}}, "loc": {"start": {"line": 49, "column": 86}, "end": {"line": 51, "column": 3}}, "line": 49}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 53, "column": 26}, "end": {"line": 53, "column": 27}}, "loc": {"start": {"line": 53, "column": 89}, "end": {"line": 58, "column": 3}}, "line": 53}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 91}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 57}}, {"start": {"line": 6, "column": 61}, "end": {"line": 6, "column": 91}}], "line": 6}, "1": {"loc": {"start": {"line": 17, "column": 44}, "end": {"line": 17, "column": 69}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 67}, "end": {"line": 17, "column": 69}}], "line": 17}, "2": {"loc": {"start": {"line": 22, "column": 8}, "end": {"line": 22, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 22, "column": 8}, "end": {"line": 22, "column": 13}}, {"start": {"line": 22, "column": 17}, "end": {"line": 22, "column": 53}}], "line": 22}, "3": {"loc": {"start": {"line": 30, "column": 2}, "end": {"line": 39, "column": 3}}, "type": "if", "locations": [{"start": {"line": 30, "column": 2}, "end": {"line": 39, "column": 3}}, {"start": {}, "end": {}}], "line": 30}, "4": {"loc": {"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 37}}, {"start": {"line": 34, "column": 41}, "end": {"line": 34, "column": 53}}], "line": 34}}, "s": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 1, "17": 0, "18": 0, "19": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [1, 1], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "58af00d683b7f904e0469708759915087c929152"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/auth.ts": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/auth.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/utils.ts": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/lib/utils.ts", "statementMap": {"0": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 31}}}, "fnMap": {"0": {"name": "cn", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 18}}, "loc": {"start": {"line": 4, "column": 44}, "end": {"line": 6, "column": 1}}, "line": 4}}, "branchMap": {}, "s": {"0": 27}, "f": {"0": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "39752f5935194b7fb32c410931e025574ac25bf1"}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/api.ts": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/api.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/assignment.ts": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/assignment.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/streamer.ts": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/streamer.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/user.ts": {"path": "/mnt/c/Users/<USER>/Desktop/Twitch-Scraper-main/artwork-outreach-web-app/frontend/src/types/user.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}}