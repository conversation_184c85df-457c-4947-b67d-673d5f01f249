from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.user_service import RequestLimitService
from app.database.connection import get_db
from app.database.models import User<PERSON>rofile, Streamer
from app.api.dependencies import get_current_user, get_db_session
from app.services.streamer_service import StreamerService
from app.schemas.streamer import StreamerCreate, StreamerRead
from datetime import datetime

router = APIRouter()

# For now, we'll use a simple check for a specific admin user ID.
# In a real application, this would be a more robust role-based access control system.
ADMIN_USER_ID = "user_2i3j4k5l6m7n8o9p" # Replace with a real admin user ID from your Clerk dashboard

@router.post("/reset-counts", status_code=status.HTTP_204_NO_CONTENT)
async def reset_daily_counts(
    current_user: UserProfile = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Resets the daily request counts for all users.
    This is an administrative endpoint and should be protected.
    """
    if str(current_user.id) != ADMIN_USER_ID:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to perform this action.",
        )

    service = RequestLimitService(db)
    await service.reset_daily_counts()


@router.post("/warm-cache", status_code=status.HTTP_204_NO_CONTENT)
async def warm_cache(
    current_user: UserProfile = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
):
    """
    Warms the cache with frequently accessed data.
    This is an administrative endpoint and should be protected.
    """
    if str(current_user.id) != ADMIN_USER_ID:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to perform this action.",
        )

    # Cache warming disabled for now
    return {"message": "Cache warming completed"}


@router.post("/streamers", response_model=StreamerRead, status_code=status.HTTP_201_CREATED)
async def create_or_update_streamer(
    streamer_data: StreamerCreate,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Create or update a streamer (public endpoint for data loading).
    This endpoint doesn't require authentication for bulk data loading.
    """
    try:
        # Check if streamer already exists
        from sqlalchemy import select
        stmt = select(Streamer).where(Streamer.twitch_user_id == streamer_data.twitch_user_id)
        result = await db.execute(stmt)
        existing_streamer = result.scalar_one_or_none()
        
        if existing_streamer:
            # Update existing streamer
            existing_streamer.username = streamer_data.username
            existing_streamer.display_name = streamer_data.display_name
            existing_streamer.follower_count = streamer_data.follower_count
            existing_streamer.is_live = streamer_data.is_live
            existing_streamer.current_game = streamer_data.current_game
            existing_streamer.stream_title = streamer_data.stream_title
            existing_streamer.thumbnail_url = streamer_data.thumbnail_url
            existing_streamer.language = streamer_data.language
            existing_streamer.last_seen_live_at = datetime.utcnow()
            existing_streamer.updated_at = datetime.utcnow()
            
            await db.commit()
            await db.refresh(existing_streamer)
            return existing_streamer
        else:
            # Create new streamer
            new_streamer = Streamer(
                twitch_user_id=streamer_data.twitch_user_id,
                username=streamer_data.username,
                display_name=streamer_data.display_name,
                follower_count=streamer_data.follower_count,
                is_live=streamer_data.is_live,
                current_game=streamer_data.current_game,
                stream_title=streamer_data.stream_title,
                thumbnail_url=streamer_data.thumbnail_url,
                language=streamer_data.language,
                last_seen_live_at=datetime.utcnow()
            )
            
            db.add(new_streamer)
            await db.commit()
            await db.refresh(new_streamer)
            return new_streamer
            
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating/updating streamer: {str(e)}"
        )