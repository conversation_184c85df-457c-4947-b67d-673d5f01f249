#!/bin/bash
set -e

echo "Building for Render deployment..."

# Install Python dependencies
pip install --upgrade pip
pip install -r requirements.txt

# Run any build-time setup
echo "Running build-time database setup..."
python -c "
import os
if os.getenv('ENVIRONMENT') == 'production':
    print('Production build - skipping test database setup')
else:
    print('Development build')
"

echo "Build completed successfully!"