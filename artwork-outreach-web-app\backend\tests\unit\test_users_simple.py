import pytest
import uuid
from unittest.mock import AsyncMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.models import UserProfile
from app.services.user_service import UserService


def create_mock_user(email_suffix: str = ""):
    """Create a mock user with unique email."""
    unique_id = str(uuid.uuid4())[:8]
    email = f"test{email_suffix}_{unique_id}@example.com"
    return UserProfile(
        id=uuid.uuid4(),
        full_name="Test User",
        email=email,
        role="agent",
        daily_request_count=5,
    )


@pytest.mark.asyncio
async def test_user_service_get_user_profile():
    """Test UserService.get_user_profile method directly."""
    mock_user = create_mock_user("_service_test")
    
    # Mock the database session
    mock_db = AsyncMock(spec=AsyncSession)
    mock_db.get = AsyncMock(return_value=mock_user)
    
    # Create UserService instance
    user_service = UserService(mock_db)
    
    # Test the service method
    result = await user_service.get_user_profile(mock_user.id)
    
    # Assertions
    assert result is not None
    assert result.id == mock_user.id
    assert result.email == mock_user.email
    assert result.full_name == mock_user.full_name
    mock_db.get.assert_called_once_with(UserProfile, mock_user.id)


@pytest.mark.asyncio
async def test_user_service_update_user_profile():
    """Test UserService.update_user_profile method directly."""
    mock_user = create_mock_user("_update_service_test")
    updated_user = create_mock_user("_updated_service_test")
    updated_user.id = mock_user.id
    updated_user.full_name = "Updated Name"
    
    # Mock the database session
    mock_db = AsyncMock(spec=AsyncSession)
    mock_db.get = AsyncMock(return_value=mock_user)
    mock_db.commit = AsyncMock()
    mock_db.refresh = AsyncMock()
    
    # Create UserService instance
    user_service = UserService(mock_db)
    
    # Test the service method
    with patch.object(user_service, 'get_user_profile', return_value=updated_user):
        result = await user_service.update_user_profile(
            mock_user.id, 
            {"full_name": "Updated Name"}
        )
    
    # Assertions
    assert result is not None
    assert result.full_name == "Updated Name"
    mock_db.get.assert_called_once_with(UserProfile, mock_user.id)


@pytest.mark.asyncio 
async def test_user_service_get_user_status():
    """Test UserService.get_user_status method directly."""
    mock_user = create_mock_user("_status_service_test")
    mock_user.daily_request_count = 3
    
    # Mock the database session
    mock_db = AsyncMock(spec=AsyncSession)
    mock_db.get = AsyncMock(return_value=mock_user)
    
    # Create UserService instance
    user_service = UserService(mock_db)
    
    # Test the service method
    result = await user_service.get_user_status(mock_user.id)
    
    # Assertions
    assert result is not None
    assert "daily_request_count" in result
    assert "remaining_requests" in result
    assert "next_reset_time" in result
    assert result["daily_request_count"] == 3
    mock_db.get.assert_called_once_with(UserProfile, mock_user.id)


def test_user_model_creation():
    """Test UserProfile model creation and validation."""
    user = create_mock_user("_model_test")
    
    # Test basic properties
    assert user.full_name == "Test User"
    assert user.role == "agent"
    assert user.daily_request_count == 5
    assert "@example.com" in user.email
    assert isinstance(user.id, uuid.UUID)


def test_user_model_email_uniqueness():
    """Test that different users get unique emails."""
    user1 = create_mock_user("_unique1")
    user2 = create_mock_user("_unique2")
    
    # Emails should be different
    assert user1.email != user2.email
    assert user1.id != user2.id