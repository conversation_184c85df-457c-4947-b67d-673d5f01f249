# Database
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
DATABASE_URL=postgresql+asyncpg://user:password@localhost/artwork_outreach
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_RECYCLE=1800

# Authentication
CLERK_SECRET_KEY=your_clerk_secret_key

# Twitch API
TWITCH_CLIENT_ID=your_twitch_client_id
TWITCH_CLIENT_SECRET=your_twitch_client_secret

# Monitoring
SENTRY_DSN=your_sentry_dsn

# App Configuration
ENVIRONMENT=development
LOG_LEVEL=INFO
CORS_ORIGINS='["http://localhost:3000"]'

# Redis
REDIS_URL=redis://localhost:6379/0