import pytest
from fastapi.testclient import TestClient

def test_health_check(client: TestClient):
    """
    Test the basic health check endpoint.
    """
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok", "message": "Service is running"}

def test_detailed_health_check(client: TestClient):
    """
    Test the detailed health check endpoint.
    """
    response = client.get("/api/v1/health/detailed")
    assert response.status_code == 200
    json_response = response.json()
    assert json_response["status"] == "ok"
    assert json_response["details"]["database_status"] == "connected"
    assert "version" in json_response["details"]