import pytest
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4, UUI<PERSON>

from sqlalchemy.ext.asyncio import AsyncSession

from app.services import assignment_service
from app.database.models import Assignment, Streamer
from app.schemas.assignment import UpdateAssignmentRequest, AssignmentStatus

@pytest.fixture
def mock_db_session():
    """Fixture for a mocked SQLAlchemy AsyncSession."""
    session = AsyncMock(spec=AsyncSession)
    session.execute = AsyncMock()
    return session

@pytest.mark.asyncio
async def test_create_assignment(mock_db_session: AsyncSession):
    """Test creating a new assignment."""
    streamer_id = 123
    agent_id = uuid4()

    # The function returns the created object, so we don't need to mock execute
    # Instead, we check that add, commit, and refresh are called.
    
    created_assignment = await assignment_service.create_assignment(mock_db_session, streamer_id, agent_id)

    mock_db_session.add.assert_called_once()
    mock_db_session.commit.assert_called_once()
    mock_db_session.refresh.assert_called_once()
    
    assert created_assignment.streamer_id == streamer_id
    assert created_assignment.agent_id == agent_id
    assert created_assignment.status == AssignmentStatus.PENDING

@pytest.mark.asyncio
async def test_get_assignments(mock_db_session: AsyncSession):
    """Test fetching assignments."""
    mock_assignments = [Assignment(id=uuid4(), streamer_id=i) for i in range(5)]
    mock_db_session.execute.return_value.scalars.return_value.all.return_value = mock_assignments

    assignments = await assignment_service.get_assignments(mock_db_session)

    assert len(assignments) == 5
    mock_db_session.execute.assert_called_once()

@pytest.mark.asyncio
async def test_update_assignment(mock_db_session: AsyncSession):
    """Test updating an assignment."""
    assignment_id = uuid4()
    update_data = UpdateAssignmentRequest(status=AssignmentStatus.SUCCESS)
    
    mock_assignment = Assignment(id=assignment_id, status=AssignmentStatus.PENDING)
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_assignment

    updated_assignment = await assignment_service.update_assignment(mock_db_session, assignment_id, update_data)

    assert updated_assignment is not None
    assert updated_assignment.status == AssignmentStatus.SUCCESS
    mock_db_session.commit.assert_called_once()
    mock_db_session.refresh.assert_called_once()

@pytest.mark.asyncio
async def test_update_assignment_not_found(mock_db_session: AsyncSession):
    """Test updating a non-existent assignment."""
    assignment_id = uuid4()
    update_data = UpdateAssignmentRequest(status=AssignmentStatus.SUCCESS)
    
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

    updated_assignment = await assignment_service.update_assignment(mock_db_session, assignment_id, update_data)

    assert updated_assignment is None
    mock_db_session.commit.assert_not_called()

@pytest.mark.asyncio
async def test_delete_assignment(mock_db_session: AsyncSession):
    """Test deleting an assignment."""
    assignment_id = uuid4()
    mock_assignment = Assignment(id=assignment_id)
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_assignment

    deleted_assignment = await assignment_service.delete_assignment(mock_db_session, assignment_id)

    assert deleted_assignment is not None
    mock_db_session.delete.assert_called_once_with(mock_assignment)
    mock_db_session.commit.assert_called_once()

@pytest.mark.asyncio
async def test_delete_assignment_not_found(mock_db_session: AsyncSession):
    """Test deleting a non-existent assignment."""
    assignment_id = uuid4()
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

    deleted_assignment = await assignment_service.delete_assignment(mock_db_session, assignment_id)

    assert deleted_assignment is None
    mock_db_session.delete.assert_not_called()