#!/usr/bin/env python3
"""
Load fresh CSV data from scraper to Supabase using REST API
"""

import pandas as pd
import requests
import json
from datetime import datetime
from app.config import get_settings

def load_csv_to_supabase():
    """Load CSV data directly to Supabase via REST API"""
    
    settings = get_settings()
    
    # Supabase configuration
    base_url = settings.SUPABASE_URL
    headers = {
        "apikey": settings.SUPABASE_SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {settings.SUPABASE_SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    print("🚀 Loading Fresh CSV Data to Supabase")
    print("=" * 50)
    
    # Read CSV file
    try:
        df = pd.read_csv('twitch_streams_filtered.csv')
        print(f"📊 Found {len(df)} streamers in CSV")
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return False
    
    success_count = 0
    error_count = 0
    
    for index, row in df.iterrows():
        try:
            # Prepare streamer data
            streamer_data = {
                "twitch_user_id": row['username'],  # Using username as ID
                "username": row['username'],
                "display_name": row['display_name'],
                "follower_count": int(row['follower_count']),
                "is_live": True,  # From fresh scraper data
                "current_game": row['game_name'],
                "stream_title": row['title'],
                "language": row['language'],
                "thumbnail_url": row['thumbnail_url'],
                "last_seen": datetime.utcnow().isoformat(),
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            # Try to insert/update streamer
            response = requests.post(
                f"{base_url}/rest/v1/streamers",
                headers=headers,
                json=streamer_data,
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                print(f"✅ {index+1:2d}/{len(df)} - {row['username']} (added)")
                success_count += 1
            else:
                # Try update if insert failed (might already exist)
                update_response = requests.patch(
                    f"{base_url}/rest/v1/streamers?twitch_user_id=eq.{row['username']}",
                    headers=headers,
                    json=streamer_data,
                    timeout=10
                )
                
                if update_response.status_code in [200, 204]:
                    print(f"🔄 {index+1:2d}/{len(df)} - {row['username']} (updated)")
                    success_count += 1
                else:
                    print(f"❌ {index+1:2d}/{len(df)} - {row['username']} - Error: {response.status_code}")
                    error_count += 1
                    
        except Exception as e:
            print(f"❌ {index+1:2d}/{len(df)} - {row['username']} - Exception: {e}")
            error_count += 1
    
    print("\n📈 Upload Summary:")
    print(f"   ✅ Successful: {success_count}")
    print(f"   ❌ Errors: {error_count}")
    print(f"   📊 Total: {len(df)}")
    
    if success_count > 0:
        print("\n🎉 Fresh data loaded successfully!")
        return True
    else:
        print("\n❌ No data was loaded!")
        return False

if __name__ == "__main__":
    load_csv_to_supabase()
