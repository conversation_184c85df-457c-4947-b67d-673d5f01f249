const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(gif|ttf|eot|svg|png)$': '<rootDir>/tests/__mocks__/fileMock.js',
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@clerk/nextjs$': '<rootDir>/tests/__mocks__/@clerk/nextjs.ts',
    'msw/node': 'msw/node',
  },
  testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/.next/'],
  transform: {
    '^.+\\.(ts|tsx)$': '@swc/jest',
  },
  transformIgnorePatterns: [
    '/node_modules/',
    '^.+\\.module\\.(css|sass|scss)$',
  ],
  collectCoverage: true,
  coverageReporters: ['text', 'lcov', 'json-summary'],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!<rootDir>/out/**',
    '!<rootDir>/.next/**',
    '!<rootDir>/*.config.js',
    '!<rootDir>/coverage/**',
  ],
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50,
    },
  },
   coveragePathIgnorePatterns: [
    '/node_modules/',
    '<rootDir>/jest.config.js',
    '<rootDir>/jest.setup.js',
    '<rootDir>/next.config.js',
    '<rootDir>/.eslintrc.json',
    '<rootDir>/postcss.config.js',
    '<rootDir>/src/app/api/',
    '<rootDir>/src/lib/test-utils.tsx',
    '<rootDir>/src/app/auth/',
    '<rootDir>/src/types/',
    '<rootDir>/src/styles/',
    '<rootDir>/src/app/layout.tsx',
    '<rootDir>/src/app/page.tsx',
    '<rootDir>/src/app/not-found.tsx',
    '<rootDir>/src/components/SWRProvider.tsx',
  ],
};

module.exports = createJestConfig(customJestConfig);