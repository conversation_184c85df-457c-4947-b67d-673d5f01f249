{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/css-loader/src/camelcase.ts"], "names": ["preserveCamelCase", "string", "locale", "isLastCharLower", "isLastCharUpper", "isLastLastCharUpper", "i", "length", "character", "test", "slice", "toLocaleLowerCase", "toLocaleUpperCase", "preserveConsecutiveUppercase", "input", "replace", "m1", "toLowerCase", "postProcess", "options", "_", "p1", "m", "camelCase", "Array", "isArray", "TypeError", "pascalCase", "map", "x", "trim", "filter", "join", "hasUpperCase", "char<PERSON>t"], "mappings": "AAAA;;;;;;;;;;AAUA;;;;+BAwGA;;;eAAA;;;AAtGA,MAAMA,oBAAoB,CAACC,QAAgBC;IACzC,IAAIC,kBAAkB;IACtB,IAAIC,kBAAkB;IACtB,IAAIC,sBAAsB;IAE1B,IAAK,IAAIC,IAAI,GAAGA,IAAIL,OAAOM,MAAM,EAAED,IAAK;QACtC,MAAME,YAAYP,MAAM,CAACK,EAAE;QAE3B,IAAIH,mBAAmB,YAAYM,IAAI,CAACD,YAAY;YAClDP,SAASA,OAAOS,KAAK,CAAC,GAAGJ,KAAK,MAAML,OAAOS,KAAK,CAACJ;YACjDH,kBAAkB;YAClBE,sBAAsBD;YACtBA,kBAAkB;YAClBE;QACF,OAAO,IACLF,mBACAC,uBACA,YAAYI,IAAI,CAACD,YACjB;YACAP,SAASA,OAAOS,KAAK,CAAC,GAAGJ,IAAI,KAAK,MAAML,OAAOS,KAAK,CAACJ,IAAI;YACzDD,sBAAsBD;YACtBA,kBAAkB;YAClBD,kBAAkB;QACpB,OAAO;YACLA,kBACEK,UAAUG,iBAAiB,CAACT,YAAYM,aACxCA,UAAUI,iBAAiB,CAACV,YAAYM;YAC1CH,sBAAsBD;YACtBA,kBACEI,UAAUI,iBAAiB,CAACV,YAAYM,aACxCA,UAAUG,iBAAiB,CAACT,YAAYM;QAC5C;IACF;IAEA,OAAOP;AACT;AAEA,MAAMY,+BAA+B,CAACC;IACpC,OAAOA,MAAMC,OAAO,CAAC,2BAA2B,CAACC,KAAOA,GAAGC,WAAW;AACxE;AAEA,MAAMC,cAAc,CAACJ,OAAeK;IAClC,OAAOL,MACJC,OAAO,CAAC,mCAAmC,CAACK,GAAGC,KAC9CA,GAAGT,iBAAiB,CAACO,QAAQjB,MAAM,GAEpCa,OAAO,CAAC,8BAA8B,CAACO,IACtCA,EAAEV,iBAAiB,CAACO,QAAQjB,MAAM;AAExC;AAEA,MAAMqB,YAAY,CAACT,OAA0BK;IAC3C,IAAI,CAAE,CAAA,OAAOL,UAAU,YAAYU,MAAMC,OAAO,CAACX,MAAK,GAAI;QACxD,MAAM,IAAIY,UAAU;IACtB;IAEAP,UAAU;QACRQ,YAAY;QACZd,8BAA8B;QAC9B,GAAGM,OAAO;IACZ;IAEA,IAAIK,MAAMC,OAAO,CAACX,QAAQ;QACxBA,QAAQA,MACLc,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI,IACjBC,MAAM,CAAC,CAACF,IAAMA,EAAEtB,MAAM,EACtByB,IAAI,CAAC;IACV,OAAO;QACLlB,QAAQA,MAAMgB,IAAI;IACpB;IAEA,IAAIhB,MAAMP,MAAM,KAAK,GAAG;QACtB,OAAO;IACT;IAEA,IAAIO,MAAMP,MAAM,KAAK,GAAG;QACtB,OAAOY,QAAQQ,UAAU,GACrBb,MAAMF,iBAAiB,CAACO,QAAQjB,MAAM,IACtCY,MAAMH,iBAAiB,CAACQ,QAAQjB,MAAM;IAC5C;IAEA,MAAM+B,eAAenB,UAAUA,MAAMH,iBAAiB,CAACQ,QAAQjB,MAAM;IAErE,IAAI+B,cAAc;QAChBnB,QAAQd,kBAAkBc,OAAOK,QAAQjB,MAAM;IACjD;IAEAY,QAAQA,MAAMC,OAAO,CAAC,aAAa;IAEnC,IAAII,QAAQN,4BAA4B,EAAE;QACxCC,QAAQD,6BAA6BC;IACvC,OAAO;QACLA,QAAQA,MAAMH,iBAAiB;IACjC;IAEA,IAAIQ,QAAQQ,UAAU,EAAE;QACtBb,QAAQA,MAAMoB,MAAM,CAAC,GAAGtB,iBAAiB,CAACO,QAAQjB,MAAM,IAAIY,MAAMJ,KAAK,CAAC;IAC1E;IAEA,OAAOQ,YAAYJ,OAAOK;AAC5B;MAEA,WAAeI"}