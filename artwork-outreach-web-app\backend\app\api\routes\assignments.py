"""Assignment API endpoints"""

from datetime import datetime
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from app.database.connection import get_db
from app.database.models import Assignment, Streamer, UserProfile
from app.schemas.assignment import AssignmentResponse, UpdateAssignmentRequest
from app.api.dependencies import get_current_user

router = APIRouter(prefix="/assignments", tags=["assignments"])

@router.post("/", response_model=dict)
async def create_streamer_assignments(
    streamer_ids: List[str],
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create assignments for multiple streamers to the current user."""
    try:
        user_email = current_user.get("email")
        if not user_email:
            raise HTTPException(status_code=401, detail="User email not found")
        
        # Get user profile
        user_result = await db.execute(
            select(UserProfile).where(UserProfile.email == user_email)
        )
        user_profile = user_result.scalar_one_or_none()
        
        if not user_profile:
            raise HTTPException(status_code=404, detail="User profile not found")
        
        assignments_created = []
        for streamer_id in streamer_ids:
            try:
                # Create assignment record
                new_assignment = Assignment(
                    agent_id=user_profile.id,
                    streamer_id=streamer_id,
                    status="assigned",
                    assigned_at=datetime.utcnow()
                )
                db.add(new_assignment)
                assignments_created.append(streamer_id)
            except Exception as e:
                print(f"Error creating assignment for {streamer_id}: {e}")
        
        await db.commit()
        
        return {
            "message": f"Created {len(assignments_created)} assignments",
            "assignments_created": assignments_created
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating assignments: {str(e)}")

@router.get("/", response_model=List[AssignmentResponse])
async def get_user_assignments(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all assignments for the current user."""
    try:
        user_email = current_user.get("email")
        if not user_email:
            raise HTTPException(status_code=401, detail="User email not found")
        
        # Get user profile
        user_result = await db.execute(
            select(UserProfile).where(UserProfile.email == user_email)
        )
        user_profile = user_result.scalar_one_or_none()
        
        if not user_profile:
            raise HTTPException(status_code=404, detail="User profile not found")
        
        # Get assignments with streamer details
        result = await db.execute(
            select(Assignment)
            .options(selectinload(Assignment.streamer))
            .where(Assignment.agent_id == user_profile.id)
            .where(Assignment.deleted_at.is_(None))
            .order_by(Assignment.assigned_at.desc())
        )
        assignments = result.scalars().all()
        
        return [
            AssignmentResponse(
                id=str(assignment.id),
                streamer_id=assignment.streamer_id,
                streamer_username=assignment.streamer.username if assignment.streamer else "Unknown",
                streamer_display_name=assignment.streamer.display_name if assignment.streamer else "Unknown",
                status=assignment.status,
                assigned_at=assignment.assigned_at,
                updated_at=assignment.updated_at,
                notes=assignment.notes
            )
            for assignment in assignments
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving assignments: {str(e)}")

@router.patch("/{assignment_id}/status")
async def update_assignment_status(
    assignment_id: str,
    update_data: UpdateAssignmentRequest,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update the status of an assignment (e.g., mark as 'interested')."""
    try:
        user_email = current_user.get("email")
        if not user_email:
            raise HTTPException(status_code=401, detail="User email not found")
        
        # Get user profile
        user_result = await db.execute(
            select(UserProfile).where(UserProfile.email == user_email)
        )
        user_profile = user_result.scalar_one_or_none()
        
        if not user_profile:
            raise HTTPException(status_code=404, detail="User profile not found")
        
        # Get the assignment
        result = await db.execute(
            select(Assignment)
            .where(Assignment.id == assignment_id)
            .where(Assignment.agent_id == user_profile.id)
            .where(Assignment.deleted_at.is_(None))
        )
        assignment = result.scalar_one_or_none()
        
        if not assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")
        
        # Update the assignment
        await db.execute(
            update(Assignment)
            .where(Assignment.id == assignment_id)
            .values(
                status=update_data.status,
                notes=update_data.notes,
                updated_at=datetime.utcnow()
            )
        )
        
        await db.commit()
        
        return {
            "message": "Assignment updated successfully",
            "assignment_id": assignment_id,
            "new_status": update_data.status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating assignment: {str(e)}")

@router.delete("/{assignment_id}")
async def delete_assignment(
    assignment_id: str,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Soft delete an assignment (mark as deleted)."""
    try:
        user_email = current_user.get("email")
        if not user_email:
            raise HTTPException(status_code=401, detail="User email not found")
        
        # Get user profile
        user_result = await db.execute(
            select(UserProfile).where(UserProfile.email == user_email)
        )
        user_profile = user_result.scalar_one_or_none()
        
        if not user_profile:
            raise HTTPException(status_code=404, detail="User profile not found")
        
        # Get the assignment
        result = await db.execute(
            select(Assignment)
            .where(Assignment.id == assignment_id)
            .where(Assignment.agent_id == user_profile.id)
            .where(Assignment.deleted_at.is_(None))
        )
        assignment = result.scalar_one_or_none()
        
        if not assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")
        
        # Soft delete the assignment
        await db.execute(
            update(Assignment)
            .where(Assignment.id == assignment_id)
            .values(deleted_at=datetime.utcnow())
        )
        
        await db.commit()
        
        return {
            "message": "Assignment deleted successfully",
            "assignment_id": assignment_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting assignment: {str(e)}")