{"version": 3, "sources": ["../../../src/build/templates/app-route.ts"], "names": ["originalPathname", "patchFetch", "requestAsyncStorage", "routeModule", "serverHooks", "staticGenerationAsyncStorage", "AppRouteRouteModule", "definition", "kind", "RouteKind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "_patchFetch"], "mappings": ";;;;;;;;;;;;;;;;;;;IAgDEA,gBAAgB;eAAhBA;;IACAC,UAAU;eAAVA;;IAJAC,mBAAmB;eAAnBA;;IADAC,WAAW;eAAXA;;IAGAC,WAAW;eAAXA;;IADAC,4BAA4B;eAA5BA;;;gCA3CK;2BACmB;4BACgB;sEAEhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B,2EAA2E;AAC3E,UAAU;AACV,0BAA0B;AAE1B,MAAMF,cAAc,IAAIG,mCAAmB,CAAC;IAC1CC,YAAY;QACVC,MAAMC,oBAAS,CAACC,SAAS;QACzBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,kBAAkB;IAClBC;IACAC,UAAAA;AACF;AAEA,2EAA2E;AAC3E,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,EAAEf,mBAAmB,EAAEG,4BAA4B,EAAED,WAAW,EAAE,GACtED;AAEF,MAAMH,mBAAmB;AAEzB,SAASC;IACP,OAAOiB,IAAAA,sBAAW,EAAC;QAAEd;QAAaC;IAA6B;AACjE"}