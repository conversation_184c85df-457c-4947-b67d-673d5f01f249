-- Simple Migration: Add streamer quota tracking to user_profiles table
-- Run this in Supabase SQL Editor

-- Step 1: Add new columns for streamer quota tracking
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS daily_streamers_received INTEGER DEFAULT 0;

ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS daily_streamer_limit INTEGER DEFAULT 200;

-- Step 2: Update existing users to have the default streamer limit
UPDATE user_profiles 
SET daily_streamer_limit = 200 
WHERE daily_streamer_limit IS NULL;

UPDATE user_profiles 
SET daily_streamers_received = 0 
WHERE daily_streamers_received IS NULL;

-- Step 3: Add constraints (run these one by one if needed)
ALTER TABLE user_profiles 
ADD CONSTRAINT cc_user_daily_streamers_received_non_negative 
CHECK (daily_streamers_received >= 0);

ALTER TABLE user_profiles 
ADD CONSTRAINT cc_user_daily_streamer_limit_positive 
CHECK (daily_streamer_limit > 0);

-- Step 4: Create index for better performance
CREATE INDEX idx_user_profiles_quota_lookup 
ON user_profiles (id, last_request_date, daily_streamers_received, daily_streamer_limit);

-- Step 5: Add comments for documentation
COMMENT ON COLUMN user_profiles.daily_streamers_received IS 'Number of live streamers received by user today';
COMMENT ON COLUMN user_profiles.daily_streamer_limit IS 'Daily limit of live streamers per user (default: 200)';

-- Verify the changes
SELECT column_name, data_type, column_default, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND column_name IN ('daily_streamers_received', 'daily_streamer_limit')
ORDER BY column_name;
