#!/usr/bin/env python3
"""
Simple test server to verify FastAPI basics are working
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="Simple Test API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hello World", "status": "API is working!"}

@app.get("/health")
async def health():
    return {"status": "healthy", "message": "Simple test API is running"}

@app.get("/api/v1/streamers/stats")
async def get_stats():
    return {
        "total_streamers": 150,
        "live_streamers": 25,
        "available_streamers": 75,
        "average_followers": 35
    }

@app.get("/api/v1/streamers/available")
async def get_available_streamers():
    return [
        {
            "twitch_user_id": "123456",
            "username": "test_streamer_1",
            "display_name": "Test Streamer 1",
            "follower_count": 45,
            "current_game": "Just Chatting",
            "is_live": True,
            "language": "en",
            "stream_title": "Test stream title"
        },
        {
            "twitch_user_id": "789012",
            "username": "test_streamer_2", 
            "display_name": "Test Streamer 2",
            "follower_count": 23,
            "current_game": "Art",
            "is_live": False,
            "language": "en",
            "stream_title": None
        }
    ]

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000, reload=True)