"""
Supabase API-based service to bypass database connection issues
"""

import requests
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from app.config import get_settings

settings = get_settings()

class SupabaseAPIService:
    """Service that uses Supabase REST API instead of direct database connection"""
    
    def __init__(self):
        self.base_url = settings.SUPABASE_URL
        self.headers = {
            "apikey": settings.SUPABASE_SERVICE_ROLE_KEY,
            "Authorization": f"Bearer {settings.SUPABASE_SERVICE_ROLE_KEY}",
            "Content-Type": "application/json"
        }
    
    def get_streamers_stats(self) -> Dict[str, int]:
        """Get streamer statistics via API"""
        try:
            # Get total count
            response = requests.get(
                f"{self.base_url}/rest/v1/streamers?select=count",
                headers={**self.headers, "Prefer": "count=exact"},
                timeout=10
            )
            
            total_streamers = 0
            if response.status_code in [200, 206]:  # 206 = Partial Content is also valid
                count_header = response.headers.get('Content-Range', '0-0/0')
                if count_header and '/' in count_header:
                    total_streamers = int(count_header.split('/')[-1])
                else:
                    # Fallback: get actual data to count
                    data_response = requests.get(
                        f"{self.base_url}/rest/v1/streamers?select=id",
                        headers=self.headers,
                        timeout=10
                    )
                    if data_response.status_code == 200:
                        total_streamers = len(data_response.json())
            
            # Get live streamers count
            live_response = requests.get(
                f"{self.base_url}/rest/v1/streamers?select=count&is_live=eq.true",
                headers={**self.headers, "Prefer": "count=exact"},
                timeout=10
            )
            
            live_streamers = 0
            if live_response.status_code in [200, 206]:
                count_header = live_response.headers.get('Content-Range', '0-0/0')
                if count_header and '/' in count_header:
                    live_streamers = int(count_header.split('/')[-1])
                else:
                    # Fallback: get actual data to count
                    data_response = requests.get(
                        f"{self.base_url}/rest/v1/streamers?select=id&is_live=eq.true",
                        headers=self.headers,
                        timeout=10
                    )
                    if data_response.status_code == 200:
                        live_streamers = len(data_response.json())
            
            # Get available streamers (0-50 followers, English) - Remove live filter for realistic count
            available_response = requests.get(
                f"{self.base_url}/rest/v1/streamers?select=count&language=eq.en&follower_count=lte.50&follower_count=gte.0",
                headers={**self.headers, "Prefer": "count=exact"},
                timeout=10
            )
            
            available_streamers = 0
            if available_response.status_code in [200, 206]:
                count_header = available_response.headers.get('Content-Range', '0-0/0')
                if count_header and '/' in count_header:
                    available_streamers = int(count_header.split('/')[-1])
                else:
                    # Fallback: get actual data to count
                    data_response = requests.get(
                        f"{self.base_url}/rest/v1/streamers?select=id&is_live=eq.true&language=eq.en&follower_count=lte.50&follower_count=gte.0",
                        headers=self.headers,
                        timeout=10
                    )
                    if data_response.status_code == 200:
                        available_streamers = len(data_response.json())
            
            return {
                "total_streamers": total_streamers,
                "live_streamers": live_streamers,
                "available_streamers": available_streamers,
                "average_followers": 25.0  # Approximate since we can't calculate via API
            }
            
        except Exception as e:
            print(f"Error getting stats via API: {e}")
            return {
                "total_streamers": 0,
                "live_streamers": 0,
                "available_streamers": 0,
                "average_followers": 0.0
            }
    
    def get_available_streamers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get available streamers for outreach via API"""
        try:
            # Get available streamers (0-50 followers, English) - Remove live status filter since data may be stale
            response = requests.get(
                f"{self.base_url}/rest/v1/streamers?select=*&language=eq.en&follower_count=lte.50&follower_count=gte.0&limit={limit}&order=follower_count.desc",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                streamers = response.json()
                
                # Convert to expected format
                result = []
                for streamer in streamers:
                    result.append({
                        "id": streamer.get("id"),
                        "twitch_user_id": streamer.get("twitch_user_id"),
                        "username": streamer.get("username"),
                        "display_name": streamer.get("display_name"),
                        "follower_count": streamer.get("follower_count", 0),
                        "is_live": streamer.get("is_live", False),
                        "current_game": streamer.get("current_game"),
                        "stream_title": streamer.get("stream_title"),
                        "thumbnail_url": streamer.get("thumbnail_url"),
                        "language": streamer.get("language", "en"),
                        "last_seen_live_at": streamer.get("last_seen_live_at"),
                        "created_at": streamer.get("created_at"),
                        "updated_at": streamer.get("updated_at")
                    })
                
                return result
            else:
                print(f"Error getting streamers: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            print(f"Error getting available streamers via API: {e}")
            return []
    
    def create_user_profile(self, email: str, full_name: str) -> Optional[Dict[str, Any]]:
        """Create a user profile via API"""
        try:
            user_data = {
                "email": email,
                "full_name": full_name,
                "role": "agent",
                "is_active": True,
                "daily_request_count": 0
            }
            
            response = requests.post(
                f"{self.base_url}/rest/v1/user_profiles",
                headers=self.headers,
                json=user_data,
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                return response.json()
            else:
                print(f"Error creating user: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Error creating user via API: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email via API"""
        try:
            response = requests.get(
                f"{self.base_url}/rest/v1/user_profiles?select=*&email=eq.{email}&limit=1",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                users = response.json()
                return users[0] if users else None
            else:
                print(f"Error getting user: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Error getting user via API: {e}")
            return None

# Global instance
supabase_api = SupabaseAPIService()