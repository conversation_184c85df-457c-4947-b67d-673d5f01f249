"""
Advanced logging configuration for automation system
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional

def setup_automation_logging(
    log_level: str = "INFO",
    log_dir: Optional[str] = None,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    Setup comprehensive logging for automation system
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_dir: Directory for log files (defaults to logs/)
        max_bytes: Maximum size per log file
        backup_count: Number of backup files to keep
    
    Returns:
        Configured logger instance
    """
    
    # Create logs directory
    if log_dir is None:
        log_dir = Path("logs")
    else:
        log_dir = Path(log_dir)
    
    log_dir.mkdir(exist_ok=True)
    
    # Create logger
    logger = logging.getLogger("automation")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    # Main log file (rotating)
    main_log_file = log_dir / "automation.log"
    file_handler = logging.handlers.RotatingFileHandler(
        main_log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    logger.addHandler(file_handler)
    
    # Daily log file
    today = datetime.now().strftime('%Y%m%d')
    daily_log_file = log_dir / f"automation_{today}.log"
    daily_handler = logging.FileHandler(daily_log_file, encoding='utf-8')
    daily_handler.setLevel(logging.INFO)
    daily_handler.setFormatter(detailed_formatter)
    logger.addHandler(daily_handler)
    
    # Error log file
    error_log_file = log_dir / "errors.log"
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    logger.addHandler(error_handler)
    
    # Performance log file
    perf_log_file = log_dir / "performance.log"
    perf_handler = logging.handlers.RotatingFileHandler(
        perf_log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    perf_handler.setLevel(logging.INFO)
    perf_handler.setFormatter(detailed_formatter)
    
    # Create performance logger
    perf_logger = logging.getLogger("automation.performance")
    perf_logger.addHandler(perf_handler)
    perf_logger.setLevel(logging.INFO)
    
    logger.info("=" * 60)
    logger.info("🚀 AUTOMATION LOGGING INITIALIZED")
    logger.info(f"📁 Log Directory: {log_dir.absolute()}")
    logger.info(f"📊 Log Level: {log_level}")
    logger.info(f"📄 Main Log: {main_log_file}")
    logger.info(f"📅 Daily Log: {daily_log_file}")
    logger.info(f"❌ Error Log: {error_log_file}")
    logger.info(f"⚡ Performance Log: {perf_log_file}")
    logger.info("=" * 60)
    
    return logger

def log_performance_metrics(
    operation: str,
    duration: float,
    success: bool,
    details: Optional[dict] = None
):
    """Log performance metrics"""
    perf_logger = logging.getLogger("automation.performance")
    
    status = "SUCCESS" if success else "FAILED"
    message = f"{operation} - {status} - Duration: {duration:.2f}s"
    
    if details:
        detail_str = " - ".join([f"{k}: {v}" for k, v in details.items()])
        message += f" - {detail_str}"
    
    perf_logger.info(message)

def log_scraper_run(
    streamers_found: int,
    streamers_loaded: int,
    duration: float,
    success: bool,
    errors: Optional[list] = None
):
    """Log scraper run results"""
    logger = logging.getLogger("automation")
    
    if success:
        logger.info("=" * 50)
        logger.info("✅ SCRAPER RUN COMPLETED SUCCESSFULLY")
        logger.info(f"📊 Streamers Found: {streamers_found}")
        logger.info(f"💾 Streamers Loaded: {streamers_loaded}")
        logger.info(f"⏱️ Duration: {duration:.2f} seconds")
        logger.info("=" * 50)
    else:
        logger.error("=" * 50)
        logger.error("❌ SCRAPER RUN FAILED")
        logger.error(f"📊 Streamers Found: {streamers_found}")
        logger.error(f"💾 Streamers Loaded: {streamers_loaded}")
        logger.error(f"⏱️ Duration: {duration:.2f} seconds")
        if errors:
            logger.error("🐛 Errors:")
            for error in errors:
                logger.error(f"   - {error}")
        logger.error("=" * 50)
    
    # Log performance metrics
    log_performance_metrics(
        "SCRAPER_RUN",
        duration,
        success,
        {
            "streamers_found": streamers_found,
            "streamers_loaded": streamers_loaded,
            "success_rate": f"{(streamers_loaded/streamers_found*100):.1f}%" if streamers_found > 0 else "0%"
        }
    )

def log_scheduler_event(event_type: str, job_id: str, details: Optional[str] = None):
    """Log scheduler events"""
    logger = logging.getLogger("automation")
    
    emoji_map = {
        "JOB_STARTED": "🚀",
        "JOB_COMPLETED": "✅", 
        "JOB_FAILED": "❌",
        "JOB_MISSED": "⚠️",
        "SCHEDULER_STARTED": "🎯",
        "SCHEDULER_STOPPED": "🛑"
    }
    
    emoji = emoji_map.get(event_type, "📝")
    message = f"{emoji} {event_type}: {job_id}"
    
    if details:
        message += f" - {details}"
    
    if event_type in ["JOB_FAILED", "JOB_MISSED"]:
        logger.error(message)
    elif event_type in ["SCHEDULER_STARTED", "SCHEDULER_STOPPED"]:
        logger.warning(message)
    else:
        logger.info(message)

def cleanup_old_logs(log_dir: Optional[str] = None, days_to_keep: int = 7):
    """Clean up old log files"""
    if log_dir is None:
        log_dir = Path("logs")
    else:
        log_dir = Path(log_dir)
    
    if not log_dir.exists():
        return
    
    logger = logging.getLogger("automation")
    cutoff_date = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
    
    cleaned_files = 0
    for log_file in log_dir.glob("*.log*"):
        if log_file.stat().st_mtime < cutoff_date:
            try:
                log_file.unlink()
                cleaned_files += 1
                logger.info(f"🧹 Cleaned up old log file: {log_file.name}")
            except Exception as e:
                logger.error(f"❌ Failed to clean up {log_file.name}: {e}")
    
    if cleaned_files > 0:
        logger.info(f"🧹 Cleaned up {cleaned_files} old log files")
    else:
        logger.info("🧹 No old log files to clean up")

# Context manager for timing operations
class LoggedOperation:
    """Context manager for logging timed operations"""
    
    def __init__(self, operation_name: str, logger: Optional[logging.Logger] = None):
        self.operation_name = operation_name
        self.logger = logger or logging.getLogger("automation")
        self.start_time = None
        self.success = False
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.info(f"🚀 Starting {self.operation_name}...")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        if exc_type is None:
            self.success = True
            self.logger.info(f"✅ {self.operation_name} completed in {duration:.2f}s")
        else:
            self.logger.error(f"❌ {self.operation_name} failed after {duration:.2f}s: {exc_val}")
        
        # Log performance metrics
        log_performance_metrics(
            self.operation_name.upper().replace(" ", "_"),
            duration,
            self.success
        )
