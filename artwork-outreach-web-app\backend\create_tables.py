#!/usr/bin/env python3
"""
Create database tables for Artwork Outreach Web App
Run this script to set up the database schema
"""

import asyncio
import asyncpg
from app.config import get_settings
from app.database.models import Base
from sqlalchemy.ext.asyncio import create_async_engine

settings = get_settings()

async def create_tables():
    """Create database tables using SQLAlchemy models"""
    print("🗄️  Creating database tables...")
    
    # Create async engine
    engine = create_async_engine(settings.DATABASE_URL, echo=True)
    
    try:
        # Create all tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        print("✅ Database tables created successfully!")
        
        # Verify tables exist
        async with engine.connect() as conn:
            result = await conn.execute(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
            )
            tables = [row[0] for row in result.fetchall()]
            print(f"📋 Created tables: {', '.join(tables)}")
            
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        raise
    finally:
        await engine.dispose()

async def seed_test_user():
    """Create a test user for development"""
    print("👤 Creating test user...")
    
    try:
        conn = await asyncpg.connect(settings.DATABASE_URL)
        
        # Insert test user if doesn't exist
        await conn.execute("""
            INSERT INTO user_profiles (email, full_name, role, is_active)
            VALUES ('<EMAIL>', 'Test Agent', 'agent', true)
            ON CONFLICT (email) DO NOTHING
        """)
        
        print("✅ Test user created: <EMAIL>")
        
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
    finally:
        await conn.close()

async def main():
    """Main setup function"""
    print("🚀 Setting up Artwork Outreach Database\n")
    
    await create_tables()
    await seed_test_user()
    
    print("\n🎉 Database setup complete!")
    print("💡 Next steps:")
    print("   1. Start the backend: uvicorn app.main:app --reload")
    print("   2. Run the scraper: python -m app.scraper.background_tasks")
    print("   3. Test the API: http://localhost:8000/docs")

if __name__ == "__main__":
    asyncio.run(main())