import factory
from faker import Faker
from sqlalchemy.orm import Session
from app.database import models

fake = Faker()

class UserProfileFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.UserProfile
        sqlalchemy_session = None  # Session is passed in create()
        sqlalchemy_session_persistence = "flush"

    id = factory.LazyFunction(fake.uuid4)
    email = factory.LazyFunction(fake.email)
    full_name = factory.LazyFunction(fake.name)
    daily_request_count = 0
    last_request_date = None
    role = "agent"
    is_active = True

class StreamerFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Streamer
        sqlalchemy_session = None  # Session is passed in create()
        sqlalchemy_session_persistence = "flush"

    id = factory.LazyFunction(fake.uuid4)
    twitch_user_id = factory.LazyFunction(lambda: str(fake.random_number(digits=8)))
    username = factory.LazyFunction(fake.user_name)
    display_name = factory.LazyFunction(fake.name)
    follower_count = factory.LazyFunction(lambda: fake.random_number(digits=5))
    is_live = False
    language = "en"

class AssignmentFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        model = models.Assignment
        sqlalchemy_session = None  # Session is passed in create()
        sqlalchemy_session_persistence = "flush"

    id = factory.LazyFunction(fake.uuid4)
    status = "assigned"
    notes = factory.LazyFunction(fake.sentence)

    @factory.lazy_attribute
    def agent_id(self):
        if not self.user:
            user = UserProfileFactory()
            self.__class__._meta.sqlalchemy_session.add(user)
            return user.id
        return self.user.id

    @factory.lazy_attribute
    def streamer_id(self):
        if not self.streamer:
            streamer = StreamerFactory()
            self.__class__._meta.sqlalchemy_session.add(streamer)
            return streamer.twitch_user_id
        return self.streamer.twitch_user_id

def setup_factories(session: Session):
    UserProfileFactory._meta.sqlalchemy_session = session
    StreamerFactory._meta.sqlalchemy_session = session
    AssignmentFactory._meta.sqlalchemy_session = session
