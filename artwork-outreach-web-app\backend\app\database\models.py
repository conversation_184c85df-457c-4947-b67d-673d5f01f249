import uuid
from datetime import datetime

from sqlalchemy import (
    Bo<PERSON>an,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
    CheckConstraint,
    Index,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import TypeDecorator, CHAR
import uuid

# SQLite compatibility for UUID
class GUID(TypeDecorator):
    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(UUID())
        else:
            return dialect.type_descriptor(CHAR(32))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return "%.32x" % uuid.UUID(value).int
            else:
                return "%.32x" % value.int

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            else:
                return value
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class Streamer(Base):
    __tablename__ = "streamers"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    twitch_user_id = Column(String, unique=True, nullable=False, index=True)
    username = Column(String, nullable=False, index=True)
    display_name = Column(String)
    follower_count = Column(Integer, default=0, index=True)
    is_live = Column(Boolean, default=False, index=True)
    current_game = Column(String)
    stream_title = Column(String)
    thumbnail_url = Column(String)
    profile_image_url = Column(String)
    language = Column(String, default="en", index=True)
    last_seen_live_at = Column(DateTime(timezone=True), index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    assignments = relationship("Assignment", back_populates="streamer")

    __table_args__ = (
        CheckConstraint("follower_count >= 0", name="cc_streamer_follower_count_non_negative"),
        # Composite indexes for optimized queries
        Index("idx_streamers_available", "is_live", "language", "follower_count"),
        Index("idx_streamers_live_followers", "is_live", "follower_count"),
        Index("idx_streamers_search", "username", "display_name"),
        Index("idx_streamers_pagination", "created_at", "id"),
        Index("idx_streamers_follower_sort", "follower_count", "id"),
    )


class UserProfile(Base):
    __tablename__ = "user_profiles"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, nullable=False, index=True)
    full_name = Column(String)
    daily_request_count = Column(Integer, default=0)
    last_request_date = Column(DateTime(timezone=True), index=True)
    # New streamer quota tracking
    daily_streamers_received = Column(Integer, default=0)  # Track streamers received today
    daily_streamer_limit = Column(Integer, default=200)    # Daily limit per agent (150-200)
    role = Column(String, default="agent")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    assignments = relationship("Assignment", back_populates="user")

    __table_args__ = (
        CheckConstraint(
            "daily_request_count >= 0", name="cc_user_daily_request_count_non_negative"
        ),
        CheckConstraint(
            "daily_streamers_received >= 0", name="cc_user_daily_streamers_received_non_negative"
        ),
        CheckConstraint(
            "daily_streamer_limit > 0", name="cc_user_daily_streamer_limit_positive"
        ),
    )


class Assignment(Base):
    __tablename__ = "assignments"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    agent_id = Column(
        GUID(),
        ForeignKey("user_profiles.id", ondelete="CASCADE"),
        index=True,
    )
    streamer_id = Column(
        String,
        ForeignKey("streamers.twitch_user_id", ondelete="CASCADE"),
        index=True,
    )
    status = Column(String, default="assigned", index=True)
    assigned_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True, index=True)
    notes = Column(Text)

    user = relationship("UserProfile", back_populates="assignments")
    streamer = relationship("Streamer", back_populates="assignments")

    __table_args__ = (
        UniqueConstraint("agent_id", "streamer_id", name="uq_agent_streamer"),
        # Optimize assignment lookups for recent assignments
        Index("idx_assignments_recent", "assigned_at", "streamer_id"),
        Index("idx_assignments_status", "status", "assigned_at"),
    )


class ScraperRun(Base):
    __tablename__ = "scraper_runs"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    run_at = Column(DateTime(timezone=True), server_default=func.now())
    status = Column(String, nullable=False)  # e.g., "started", "completed", "failed"
    streamers_found = Column(Integer, default=0)
    details = Column(Text)  # For storing error messages or other details

    __table_args__ = (
        CheckConstraint(
            "streamers_found >= 0", name="cc_scraper_run_streamers_found_non_negative"
        ),
    )