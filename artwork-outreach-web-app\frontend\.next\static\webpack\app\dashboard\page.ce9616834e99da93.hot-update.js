"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\nconst API_BASE_URL = \"http://127.0.0.1:8000/api/v1\" || 0;\nclass ApiClient {\n    async makeRequest(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        // Get auth token from Clerk\n        const token = await this.getAuthToken();\n        const defaultHeaders = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            defaultHeaders[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const config = {\n            ...options,\n            headers: {\n                ...defaultHeaders,\n                ...options.headers\n            }\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        detail: \"Unknown error\"\n                    }));\n                throw new Error(errorData.detail || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            throw error;\n        }\n    }\n    async getAuthToken() {\n        // This will be implemented with Clerk integration\n        // For now, return null (unauthenticated requests)\n        return null;\n    }\n    calculateTimeSinceLive(lastSeenLiveAt) {\n        if (!lastSeenLiveAt) return null;\n        const now = new Date();\n        const lastSeen = new Date(lastSeenLiveAt);\n        const diffMs = now.getTime() - lastSeen.getTime();\n        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffDays > 0) {\n            return \"\".concat(diffDays, \" day\").concat(diffDays > 1 ? \"s\" : \"\", \" ago\");\n        } else if (diffHours > 0) {\n            return \"\".concat(diffHours, \" hour\").concat(diffHours > 1 ? \"s\" : \"\", \" ago\");\n        } else {\n            return \"Recently\";\n        }\n    }\n    formatFollowerCount(count) {\n        if (count >= 1000000) {\n            return \"\".concat((count / 1000000).toFixed(1), \"M\");\n        } else if (count >= 1000) {\n            return \"\".concat((count / 1000).toFixed(1), \"K\");\n        } else {\n            return count.toString();\n        }\n    }\n    // Streamer API methods\n    async getAvailableStreamers() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50;\n        const backendStreamers = await this.makeRequest(\"/fallback/available?limit=\".concat(limit));\n        // Transform backend data to frontend format\n        const streamers = backendStreamers.map((streamer)=>({\n                id: streamer.id || streamer.twitch_user_id,\n                twitch_user_id: streamer.twitch_user_id,\n                username: streamer.username,\n                display_name: streamer.display_name,\n                follower_count: streamer.follower_count,\n                is_live: streamer.is_live || false,\n                current_game: streamer.current_game || null,\n                stream_title: streamer.stream_title || null,\n                profile_image_url: streamer.thumbnail_url || \"https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png\",\n                language: streamer.language,\n                last_seen_live_at: streamer.last_seen_live_at || null,\n                created_at: streamer.created_at || new Date().toISOString(),\n                updated_at: streamer.updated_at || new Date().toISOString(),\n                time_since_live: this.calculateTimeSinceLive(streamer.last_seen_live_at),\n                formatted_follower_count: this.formatFollowerCount(streamer.follower_count),\n                assignment_status_for_current_user: null\n            }));\n        return {\n            streamers,\n            total: streamers.length,\n            user_status: {\n                daily_requests_used: 1,\n                daily_requests_remaining: 2,\n                can_make_request: true\n            }\n        };\n    }\n    async getLiveVerifiedStreamers() {\n        let batchSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 100;\n        const response = await this.makeRequest(\"/fallback/live-verified?batch_size=\".concat(batchSize));\n        // Transform backend data to frontend format\n        const streamers = response.streamers.map((streamer)=>({\n                id: streamer.id || streamer.twitch_user_id,\n                twitch_user_id: streamer.twitch_user_id,\n                username: streamer.username,\n                display_name: streamer.display_name,\n                follower_count: streamer.follower_count,\n                is_live: true,\n                current_game: streamer.current_game || null,\n                stream_title: streamer.stream_title || null,\n                profile_image_url: streamer.thumbnail_url || \"https://static-cdn.jtvnw.net/jtv_user_pictures/default-profile_image-300x300.png\",\n                language: streamer.language,\n                last_seen_live_at: new Date().toISOString(),\n                created_at: streamer.created_at || new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                time_since_live: \"Live now\",\n                formatted_follower_count: this.formatFollowerCount(streamer.follower_count),\n                assignment_status_for_current_user: null\n            }));\n        return {\n            streamers,\n            batch_size: response.batch_size,\n            checked_count: response.checked_count,\n            verification_method: response.verification_method,\n            status: response.status,\n            quota_info: response.quota_info\n        };\n    }\n    async getStreamerStats() {\n        const backendStats = await this.makeRequest(\"/fallback/stats\");\n        return {\n            total_streamers: backendStats.total_streamers,\n            live_streamers: backendStats.live_streamers,\n            available_streamers: backendStats.available_streamers,\n            last_updated: new Date().toISOString()\n        };\n    }\n    async triggerScrape() {\n        return this.makeRequest(\"/api/v1/streamers/trigger-scrape\", {\n            method: \"POST\"\n        });\n    }\n    async getScraperStatus() {\n        return this.makeRequest(\"/api/v1/streamers/scraper-status\");\n    }\n    // Assignment API methods\n    async getUserAssignments() {\n        return this.makeRequest(\"/api/v1/assignments/\");\n    }\n    async updateAssignmentStatus(assignmentId, status, notes) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId, \"/status\"), {\n            method: \"PATCH\",\n            body: JSON.stringify({\n                status,\n                notes\n            })\n        });\n    }\n    async deleteAssignment(assignmentId) {\n        return this.makeRequest(\"/api/v1/assignments/\".concat(assignmentId), {\n            method: \"DELETE\"\n        });\n    }\n    // Health check\n    async healthCheck() {\n        return this.makeRequest(\"/api/v1/health\");\n    }\n    constructor(){\n        this.baseURL = API_BASE_URL;\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});