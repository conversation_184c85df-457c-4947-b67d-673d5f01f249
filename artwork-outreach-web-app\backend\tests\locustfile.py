from locust import HttpUser, task, between
from uuid import uuid4

class APIUser(HttpUser):
    wait_time = between(1, 5)
    auth_headers = {}
    assignment_id = None

    def on_start(self):
        # In a real scenario, you would log in and get a token.
        # For this test, we'll use a dummy user ID.
        self.auth_headers = {"Authorization": "Bearer dummy_token"}

    @task
    def health_check(self):
        self.client.get("/api/v1/health")

    @task
    def get_streamers(self):
        self.client.get("/api/v1/streamers", headers=self.auth_headers)

    @task
    def get_user_status(self):
        self.client.get("/api/v1/users/status", headers=self.auth_headers)

    @task
    def update_assignment(self):
        if self.assignment_id:
            self.client.patch(
                f"/api/v1/assignments/{self.assignment_id}",
                json={"status": "interested"},
                headers=self.auth_headers,
            )

    @task
    def trigger_scrape(self):
        self.client.post("/api/v1/worker/trigger-scrape", headers=self.auth_headers)

    def on_stop(self):
        # Clean up any created resources
        pass
