"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/StreamerList */ \"(app-pages-browser)/./src/components/dashboard/StreamerList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/AssignmentList */ \"(app-pages-browser)/./src/components/dashboard/AssignmentList.tsx\");\n/* harmony import */ var _components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/AssignmentAnalytics */ \"(app-pages-browser)/./src/components/dashboard/AssignmentAnalytics.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [streamers, setStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredStreamers, setFilteredStreamers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStatus, setUserStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [backendConnected, setBackendConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filter states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showLiveOnly, setShowLiveOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedGame, setSelectedGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [minFollowers, setMinFollowers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Live verification states\n    const [useVerifiedLive, setUseVerifiedLive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Default to verified live\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const API_BASE_URL = \"/api\";\n    // Filter streamers based on current filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = streamers;\n        if (searchQuery) {\n            filtered = filtered.filter((streamer)=>{\n                var _streamer_current_game;\n                return streamer.display_name.toLowerCase().includes(searchQuery.toLowerCase()) || streamer.username.toLowerCase().includes(searchQuery.toLowerCase()) || ((_streamer_current_game = streamer.current_game) === null || _streamer_current_game === void 0 ? void 0 : _streamer_current_game.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        if (showLiveOnly) {\n            filtered = filtered.filter((streamer)=>streamer.is_live);\n        }\n        if (selectedGame) {\n            filtered = filtered.filter((streamer)=>streamer.current_game === selectedGame);\n        }\n        if (minFollowers > 0) {\n            filtered = filtered.filter((streamer)=>streamer.follower_count >= minFollowers);\n        }\n        setFilteredStreamers(filtered);\n    }, [\n        streamers,\n        searchQuery,\n        showLiveOnly,\n        selectedGame,\n        minFollowers\n    ]);\n    // Fetch initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStats();\n        fetchUserStatus();\n        fetchAssignments();\n        // Auto-load streamers on page load\n        fetchNewStreamers();\n    }, []);\n    const fetchUserStatus = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/user/status\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUserStatus(data);\n            } else {\n                console.error(\"Failed to fetch user status - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch user status - Connection error:\", err);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            console.log(\"Fetching stats using API client...\");\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getStreamerStats();\n            setStats({\n                total_streamers: data.total_streamers,\n                live_streamers: data.live_streamers,\n                available_streamers: data.available_streamers,\n                average_followers: 25 // Use static value since backend doesn't calculate this\n            });\n            setBackendConnected(true);\n            console.log(\"Stats fetched successfully:\", data);\n        } catch (err) {\n            console.error(\"Failed to fetch stats - Connection error:\", err);\n            setBackendConnected(false);\n            // Set default stats if backend is not available\n            setStats({\n                total_streamers: 0,\n                live_streamers: 0,\n                available_streamers: 0,\n                average_followers: 0\n            });\n        }\n    };\n    const fetchNewStreamers = async ()=>{\n        console.log(\"=== FETCH NEW STREAMERS BUTTON CLICKED ===\");\n        setLoading(true);\n        setError(null);\n        setVerificationStatus(\"\");\n        try {\n            if (useVerifiedLive) {\n                console.log(\"Fetching VERIFIED LIVE streamers using real-time API...\");\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getLiveVerifiedStreamers(10);\n                console.log(\"Received verified live data:\", data);\n                const streamersArray = data.streamers || [];\n                setStreamers(streamersArray);\n                setVerificationStatus(\"\".concat(data.status, \" (checked \").concat(data.checked_count, \" streamers)\"));\n                console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" VERIFIED LIVE streamers!\"));\n            } else {\n                console.log(\"Fetching streamers using database API...\");\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getAvailableStreamers(50);\n                console.log(\"Received database data:\", data);\n                const streamersArray = data.streamers || [];\n                setStreamers(streamersArray);\n                setVerificationStatus(\"Using database data (may include offline streamers)\");\n                // Update user status from response\n                if (data.user_status) {\n                    setUserStatus({\n                        daily_request_count: data.user_status.daily_requests_used,\n                        remaining_requests: data.user_status.daily_requests_remaining,\n                        next_reset_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now\n                    });\n                }\n                console.log(\"✅ Success! Loaded \".concat(streamersArray.length, \" streamers from database!\"));\n            }\n            // Refresh stats after fetching streamers\n            await fetchStats();\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to fetch streamers\";\n            setError(errorMessage);\n            console.error(\"=== ERROR FETCHING STREAMERS ===\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAssignments = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments\"), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setAssignments(data.assignments);\n            } else {\n                console.error(\"Failed to fetch assignments - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to fetch assignments - Connection error:\", err);\n        }\n    };\n    const handleUpdateAssignment = async (id, status, notes)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status,\n                    notes\n                })\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to update assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to update assignment - Connection error:\", err);\n        }\n    };\n    const handleDeleteAssignment = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/assignments/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                fetchAssignments();\n            } else {\n                console.error(\"Failed to delete assignment - Server responded with:\", response.status);\n            }\n        } catch (err) {\n            console.error(\"Failed to delete assignment - Connection error:\", err);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Please sign in to access the dashboard.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 233,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"\\uD83C\\uDFAF Streamer Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.name) || \"Agent\",\n                                \"! Discover and manage your streamer outreach campaigns.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Total Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.total_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-green-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-green-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Live Now\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.live_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-purple-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.available_streamers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-yellow-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-yellow-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Loaded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: filteredStreamers.length > 0 && filteredStreamers.length !== streamers.length ? \"\".concat(filteredStreamers.length, \"/\").concat(streamers.length) : streamers.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                children: \"Streamer Discovery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: useVerifiedLive ? \"Get real-time verified live streamers (recommended for outreach)\" : \"Load streamers from database (may include offline streamers)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            verificationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-purple-600 mt-1\",\n                                                children: [\n                                                    \"\\uD83D\\uDCCA \",\n                                                    verificationStatus\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center bg-gray-50 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: useVerifiedLive,\n                                                            onChange: (e)=>setUseVerifiedLive(e.target.checked),\n                                                            className: \"rounded border-gray-300 text-purple-600 focus:ring-purple-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: useVerifiedLive ? \"✅ Live Verified\" : \"\\uD83D\\uDCCA Database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: fetchNewStreamers,\n                                                disabled: loading || (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0,\n                                                className: \"px-6 py-3 rounded-lg font-medium text-white transition-all \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"bg-gray-400 cursor-not-allowed\" : loading ? \"bg-blue-400 cursor-wait\" : useVerifiedLive ? \"bg-green-600 hover:bg-green-700 hover:shadow-lg\" : \"bg-blue-600 hover:bg-blue-700 hover:shadow-lg\"),\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        useVerifiedLive ? \"Verifying...\" : \"Loading...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this) : (userStatus === null || userStatus === void 0 ? void 0 : userStatus.remaining_requests) === 0 ? \"❌ Daily Limit Reached\" : useVerifiedLive ? \"\\uD83D\\uDD34 Get Live Streamers\" : \"\\uD83D\\uDD04 Refresh Streamers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this),\n                            userStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"Daily requests: \",\n                                                userStatus.daily_request_count,\n                                                \"/3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full mr-2 \".concat(backendConnected === true ? \"bg-green-500\" : backendConnected === false ? \"bg-red-500\" : \"bg-yellow-500\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: backendConnected === true ? \"Backend Connected\" : backendConnected === false ? \"Backend Disconnected\" : \"Checking...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: [\n                                        \"⚠️ \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                streamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"\\uD83D\\uDD0D Filter Streamers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search by name or game...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: showLiveOnly,\n                                                        onChange: (e)=>setShowLiveOnly(e.target.checked),\n                                                        className: \"rounded border-gray-300 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                        children: \"Live only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Game\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedGame,\n                                                onChange: (e)=>setSelectedGame(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All games\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    Array.from(new Set(streamers.map((s)=>s.current_game).filter(Boolean))).map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: game,\n                                                            children: game\n                                                        }, game, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Min Followers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"0\",\n                                                value: minFollowers || \"\",\n                                                onChange: (e)=>setMinFollowers(Number(e.target.value) || 0),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Showing \",\n                                            filteredStreamers.length,\n                                            \" of \",\n                                            streamers.length,\n                                            \" streamers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setSearchQuery(\"\");\n                                            setShowLiveOnly(false);\n                                            setSelectedGame(\"\");\n                                            setMinFollowers(0);\n                                        },\n                                        className: \"text-sm text-purple-600 hover:text-purple-700 font-medium\",\n                                        children: \"Clear filters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        \"\\uD83C\\uDFAE Available Streamers \",\n                                        filteredStreamers.length > 0 && \"(\".concat(filteredStreamers.length, \")\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this),\n                                filteredStreamers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredStreamers.length,\n                                        \" streamers\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(8)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-48 bg-gray-200 rounded-lg mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-2/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this) : filteredStreamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StreamerList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            streamers: filteredStreamers,\n                            assignments: assignments,\n                            onInterestLevelChanged: ()=>{}\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 13\n                        }, this) : streamers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers match your filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Try adjusting your search criteria or clear the filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 1,\n                                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No streamers loaded yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'Click \"Refresh Streamers\" to load available streamers from your database'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentAnalytics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        assignments: assignments\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 531,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AssignmentList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        assignments: assignments,\n                        onUpdate: handleUpdateAssignment,\n                        onDelete: handleDeleteAssignment\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 536,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Twitch-Scraper-main\\\\artwork-outreach-web-app\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"N9LcwhoMswGSW2bkjNzTjbEQrbQ=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});