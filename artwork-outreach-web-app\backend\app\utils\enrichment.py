from datetime import datetime
from typing import List, Optional, TYPE_CHECKING

import humanize

if TYPE_CHECKING:
    from app.database.models import Assignment, Streamer


def calculate_time_since_last_live(last_seen: Optional[datetime]) -> Optional[str]:
    """
    Calculates a human-readable string for the time since a streamer was last live.
    """
    if not last_seen:
        return None
    return humanize.naturaltime(datetime.now() - last_seen)


def format_follower_count(count: int) -> str:
    """
    Formats a large integer into a more readable string (e.g., 1.2M).
    """
    if count < 1000:
        return str(count)
    if count < 1_000_000:
        return f"{count / 1000:.1f}K"
    return f"{count / 1_000_000:.1f}M"


def generate_thumbnail_url(twitch_user_id: str, width: int, height: int) -> str:
    """
    Constructs a URL for a streamer's thumbnail.
    """
    return f"https://static-cdn.jtvnw.net/previews-ttv/live_user_{twitch_user_id}-{width}x{height}.jpg"


def determine_assignment_eligibility(
    streamer, assignments: List
) -> bool:
    """
    Determines if a streamer is eligible for a new assignment.
    """
    # Business logic to be defined, e.g., not assigned in the last 7 days
    return True