import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch

def test_update_user_profile(client: TestClient):
    """
    Test updating the user profile.
    """
    with patch("app.services.user_service.update_user_profile") as mock_update:
        mock_update.return_value = {"username": "testuser", "email": "<EMAIL>"}
        response = client.patch("/api/v1/user/profile", json={"email": "<EMAIL>"})
        assert response.status_code == 200
        assert response.json()["email"] == "<EMAIL>"
        mock_update.assert_called_once()

def test_update_user_profile_invalid_input(client: TestClient):
    """
    Test updating the user profile with invalid input.
    """
    response = client.patch("/api/v1/user/profile", json={"email": "not-an-email"})
    assert response.status_code == 422