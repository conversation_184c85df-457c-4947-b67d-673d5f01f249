"""
Application monitoring and observability setup.
Includes Sentry error tracking, performance monitoring, and custom metrics.
"""

import os
import time
import logging
import sentry_sdk
from typing import Dict, Any, Optional
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.asyncio import AsyncioIntegration
from fastapi import Request, Response
from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


def init_sentry():
    """Initialize Sentry error tracking and performance monitoring."""
    if not settings.SENTRY_DSN:
        logger.warning("SENTRY_DSN not configured, skipping Sentry initialization")
        return

    sentry_logging = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )

    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        environment=settings.ENVIRONMENT,
        release=os.getenv("RENDER_GIT_COMMIT", "development"),
        integrations=[
            FastApiIntegration(auto_enabling_integrations=False),
            SqlalchemyIntegration(),
            sentry_logging,
            AsyncioIntegration(),
        ],
        traces_sample_rate=0.1 if settings.ENVIRONMENT == "production" else 1.0,
        profiles_sample_rate=0.1 if settings.ENVIRONMENT == "production" else 1.0,
        send_default_pii=False,
        attach_stacktrace=True,
        include_local_variables=True,
        max_breadcrumbs=50,
        before_send=filter_sensitive_data,
    )
    
    logger.info("Sentry monitoring initialized")


def filter_sensitive_data(event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Filter sensitive data from Sentry events."""
    # Remove sensitive data from request/response
    if 'request' in event:
        request = event['request']
        
        # Remove sensitive headers
        if 'headers' in request:
            sensitive_headers = ['authorization', 'cookie', 'x-api-key']
            for header in sensitive_headers:
                if header in request['headers']:
                    request['headers'][header] = '[Filtered]'
        
        # Remove sensitive query parameters
        if 'query_string' in request:
            sensitive_params = ['token', 'key', 'secret', 'password']
            # This would need more sophisticated filtering in practice
            for param in sensitive_params:
                if param in str(request.get('query_string', '')):
                    request['query_string'] = '[Filtered]'
    
    return event


class PerformanceMonitor:
    """Custom performance monitoring and metrics collection."""
    
    def __init__(self):
        self.metrics = {}
        self.request_times = []
        
    def record_request_time(self, endpoint: str, duration: float):
        """Record request processing time."""
        if endpoint not in self.metrics:
            self.metrics[endpoint] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'times': []
            }
        
        metric = self.metrics[endpoint]
        metric['count'] += 1
        metric['total_time'] += duration
        metric['min_time'] = min(metric['min_time'], duration)
        metric['max_time'] = max(metric['max_time'], duration)
        metric['times'].append(duration)
        
        # Keep only last 100 times for memory efficiency
        if len(metric['times']) > 100:
            metric['times'] = metric['times'][-100:]
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get collected performance metrics."""
        result = {}
        
        for endpoint, metric in self.metrics.items():
            if metric['count'] > 0:
                avg_time = metric['total_time'] / metric['count']
                times = sorted(metric['times'])
                
                # Calculate percentiles
                p95_idx = int(0.95 * len(times))
                p99_idx = int(0.99 * len(times))
                
                result[endpoint] = {
                    'count': metric['count'],
                    'avg_time': round(avg_time, 4),
                    'min_time': round(metric['min_time'], 4),
                    'max_time': round(metric['max_time'], 4),
                    'p95_time': round(times[p95_idx] if times else 0, 4),
                    'p99_time': round(times[p99_idx] if times else 0, 4),
                }
        
        return result
    
    def reset_metrics(self):
        """Reset collected metrics."""
        self.metrics.clear()


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


async def monitor_request_performance(request: Request, call_next):
    """Middleware to monitor request performance."""
    start_time = time.time()
    
    # Add request info to Sentry scope
    with sentry_sdk.configure_scope() as scope:
        scope.set_tag("endpoint", request.url.path)
        scope.set_tag("method", request.method)
        scope.set_context("request", {
            "url": str(request.url),
            "method": request.method,
            "headers": dict(request.headers),
        })
    
    try:
        response = await call_next(request)
        duration = time.time() - start_time
        
        # Record performance metrics
        endpoint = f"{request.method} {request.url.path}"
        performance_monitor.record_request_time(endpoint, duration)
        
        # Add response info to Sentry
        with sentry_sdk.configure_scope() as scope:
            scope.set_context("response", {
                "status_code": response.status_code,
                "duration": duration,
            })
        
        # Log slow requests
        if duration > 5.0:  # 5 seconds
            logger.warning(
                f"Slow request detected: {endpoint} took {duration:.2f}s",
                extra={
                    "endpoint": endpoint,
                    "duration": duration,
                    "status_code": response.status_code
                }
            )
        
        # Add performance headers
        response.headers["X-Response-Time"] = f"{duration:.4f}s"
        
        return response
        
    except Exception as e:
        duration = time.time() - start_time
        
        # Record error metrics
        endpoint = f"{request.method} {request.url.path}"
        logger.error(
            f"Request error: {endpoint} failed after {duration:.2f}s",
            exc_info=True,
            extra={
                "endpoint": endpoint,
                "duration": duration,
                "error": str(e)
            }
        )
        
        # Re-raise the exception
        raise


class HealthChecker:
    """Application health checking and monitoring."""
    
    @staticmethod
    async def check_database_health() -> Dict[str, Any]:
        """Check database connectivity and performance."""
        try:
            from app.database.connection import get_database
            
            start_time = time.time()
            async with get_database() as db:
                await db.fetch_one("SELECT 1")
            duration = time.time() - start_time
            
            return {
                "status": "healthy",
                "response_time": round(duration, 4),
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error("Database health check failed", exc_info=True)
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
    
    @staticmethod
    async def check_external_services() -> Dict[str, Any]:
        """Check external service connectivity."""
        services = {}
        
        # Check Twitch API
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                async with session.get("https://api.twitch.tv/helix/games?name=Art") as response:
                    duration = time.time() - start_time
                    
                    if response.status == 200:
                        services["twitch_api"] = {
                            "status": "healthy",
                            "response_time": round(duration, 4)
                        }
                    else:
                        services["twitch_api"] = {
                            "status": "degraded",
                            "status_code": response.status,
                            "response_time": round(duration, 4)
                        }
        except Exception as e:
            services["twitch_api"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        # Check Clerk API (if configured)
        if settings.CLERK_SECRET_KEY:
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    headers = {"Authorization": f"Bearer {settings.CLERK_SECRET_KEY}"}
                    start_time = time.time()
                    async with session.get("https://api.clerk.dev/v1/users", headers=headers) as response:
                        duration = time.time() - start_time
                        
                        if response.status in [200, 401]:  # 401 is expected without proper auth
                            services["clerk_api"] = {
                                "status": "healthy",
                                "response_time": round(duration, 4)
                            }
                        else:
                            services["clerk_api"] = {
                                "status": "degraded",
                                "status_code": response.status,
                                "response_time": round(duration, 4)
                            }
            except Exception as e:
                services["clerk_api"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
        
        return services
    
    @staticmethod
    def get_system_metrics() -> Dict[str, Any]:
        """Get system resource metrics."""
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            return {
                "cpu": {
                    "percent": cpu_percent,
                    "count": psutil.cpu_count()
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                }
            }
        except ImportError:
            return {"error": "psutil not available"}
        except Exception as e:
            return {"error": str(e)}


# Initialize monitoring
health_checker = HealthChecker()


def setup_logging():
    """Set up application logging configuration."""
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )
    
    # Configure specific loggers
    loggers = {
        'uvicorn': logging.INFO,
        'uvicorn.error': logging.INFO,
        'uvicorn.access': logging.INFO if settings.ENVIRONMENT == "production" else logging.DEBUG,
        'sqlalchemy.engine': logging.WARNING,  # Reduce SQL query logging
        'app': log_level,
    }
    
    for logger_name, level in loggers.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)
    
    logger.info(f"Logging configured with level: {settings.LOG_LEVEL}")


# Initialize monitoring components
if __name__ != "__main__":
    setup_logging()
    init_sentry()