"""
Backend Performance Tests
Tests API response times, database query performance, concurrent users, and resource usage.
"""

import asyncio
import time
import statistics
import psutil
import pytest
import aiohttp
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any
from sqlalchemy import text
from app.database.connection import get_database
from app.models import <PERSON>er, UserProfile, StreamerAssignment
from app.services.streamer_service import StreamerService
from app.services.database_service import DatabaseService


class PerformanceMetrics:
    """Utility class for collecting and analyzing performance metrics."""
    
    def __init__(self):
        self.response_times: List[float] = []
        self.memory_usage: List[float] = []
        self.cpu_usage: List[float] = []
        
    def add_response_time(self, response_time: float):
        self.response_times.append(response_time)
        
    def add_memory_usage(self, memory_mb: float):
        self.memory_usage.append(memory_mb)
        
    def add_cpu_usage(self, cpu_percent: float):
        self.cpu_usage.append(cpu_percent)
        
    def get_response_time_stats(self) -> Dict[str, float]:
        if not self.response_times:
            return {}
        return {
            'mean': statistics.mean(self.response_times),
            'median': statistics.median(self.response_times),
            'p95': self._percentile(self.response_times, 95),
            'p99': self._percentile(self.response_times, 99),
            'min': min(self.response_times),
            'max': max(self.response_times),
        }
        
    def get_memory_stats(self) -> Dict[str, float]:
        if not self.memory_usage:
            return {}
        return {
            'mean': statistics.mean(self.memory_usage),
            'max': max(self.memory_usage),
            'min': min(self.memory_usage),
        }
        
    def get_cpu_stats(self) -> Dict[str, float]:
        if not self.cpu_usage:
            return {}
        return {
            'mean': statistics.mean(self.cpu_usage),
            'max': max(self.cpu_usage),
            'min': min(self.cpu_usage),
        }
        
    @staticmethod
    def _percentile(data: List[float], percentile: int) -> float:
        size = len(data)
        return sorted(data)[int(size * percentile / 100)]


class APIPerformanceTester:
    """Test API endpoint performance under various conditions."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.metrics = PerformanceMetrics()
        
    async def test_endpoint_response_time(self, endpoint: str, method: str = "GET", 
                                        data: Dict = None, headers: Dict = None) -> float:
        """Test individual endpoint response time."""
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            
            try:
                if method.upper() == "GET":
                    async with session.get(f"{self.base_url}{endpoint}", 
                                         headers=headers) as response:
                        await response.text()
                elif method.upper() == "POST":
                    async with session.post(f"{self.base_url}{endpoint}", 
                                          json=data, headers=headers) as response:
                        await response.text()
                elif method.upper() == "PATCH":
                    async with session.patch(f"{self.base_url}{endpoint}", 
                                           json=data, headers=headers) as response:
                        await response.text()
                        
                response_time = time.time() - start_time
                self.metrics.add_response_time(response_time)
                return response_time
                
            except Exception as e:
                print(f"Error testing endpoint {endpoint}: {e}")
                return -1
                
    async def load_test_endpoint(self, endpoint: str, concurrent_requests: int = 10, 
                               total_requests: int = 100) -> Dict[str, Any]:
        """Load test an endpoint with concurrent requests."""
        semaphore = asyncio.Semaphore(concurrent_requests)
        
        async def make_request():
            async with semaphore:
                return await self.test_endpoint_response_time(endpoint)
                
        start_time = time.time()
        tasks = [make_request() for _ in range(total_requests)]
        response_times = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        successful_requests = [rt for rt in response_times if rt > 0]
        
        return {
            'total_requests': total_requests,
            'successful_requests': len(successful_requests),
            'failed_requests': total_requests - len(successful_requests),
            'total_time': total_time,
            'requests_per_second': len(successful_requests) / total_time,
            'response_times': successful_requests,
        }


class DatabasePerformanceTester:
    """Test database query performance and optimization."""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.db_service = DatabaseService()
        
    async def test_query_performance(self, query: str, params: Dict = None) -> float:
        """Test individual query performance."""
        start_time = time.time()
        
        try:
            async with get_database() as db:
                result = await db.fetch_all(text(query), params or {})
                query_time = time.time() - start_time
                self.metrics.add_response_time(query_time)
                return query_time
        except Exception as e:
            print(f"Error executing query: {e}")
            return -1
            
    async def test_streamer_queries(self) -> Dict[str, float]:
        """Test common streamer-related queries."""
        queries = {
            'get_all_streamers': "SELECT * FROM streamers LIMIT 100",
            'get_live_streamers': "SELECT * FROM streamers WHERE is_live = true LIMIT 50",
            'get_streamers_by_follower_count': """
                SELECT * FROM streamers 
                WHERE follower_count BETWEEN 0 AND 50 
                ORDER BY follower_count DESC LIMIT 50
            """,
            'get_streamers_with_assignments': """
                SELECT s.*, sa.status 
                FROM streamers s 
                LEFT JOIN streamer_assignments sa ON s.twitch_user_id = sa.streamer_twitch_id 
                LIMIT 50
            """,
            'search_streamers': """
                SELECT * FROM streamers 
                WHERE display_name ILIKE '%art%' OR username ILIKE '%art%' 
                LIMIT 20
            """,
        }
        
        results = {}
        for query_name, query in queries.items():
            execution_time = await self.test_query_performance(query)
            results[query_name] = execution_time
            
        return results
        
    async def test_assignment_queries(self) -> Dict[str, float]:
        """Test assignment-related queries."""
        queries = {
            'get_user_assignments': """
                SELECT * FROM streamer_assignments 
                WHERE user_id = 'test_user' 
                ORDER BY created_at DESC LIMIT 50
            """,
            'get_pending_assignments': """
                SELECT * FROM streamer_assignments 
                WHERE status = 'pending' 
                LIMIT 100
            """,
            'count_daily_requests': """
                SELECT COUNT(*) as request_count 
                FROM streamer_assignments 
                WHERE user_id = 'test_user' 
                AND created_at >= CURRENT_DATE
            """,
            'get_assignment_statistics': """
                SELECT status, COUNT(*) as count 
                FROM streamer_assignments 
                GROUP BY status
            """,
        }
        
        results = {}
        for query_name, query in queries.items():
            execution_time = await self.test_query_performance(query)
            results[query_name] = execution_time
            
        return results
        
    async def test_index_performance(self) -> Dict[str, Any]:
        """Test database index effectiveness."""
        # Test queries with and without indexes
        test_cases = [
            {
                'name': 'streamer_follower_count_index',
                'query': 'SELECT * FROM streamers WHERE follower_count BETWEEN 0 AND 50',
                'expected_max_time': 0.1,  # 100ms
            },
            {
                'name': 'streamer_live_status_index', 
                'query': 'SELECT * FROM streamers WHERE is_live = true',
                'expected_max_time': 0.05,  # 50ms
            },
            {
                'name': 'assignment_user_index',
                'query': "SELECT * FROM streamer_assignments WHERE user_id = 'test_user'",
                'expected_max_time': 0.05,  # 50ms
            },
            {
                'name': 'assignment_created_at_index',
                'query': 'SELECT * FROM streamer_assignments WHERE created_at >= CURRENT_DATE',
                'expected_max_time': 0.1,  # 100ms
            },
        ]
        
        results = {}
        for test_case in test_cases:
            execution_time = await self.test_query_performance(test_case['query'])
            results[test_case['name']] = {
                'execution_time': execution_time,
                'expected_max_time': test_case['expected_max_time'],
                'passes_benchmark': execution_time <= test_case['expected_max_time'],
            }
            
        return results


class ResourceMonitor:
    """Monitor system resource usage during performance tests."""
    
    def __init__(self):
        self.monitoring = False
        self.metrics = PerformanceMetrics()
        
    async def start_monitoring(self, interval: float = 1.0):
        """Start monitoring system resources."""
        self.monitoring = True
        
        while self.monitoring:
            # Get memory usage
            memory = psutil.virtual_memory()
            self.metrics.add_memory_usage(memory.used / 1024 / 1024)  # MB
            
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            self.metrics.add_cpu_usage(cpu_percent)
            
            await asyncio.sleep(interval)
            
    def stop_monitoring(self):
        """Stop monitoring system resources."""
        self.monitoring = False
        
    def get_resource_stats(self) -> Dict[str, Dict[str, float]]:
        """Get resource usage statistics."""
        return {
            'memory': self.metrics.get_memory_stats(),
            'cpu': self.metrics.get_cpu_stats(),
        }


# Performance Test Cases

@pytest.mark.asyncio
async def test_api_response_times():
    """Test API endpoint response times meet performance requirements."""
    tester = APIPerformanceTester()
    
    # Test critical endpoints
    endpoints = [
        '/health',
        '/api/v1/streamers',
        '/api/v1/streamers/available',
        '/api/v1/user/status',
        '/api/v1/assignments',
    ]
    
    for endpoint in endpoints:
        response_time = await tester.test_endpoint_response_time(endpoint)
        
        # Response time should be under 500ms for all endpoints
        assert response_time < 0.5, f"Endpoint {endpoint} took {response_time:.3f}s (>500ms)"
        assert response_time > 0, f"Endpoint {endpoint} failed to respond"


@pytest.mark.asyncio
async def test_database_query_performance():
    """Test database queries meet performance requirements."""
    tester = DatabasePerformanceTester()
    
    # Test streamer queries
    streamer_results = await tester.test_streamer_queries()
    for query_name, execution_time in streamer_results.items():
        assert execution_time < 0.2, f"Query {query_name} took {execution_time:.3f}s (>200ms)"
        assert execution_time > 0, f"Query {query_name} failed to execute"
        
    # Test assignment queries
    assignment_results = await tester.test_assignment_queries()
    for query_name, execution_time in assignment_results.items():
        assert execution_time < 0.15, f"Query {query_name} took {execution_time:.3f}s (>150ms)"
        assert execution_time > 0, f"Query {query_name} failed to execute"


@pytest.mark.asyncio
async def test_concurrent_user_handling():
    """Test system performance under concurrent user load."""
    tester = APIPerformanceTester()
    
    # Test with increasing concurrent load
    load_scenarios = [
        {'concurrent': 5, 'total': 50},
        {'concurrent': 10, 'total': 100},
        {'concurrent': 20, 'total': 200},
        {'concurrent': 50, 'total': 500},
    ]
    
    for scenario in load_scenarios:
        results = await tester.load_test_endpoint(
            '/api/v1/streamers',
            concurrent_requests=scenario['concurrent'],
            total_requests=scenario['total']
        )
        
        # Success rate should be >95%
        success_rate = results['successful_requests'] / results['total_requests']
        assert success_rate > 0.95, f"Success rate {success_rate:.2%} too low for {scenario}"
        
        # Requests per second should meet minimum threshold
        min_rps = max(10, scenario['concurrent'] * 0.5)  # At least 50% efficiency
        assert results['requests_per_second'] >= min_rps, \
            f"RPS {results['requests_per_second']:.1f} below threshold {min_rps}"


@pytest.mark.asyncio
async def test_memory_usage():
    """Test memory usage remains within acceptable limits."""
    monitor = ResourceMonitor()
    tester = APIPerformanceTester()
    
    # Start monitoring
    monitor_task = asyncio.create_task(monitor.start_monitoring(0.5))
    
    try:
        # Generate load
        await tester.load_test_endpoint('/api/v1/streamers', 
                                      concurrent_requests=20, 
                                      total_requests=200)
        
        # Wait a bit for memory measurements
        await asyncio.sleep(2)
        
    finally:
        monitor.stop_monitoring()
        await monitor_task
        
    stats = monitor.get_resource_stats()
    
    # Memory usage should not exceed 1GB during testing
    max_memory_mb = stats['memory'].get('max', 0)
    assert max_memory_mb < 1024, f"Memory usage {max_memory_mb:.1f}MB exceeds 1GB limit"


@pytest.mark.asyncio
async def test_cpu_utilization():
    """Test CPU utilization remains reasonable under load."""
    monitor = ResourceMonitor()
    tester = APIPerformanceTester()
    
    # Start monitoring
    monitor_task = asyncio.create_task(monitor.start_monitoring(0.5))
    
    try:
        # Generate CPU-intensive load
        tasks = []
        for _ in range(3):  # Multiple concurrent load tests
            task = tester.load_test_endpoint('/api/v1/streamers/available',
                                           concurrent_requests=15,
                                           total_requests=150)
            tasks.append(task)
            
        await asyncio.gather(*tasks)
        
        # Wait for measurements
        await asyncio.sleep(2)
        
    finally:
        monitor.stop_monitoring()
        await monitor_task
        
    stats = monitor.get_resource_stats()
    
    # CPU usage should not consistently exceed 80%
    mean_cpu = stats['cpu'].get('mean', 0)
    assert mean_cpu < 80, f"Mean CPU usage {mean_cpu:.1f}% exceeds 80% threshold"


@pytest.mark.asyncio
async def test_database_index_effectiveness():
    """Test that database indexes are effective for query performance."""
    tester = DatabasePerformanceTester()
    
    index_results = await tester.test_index_performance()
    
    for index_name, result in index_results.items():
        assert result['passes_benchmark'], \
            f"Index {index_name} performance {result['execution_time']:.3f}s " \
            f"exceeds benchmark {result['expected_max_time']:.3f}s"


@pytest.mark.asyncio
async def test_service_layer_performance():
    """Test service layer performance for common operations."""
    streamer_service = StreamerService()
    
    # Test service methods with timing
    start_time = time.time()
    streamers = await streamer_service.get_available_streamers(limit=50)
    get_streamers_time = time.time() - start_time
    
    start_time = time.time()
    live_streamers = await streamer_service.get_live_streamers(limit=20)
    get_live_time = time.time() - start_time
    
    start_time = time.time()
    filtered_streamers = await streamer_service.filter_streamers({
        'min_followers': 0,
        'max_followers': 50,
        'language': 'en',
        'is_live': True
    })
    filter_time = time.time() - start_time
    
    # Service methods should be fast
    assert get_streamers_time < 0.3, f"get_available_streamers took {get_streamers_time:.3f}s"
    assert get_live_time < 0.2, f"get_live_streamers took {get_live_time:.3f}s"
    assert filter_time < 0.4, f"filter_streamers took {filter_time:.3f}s"


@pytest.mark.asyncio
async def test_pagination_performance():
    """Test pagination performance with large datasets."""
    tester = APIPerformanceTester()
    
    # Test different page sizes
    page_sizes = [10, 25, 50, 100]
    
    for page_size in page_sizes:
        endpoint = f'/api/v1/streamers?limit={page_size}&page=1'
        response_time = await tester.test_endpoint_response_time(endpoint)
        
        # Larger pages should still be reasonably fast
        max_time = 0.3 + (page_size / 1000)  # Allow slight increase for larger pages
        assert response_time < max_time, \
            f"Pagination with {page_size} items took {response_time:.3f}s"


@pytest.mark.asyncio
async def test_error_handling_performance():
    """Test that error handling doesn't significantly impact performance."""
    tester = APIPerformanceTester()
    
    # Test error scenarios
    error_endpoints = [
        '/api/v1/streamers/nonexistent',  # 404
        '/api/v1/invalid-endpoint',       # 404
    ]
    
    for endpoint in error_endpoints:
        response_time = await tester.test_endpoint_response_time(endpoint)
        
        # Error responses should be fast
        assert response_time < 0.1, f"Error endpoint {endpoint} took {response_time:.3f}s"


if __name__ == "__main__":
    # Run performance tests
    asyncio.run(test_api_response_times())
    asyncio.run(test_database_query_performance())
    asyncio.run(test_concurrent_user_handling())
    print("All performance tests completed!")