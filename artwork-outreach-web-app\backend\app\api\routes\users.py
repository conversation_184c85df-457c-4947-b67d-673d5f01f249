from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.database.connection import get_db
from app.database.models import UserProfile
from app.schemas.user import UserProfileResponse, UserStatusResponse, UpdateUserProfileRequest
from app.api.dependencies import get_current_user
from app.services.user_service import UserService

router = APIRouter()

@router.get(
    "/profile",
    response_model=UserProfileResponse,
    responses={
        401: {"description": "User not authenticated"},
        404: {"description": "User not found"},
    }
)
def get_user_profile(current_user: UserProfile = Depends(get_current_user)):
    """
    Get the profile of the current authenticated user.

    This endpoint returns the detailed profile of the user who is currently authenticated.
    """
    return current_user

@router.patch(
    "/profile",
    response_model=UserProfileResponse,
    responses={
        401: {"description": "User not authenticated"},
        404: {"description": "User not found"},
        422: {"description": "Validation error"},
    },
)
async def update_user_profile(
    profile_update: UpdateUserProfileRequest,
    db: AsyncSession = Depends(get_db),
    current_user: UserProfile = Depends(get_current_user),
):
    """
    Update the profile of the current authenticated user.

    This endpoint allows updating the user's full name and email.
    Only the provided fields will be updated.
    """
    user_service = UserService(db)
    updated_user = await user_service.update_user_profile(current_user.id, profile_update)
    return updated_user

@router.get(
    "/status",
    response_model=UserStatusResponse,
    responses={
        401: {"description": "User not authenticated"},
        404: {"description": "User not found"},
    }
)
def get_user_status(current_user: UserProfile = Depends(get_current_user)):
    """
    Get the API request status of the current authenticated user.

    This endpoint provides information about the user's API usage,
    including the number of requests made and when the quota will reset.
    """
    return UserStatusResponse.from_user(current_user)