[pytest]
minversion = 7.0
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -p no:warnings --strict-markers -ra
asyncio_mode = auto
log_cli = true
log_cli_level = INFO
env_files =
    .env.test

[coverage:run]
source = app
omit =
    */__init__.py
    */main.py
    */database/connection.py
    */database/migrations/*
    */core/exceptions.py

[coverage:report]
fail_under = 80
show_missing = true
skip_covered = true