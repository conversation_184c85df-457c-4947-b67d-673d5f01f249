#!/usr/bin/env python3
"""
Load data directly to Supabase using REST API (bypasses DNS issues)
"""

import sqlite3
import requests
import json
from datetime import datetime

# Supabase configuration
SUPABASE_URL = "https://wejiqonfxofwbiubqmpo.supabase.co"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indlamlxb25meG9md2JpdWJxbXBvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTM3MjkyMSwiZXhwIjoyMDY2OTQ4OTIxfQ.YNXeWA1bj9oHLgrfPqp3le8ajlikYatPOxGcB-iseks"

def test_supabase_connection():
    """Test if we can connect to Supabase REST API"""
    print("🔍 Testing Supabase connection...")
    
    headers = {
        "apikey": SUPABASE_SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        # Try to access the streamers table
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/streamers?limit=1",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Supabase connection successful!")
            return True
        else:
            print(f"⚠️  Supabase connection failed: {response.status_code}")
            print(f"    Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Supabase connection error: {e}")
        return False

def load_sqlite_to_supabase():
    """Load data from local SQLite to Supabase"""
    print("📤 Loading data from SQLite to Supabase...")
    
    # Connect to local SQLite database
    db_path = "test_streamers.db"
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all streamers
        cursor.execute("""
            SELECT twitch_user_id, username, display_name, follower_count, 
                   current_game, stream_title, thumbnail_url, language
            FROM streamers
        """)
        streamers = cursor.fetchall()
        
        print(f"📊 Found {len(streamers)} streamers in local database")
        
        # Supabase headers
        headers = {
            "apikey": SUPABASE_SERVICE_ROLE_KEY,
            "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        success_count = 0
        error_count = 0
        
        for i, (twitch_user_id, username, display_name, follower_count, 
                current_game, stream_title, thumbnail_url, language) in enumerate(streamers, 1):
            
            try:
                # Prepare data for Supabase
                streamer_data = {
                    "twitch_user_id": twitch_user_id,
                    "username": username,
                    "display_name": display_name,
                    "follower_count": follower_count,
                    "is_live": True,
                    "current_game": current_game,
                    "stream_title": stream_title,
                    "thumbnail_url": thumbnail_url,
                    "language": language,
                    "last_seen_live_at": datetime.utcnow().isoformat()
                }
                
                # Insert/update streamer via Supabase REST API
                response = requests.post(
                    f"{SUPABASE_URL}/rest/v1/streamers",
                    headers=headers,
                    json=streamer_data,
                    timeout=10
                )
                
                if response.status_code in [200, 201]:
                    success_count += 1
                    print(f"✅ {i:2d}/{len(streamers)} - {username}")
                elif response.status_code == 409:
                    # Conflict - try to update instead
                    update_headers = {**headers, "Prefer": "return=minimal"}
                    update_response = requests.patch(
                        f"{SUPABASE_URL}/rest/v1/streamers?twitch_user_id=eq.{twitch_user_id}",
                        headers=update_headers,
                        json=streamer_data,
                        timeout=10
                    )
                    if update_response.status_code in [200, 204]:
                        success_count += 1
                        print(f"🔄 {i:2d}/{len(streamers)} - {username} (updated)")
                    else:
                        error_count += 1
                        print(f"⚠️  {i:2d}/{len(streamers)} - {username} - Update failed: {update_response.status_code}")
                else:
                    error_count += 1
                    error_text = response.text[:100] if response.text else "No details"
                    print(f"⚠️  {i:2d}/{len(streamers)} - {username} - Error {response.status_code}: {error_text}")
                    
            except Exception as e:
                error_count += 1
                print(f"❌ {i:2d}/{len(streamers)} - {username} - Exception: {e}")
        
        conn.close()
        
        print(f"\n📈 Upload Summary:")
        print(f"   ✅ Successful: {success_count}")
        print(f"   ❌ Errors: {error_count}")
        print(f"   📊 Total: {len(streamers)}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ SQLite error: {e}")
        return False

def verify_supabase_data():
    """Verify data was loaded correctly into Supabase"""
    print("\n🔍 Verifying data in Supabase...")
    
    headers = {
        "apikey": SUPABASE_SERVICE_ROLE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        # Get total count
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/streamers?select=count",
            headers={**headers, "Prefer": "count=exact"},
            timeout=10
        )
        
        if response.status_code == 200:
            count = response.headers.get('Content-Range', '0-0/0').split('/')[-1]
            print(f"📊 Total streamers in Supabase: {count}")
            
            # Get sample data
            sample_response = requests.get(
                f"{SUPABASE_URL}/rest/v1/streamers?select=username,display_name,follower_count,current_game&limit=5",
                headers=headers,
                timeout=10
            )
            
            if sample_response.status_code == 200:
                samples = sample_response.json()
                print(f"\n📋 Sample Data from Supabase:")
                for streamer in samples:
                    print(f"   {streamer.get('username')} ({streamer.get('display_name')}) - {streamer.get('follower_count')} followers - {streamer.get('current_game')}")
                    
            return True
        else:
            print(f"⚠️  Verification failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Supabase Direct Data Loader\n")
    
    # Test Supabase connection
    if not test_supabase_connection():
        print("❌ Cannot connect to Supabase!")
        print("💡 Check your internet connection and Supabase credentials")
        return
    
    # Load data from SQLite to Supabase
    if load_sqlite_to_supabase():
        # Verify the data
        if verify_supabase_data():
            print("\n🎉 Data migration completed successfully!")
            print("\n📋 Summary:")
            print("   ✅ SQLite data migrated to Supabase")
            print("   ✅ Real streamers now in production database")
            print("   ✅ Backend can now serve real data")
            print("\n💡 Next steps:")
            print("   1. Restart your backend (uvicorn app.main:app --reload)")
            print("   2. Test API endpoints: http://127.0.0.1:8000/docs")
            print("   3. Access frontend: http://localhost:3000")
            print("   4. Click 'Get New Streamers' to see real data!")
        else:
            print("\n⚠️  Data migration completed but verification failed")
    else:
        print("\n❌ Data migration failed!")

if __name__ == "__main__":
    main()