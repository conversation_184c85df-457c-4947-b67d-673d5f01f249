{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-icss-parser.ts"], "names": ["plugin", "options", "postcssPlugin", "OnceExit", "root", "importReplacements", "Object", "create", "icssImports", "icssExports", "extractICSS", "imports", "Map", "tasks", "url", "tokens", "keys", "length", "normalizedUrl", "prefix", "queryParts", "split", "pop", "join", "request", "requestify", "normalizeUrl", "rootContext", "doResolve", "resolver", "context", "resolvedUrl", "resolveRequests", "Set", "push", "results", "Promise", "all", "index", "item", "newUrl", "importKey", "importName", "get", "size", "set", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icss", "api", "dedupe", "replacementIndex", "token", "entries", "replacement<PERSON>ame", "localName", "replacements", "replaceSymbols", "name", "value", "replaceValueSymbols", "exports", "postcss"], "mappings": ";;;;+BAiHA;;;eAAA;;;2BA7GO;uBAEmD;AAE1D,MAAMA,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACf,MAAMC,UAASC,IAAS;YACtB,MAAMC,qBAAqBC,OAAOC,MAAM,CAAC;YACzC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAE,GAAGC,IAAAA,sBAAW,EAACN;YACjD,MAAMO,UAAU,IAAIC;YACpB,MAAMC,QAAQ,EAAE;YAEhB,wCAAwC;YACxC,IAAK,MAAMC,OAAON,YAAa;gBAC7B,MAAMO,SAASP,WAAW,CAACM,IAAI;gBAE/B,IAAIR,OAAOU,IAAI,CAACD,QAAQE,MAAM,KAAK,GAAG;oBAEpC;gBACF;gBAEA,IAAIC,gBAAgBJ;gBACpB,IAAIK,SAAS;gBAEb,MAAMC,aAAaF,cAAcG,KAAK,CAAC;gBAEvC,IAAID,WAAWH,MAAM,GAAG,GAAG;oBACzBC,gBAAgBE,WAAWE,GAAG;oBAC9BH,SAASC,WAAWG,IAAI,CAAC;gBAC3B;gBAEA,MAAMC,UAAUC,IAAAA,iBAAU,EACxBC,IAAAA,mBAAY,EAACR,eAAe,OAC5BjB,QAAQ0B,WAAW;gBAErB,MAAMC,YAAY;oBAChB,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE,GAAG7B;oBAC9B,MAAM8B,cAAc,MAAMC,IAAAA,sBAAe,EAACH,UAAUC,SAAS;2BACxD,IAAIG,IAAI;4BAACf;4BAAeM;yBAAQ;qBACpC;oBAED,IAAI,CAACO,aAAa;wBAChB;oBACF;oBAEA,6CAA6C;oBAC7C,OAAO;wBAAEjB,KAAKiB;wBAAaZ;wBAAQJ;oBAAO;gBAC5C;gBAEAF,MAAMqB,IAAI,CAACN;YACb;YAEA,MAAMO,UAAU,MAAMC,QAAQC,GAAG,CAACxB;YAElC,IAAK,IAAIyB,QAAQ,GAAGA,SAASH,QAAQlB,MAAM,GAAG,GAAGqB,QAAS;gBACxD,MAAMC,OAAOJ,OAAO,CAACG,MAAM;gBAE3B,IAAI,CAACC,MAAM;oBAET;gBACF;gBAEA,MAAMC,SAASD,KAAKpB,MAAM,GAAG,CAAC,EAAEoB,KAAKpB,MAAM,CAAC,CAAC,EAAEoB,KAAKzB,GAAG,CAAC,CAAC,GAAGyB,KAAKzB,GAAG;gBACpE,MAAM2B,YAAYD;gBAClB,IAAIE,aAAa/B,QAAQgC,GAAG,CAACF;gBAE7B,IAAI,CAACC,YAAY;oBACfA,aAAa,CAAC,0BAA0B,EAAE/B,QAAQiC,IAAI,CAAC,GAAG,CAAC;oBAC3DjC,QAAQkC,GAAG,CAACJ,WAAWC;oBAEvBzC,QAAQU,OAAO,CAACuB,IAAI,CAAC;wBACnBY,MAAM;wBACNJ;wBACA5B,KAAKb,QAAQ8C,UAAU,CAACP;wBACxBQ,MAAM;wBACNV;oBACF;oBAEArC,QAAQgD,GAAG,CAACf,IAAI,CAAC;wBAAEQ;wBAAYQ,QAAQ;wBAAMZ;oBAAM;gBACrD;gBAEA,KAAK,MAAM,CAACa,kBAAkBC,MAAM,IAAI9C,OAAOU,IAAI,CACjDuB,KAAKxB,MAAM,EACXsC,OAAO,GAAI;oBACX,MAAMC,kBAAkB,CAAC,0BAA0B,EAAEhB,MAAM,aAAa,EAAEa,iBAAiB,GAAG,CAAC;oBAC/F,MAAMI,YAAYhB,KAAKxB,MAAM,CAACqC,MAAM;oBAEpC/C,kBAAkB,CAAC+C,MAAM,GAAGE;oBAE5BrD,QAAQuD,YAAY,CAACtB,IAAI,CAAC;wBAAEoB;wBAAiBZ;wBAAYa;oBAAU;gBACrE;YACF;YAEA,IAAIjD,OAAOU,IAAI,CAACX,oBAAoBY,MAAM,GAAG,GAAG;gBAC9CwC,IAAAA,yBAAc,EAACrD,MAAMC;YACvB;YAEA,KAAK,MAAMqD,QAAQpD,OAAOU,IAAI,CAACP,aAAc;gBAC3C,MAAMkD,QAAQC,IAAAA,8BAAmB,EAACnD,WAAW,CAACiD,KAAK,EAAErD;gBAErDJ,QAAQ4D,OAAO,CAAC3B,IAAI,CAAC;oBAAEwB;oBAAMC;gBAAM;YACrC;QACF;IACF;AACF;AAEA3D,OAAO8D,OAAO,GAAG;MAEjB,WAAe9D"}