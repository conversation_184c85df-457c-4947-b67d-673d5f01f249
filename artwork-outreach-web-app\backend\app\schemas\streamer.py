from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID

from pydantic import BaseModel, validator, root_validator

from app.utils.enrichment import (
    calculate_time_since_last_live,
    format_follower_count,
)
from app.utils.validators import (
    validate_follower_count,
    validate_language_code,
    validate_twitch_user_id,
    validate_url,
)
# Removed circular import - will handle assignments separately

class StreamerCreate(BaseModel):
    twitch_user_id: str
    username: str
    display_name: Optional[str] = None
    follower_count: int
    is_live: bool = False
    current_game: Optional[str] = None
    stream_title: Optional[str] = None
    thumbnail_url: Optional[str] = None
    profile_image_url: Optional[str] = None
    language: str
    last_seen_live_at: Optional[datetime] = None


class StreamerResponse(BaseModel):
    id: UUID
    twitch_user_id: str
    username: str
    display_name: str | None
    follower_count: int
    is_live: bool
    current_game: str | None
    stream_title: str | None
    thumbnail_url: str | None
    profile_image_url: str | None
    language: str
    last_seen_live_at: datetime | None
    created_at: datetime
    updated_at: datetime

    # Computed fields
    time_since_live: Optional[str] = None
    formatted_follower_count: Optional[str] = None
    assignment_status_for_current_user: Optional[str] = None

    @validator("twitch_user_id")
    def validate_twitch_user_id_field(cls, v):
        if not validate_twitch_user_id(v):
            raise ValueError("Invalid Twitch User ID")
        return v

    @validator("follower_count")
    def validate_follower_count_field(cls, v):
        if not validate_follower_count(v):
            raise ValueError("Follower count must be a non-negative integer")
        return v

    @validator("profile_image_url")
    def validate_profile_image_url_field(cls, v):
        if v and not validate_url(v):
            raise ValueError("Invalid Profile Image URL")
        return v

    @validator("language")
    def validate_language_field(cls, v):
        if not validate_language_code(v):
            raise ValueError("Invalid language code")
        return v
        
    @root_validator(pre=False, skip_on_failure=True)
    def set_computed_fields(cls, values):
        values["time_since_live"] = calculate_time_since_last_live(values.get("last_seen_live_at"))
        values["formatted_follower_count"] = format_follower_count(values.get("follower_count", 0))
        return values

    model_config = {"from_attributes": True}


# Alias for backward compatibility
StreamerRead = StreamerResponse

class StreamerDetailResponse(StreamerResponse):
    assignments: list[dict] = []  # Will be populated dynamically


class StreamerListResponse(BaseModel):
    streamers: list[StreamerResponse]
    next_cursor: Optional[str] = None
    previous_cursor: Optional[str] = None
    filters_applied: Dict[str, Any]

class StreamerStatsResponse(BaseModel):
    total_streamers: int
    live_streamers: int
    available_streamers: int
    average_followers: float